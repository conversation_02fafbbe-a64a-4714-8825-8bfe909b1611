version: '3.8'
services:
  app:
    ports:
      - 3000:3000
    volumes:
      - ../src:/app/src
    depends_on:
      - db
      - redis
      - mongo
    restart: always

  db:
    image: postgres
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'postgres']
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis/redis-stack:latest
    restart: always
    ports:
      - 6379:6379
      - 13333:8001
    volumes:
      - ./cache-data-dev:/data
    environment:
      REDIS_PASSWORD: admin

  mongo:
    image: mongo
    restart: always
    ports:
      - 27017:27017
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: toor
