import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { setupSwagger } from './config/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // Enable validation pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: false,
    }),
  );
  // Enable CORS
  app.enableCors({
    origin: '*',
    methods: '*',
    credentials: true,
    maxAge: 86400,
  });

  // Setup Swagger
  setupSwagger(app);

  // Start server
  const port = process.env.PORT || 3000;
  await app.listen(port);
  console.log(
    `Application is running on PORT: ${port} in ${process.env.NODE_ENV} environment`,
  );
}

bootstrap();
