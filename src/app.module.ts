import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { configSchemaValidation } from './config.schema';
import { getDataSourceOptions } from './db/data_source';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './modules/auth/guards/jwt.guard';
import { AppConfig } from './config/app.config';
import { AuthMiddleware } from '@modules/auth/middleware/auth.middleware';
import { allModules } from './modules/all-modules';
import { RedisModule } from '@nestjs-modules/ioredis';
import { MongooseModule } from '@nestjs/mongoose';
import { PusherModule } from 'nestjs-pusher/dist/pusher.module';
import { LogService } from '@modules/audit/general_log/mongo_log.service';
import { initializeLogger } from '@modules/audit/general_log/utils/logger.util';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.STAGE || 'development'}`,
      validationSchema: configSchemaValidation,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const appConfig = new AppConfig(configService);
        return getDataSourceOptions(appConfig);
      },
    }),
    PusherModule.forRoot(
      {
        appId: '1857198',
        key: 'df850f7f1fc7589fa5b1',
        secret: 'fb9307ea92c0b9cdcc6a',
        cluster: 'ap2',
        useTLS: true,
      },
      {
        limit: 100000,
        enabled: true,
      },
      true,
    ),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'single',
        url: configService.get('SSO_REDIS'),
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get('MONGO_URI'),
        dbName: configService.get('MONGO_DB'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      connectionName: 'logs',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get('MONGO_URI'),
        dbName: configService.get('MONGO_DB_LOGS'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
      inject: [ConfigService],
    }),
    ...allModules,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    AppConfig,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
  exports: [AppConfig],
})
export class AppModule {
  constructor(private readonly logService: LogService) {
    initializeLogger(this.logService);
  }
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .exclude(
        { path: 'auth/login', method: RequestMethod.POST },
        { path: 'auth/signup', method: RequestMethod.POST },
        { path: 'auth/refresh', method: RequestMethod.POST },
        { path: 'auth/google', method: RequestMethod.ALL },
        { path: 'auth/google-login', method: RequestMethod.POST },
      )
      .forRoutes('*');
  }
}
