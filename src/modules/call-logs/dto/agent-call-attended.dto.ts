import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsDate,
  Min,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CallAttendStatus, CallType } from '../enums/call-log.enum';

export class CallAttendedAgentDto {
  @ApiProperty({
    description: 'ID of the call that was attended',
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  id: number;

  @ApiProperty({
    description: 'Timestamp when call was answered',
    required: false,
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  user_answer_stamp?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  customer_answer_stamp?: Date;

  @ApiProperty({
    description: 'Ring time in seconds',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  ring_time?: number;

  @ApiProperty({
    description: 'Whether the call is from a mobile device',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isMobile?: boolean;

  @ApiProperty({
    description: 'Call attendance status',
    required: false,
    enum: CallAttendStatus,
  })
  @IsOptional()
  @IsEnum(CallAttendStatus)
  call_attend_status?: CallAttendStatus;

  @ApiProperty({
    description: 'Call type',
    required: false,
    enum: CallType,
  })
  @IsOptional()
  @IsEnum(CallType)
  call_type?: CallType;
}
