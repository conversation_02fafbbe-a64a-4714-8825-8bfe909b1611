import {
  IsS<PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>ber,
  IsDate,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CallAttendStatus, CallType } from '../enums/call-log.enum';

/**
 * Data transfer object for call hangup/termination
 */
export class CallHangupDto {
  @ApiProperty({ description: 'Unique ID of the call record' })
  @IsNumber()
  id: number;

  @ApiPropertyOptional({ description: 'Reason for the call hangup' })
  @IsOptional()
  @IsString()
  hangup_cause?: string;

  @ApiProperty({
    enum: CallAttendStatus,
    description: 'Call attendance status',
  })
  @IsEnum(CallAttendStatus)
  call_attend_status: CallAttendStatus;

  @ApiPropertyOptional({
    description: 'Time customer’s phone rang (in milliseconds)',
  })
  @IsOptional()
  @IsNumber()
  customer_ring_time?: number;

  @ApiPropertyOptional({
    description: 'Time user’s phone rang (in milliseconds)',
  })
  @IsOptional()
  @IsNumber()
  user_ring_time?: number;

  @ApiPropertyOptional({
    description: 'Timestamp when the call ended',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  end_stamp?: Date;

  @ApiPropertyOptional({
    description: 'Timestamp when the call started',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  start_stamp?: Date;

  @ApiPropertyOptional({
    description: 'Timestamp when user answered the call',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  user_answer_stamp?: Date;

  @ApiPropertyOptional({ description: 'Duration of the call in seconds' })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiPropertyOptional({ description: 'URL to the recorded call audio' })
  @IsOptional()
  @IsString()
  recording_url?: string;

  @ApiPropertyOptional({
    description: 'Indicates if the call was made from a mobile device',
  })
  @IsOptional()
  @IsBoolean()
  isMobile?: boolean;

  @ApiPropertyOptional({
    description: 'Total ring time for the call (in milliseconds)',
  })
  @IsOptional()
  @IsNumber()
  ring_time?: number;

  @ApiPropertyOptional({ enum: CallType, description: 'Type of the call' })
  @IsOptional()
  @IsEnum(CallType)
  call_type?: CallType;

  @ApiPropertyOptional({
    enum: CallAttendStatus,
    description: 'Call status at hangup',
  })
  @IsOptional()
  @IsEnum(CallAttendStatus)
  call_status?: CallAttendStatus;
}
