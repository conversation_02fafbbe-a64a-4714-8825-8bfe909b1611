// call-log.dto.ts
export class CallLogResponseDto {
  id: number;
  call_id?: number;
  start_stamp?: Date;
  end_stamp?: Date;
  user_answer_stamp?: Date;
  customer_answer_stamp?: Date;
  call_type?: string;
  call_status?: string;
  call_attend_status?: string;
  duration?: number;
  call_recording_url?: string;

  // Simplified phone without circular references
  phone?: {
    id: number;
    phone_number: string;
    country_code: string;
  };

  // Simplified contact without circular references
  contact?: {
    id: number;
    first_name?: string;
    last_name?: string;
    full_name?: string;
  };

  // Other fields you need to expose
}
