import { IsEnum, IsOptional, IsString, IsArray, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { Tab } from '../enums/call-log.enum';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CallLogFilterDto extends PaginationDto {
  @ApiProperty({
    enum: Tab,
    description: 'The tab to filter call logs by',
    default: Tab.Call_Logs,
    example: Tab.missed_calls,
  })
  @IsEnum(Tab)
  tab_id: Tab = Tab.Call_Logs;

  @ApiProperty({
    description: 'Filter calls from this date',
    required: false,
    type: Date,
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  start_date?: Date;

  @ApiProperty({
    description: 'Filter calls until this date',
    required: false,
    type: Date,
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  end_date?: Date;

  @ApiProperty({
    description: 'Field to sort results by',
    required: false,
    example: 'created_at',
  })
  @IsString()
  @IsOptional()
  sort_field?: string;

  @ApiProperty({
    description: 'Sort order direction',
    required: false,
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    example: 'DESC',
  })
  @IsString()
  @IsOptional()
  sort_order?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({
    description: 'Filter by specific user IDs',
    required: false,
    type: [Number],
    example: [1, 2, 3],
  })
  @IsArray()
  @IsOptional()
  users?: number[];
}
