import { Injectable, Logger } from '@nestjs/common';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { User } from '@modules/users/entities/user.entity';
import { CallLog } from '../entities/call-log.entity';

import { PhoneResolverService } from './phone-resolver.service';

import { CallLogService } from './call-log.service';
import { LeadAssignmentService } from '@modules/leads/services/lead-assignment.service';
import { CallStatus, CallType } from '../enums/call-log.enum';
import { mapCallStatusToCallAttendStatus } from '../utils/call-log.util';

@Injectable()
export class CallProcessingService {
  private readonly logger = new Logger(CallProcessingService.name);

  constructor(
    private readonly phoneResolverService: PhoneResolverService,
    private readonly leadAssignmentService: LeadAssignmentService,
    private readonly callLogService: CallLogService,
  ) {}

  /**
   * Process phone existence and intelligently assign leads to call logs
   */
  async processPhoneAndAssignLead(data: any, user: User): Promise<CallLog> {
    this.logger.log(
      `Processing phone and lead assignment for user: ${user.id}`,
    );

    // Check if phone exists
    const existingPhone = await this.phoneResolverService.findPhoneByNumber(
      data?.customer_phone,
    );

    if (!existingPhone) {
      // Phone doesn't exist - create new phone and call log
      return await this.handleNewPhone(data, user);
    }

    // Phone exists - process with lead assignment
    return await this.handleExistingPhone(existingPhone, data, user);
  }

  /**
   * Handle case when phone doesn't exist - create new phone and basic call log
   */
  private async handleNewPhone(data: any, user: User): Promise<CallLog> {
    this.logger.debug('Phone not found, creating new phone and contact');

    const phone = await this.phoneResolverService.createNewPhone(data);
    const callLog = await this.createBasicCallLog(phone, data, user);

    this.logger.log(
      `Created new call log ID: ${callLog.id} with new phone ID: ${phone.id} (untracked)`,
    );

    return callLog;
  }

  /**
   * Handle case when phone exists - update phone and process lead assignment
   */
  private async handleExistingPhone(
    phone: Phone,
    data: any,
    user: User,
  ): Promise<CallLog> {
    this.logger.debug(`Processing existing phone ID: ${phone.id}`);

    // Check if phone has associated contact
    if (!phone.contact_id) {
      // Phone exists but no contact - create basic call log without lead assignment
      this.logger.debug(
        'Phone exists but no contact found - creating untracked call',
      );
      const callLog = await this.createBasicCallLog(phone, data, user);

      this.logger.log(
        `Created call log ID: ${callLog.id} with existing phone ID: ${phone.id} (no contact)`,
      );

      return callLog;
    }

    // Create basic call log
    const callLog = await this.createBasicCallLog(phone, data, user);

    // Process lead assignment
    const assignmentResult =
      await this.leadAssignmentService.assignLeadToCallLog(
        callLog,
        phone,
        user,
      );

    this.logger.log(
      `Call log ID: ${assignmentResult.callLog.id} processed with status: ${assignmentResult.assignmentStatus}`,
    );

    return assignmentResult.callLog;
  }

  /**
   * Create basic call log without lead assignment
   */
  private async createBasicCallLog(
    phone: Phone,
    data: any,
    user: User,
  ): Promise<CallLog> {
    this.logger.debug(`Creating basic call log for phone ID: ${phone.id}`);

    const callLog = new CallLog();
    callLog.phone = phone;
    callLog.spoc = user;
    callLog.call_id = data?.id;
    callLog.start_stamp = data.start_stamp || new Date();
    callLog.user_answer_stamp = data.user_answer_stamp;
    callLog.call_type = data?.callType ?? CallType.INCOMING;
    callLog.call_status = CallStatus.MISSED;
    callLog.call_attend_status = mapCallStatusToCallAttendStatus(
      data.call_status,
    );
    callLog.is_IVR = data?.is_ivr === true;
    callLog.is_multi_vertical_phone = false;
    callLog.metadata = null;

    const savedCallLog = await this.callLogService.updateCallLog(callLog);

    this.logger.debug(`Created basic call log with ID: ${savedCallLog.id}`);
    return savedCallLog;
  }
}
