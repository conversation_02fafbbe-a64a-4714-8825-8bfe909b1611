import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { Contact } from '@modules/contacts/entities/contact.entity';
import { InitiateCallDto } from '../dto/create-call.dto';
import { LeadService } from '@modules/leads/services/lead.service';

@Injectable()
export class PhoneResolverService {
  private readonly logger = new Logger(PhoneResolverService.name);

  constructor(
    @InjectRepository(Phone)
    private readonly phoneRepository: Repository<Phone>,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>,
    private leadService: LeadService,
  ) {}

  /**
   * Main entry point for phone resolution
   * Handles three cases:
   * 1. Direct phone ID lookup
   * 2. Lead-based phone lookup (finds primary phone)
   * 3. Direct phone number lookup/creation
   */
  async resolvePhone(dto: InitiateCallDto): Promise<Phone> {
    try {
      let phone: Phone | null = null;

      if (dto.phone_id) {
        phone = await this.findPhoneById(dto.phone_id);
      } else if (dto.lead_id) {
        phone = await this.findPhoneByLeadId(dto.lead_id);
      } else if (dto.phone_number) {
        phone = await this.findOrCreatePhoneByNumber(
          dto.phone_number,
          dto.country_code,
        );
      }

      if (!phone) {
        throw new HttpException('Phone number not found', HttpStatus.NOT_FOUND);
      }

      return phone;
    } catch (error) {
      Logger.error('Phone resolution failed:', error);
      throw error;
    }
  }

  /**
   * Finds phone by ID with necessary relations
   */
  private async findPhoneById(phoneId: number): Promise<Phone> {
    const phone = await this.phoneRepository.findOne({
      where: { id: phoneId },
      relations: {
        contact: {
          leads: true,
          primary_phone: true,
        },
      },
    });

    if (!phone) {
      throw new HttpException('Phone number not found', HttpStatus.NOT_FOUND);
    }

    return phone;
  }

  /**
   * Finds primary phone for a lead
   * Creates primary phone relationship if not exists
   */
  private async findPhoneByLeadId(leadId: number): Promise<Phone> {
    const lead = await this.leadService.findOne(leadId);
    if (!lead) {
      throw new HttpException('Lead not found', HttpStatus.NOT_FOUND);
    }

    if (!lead.contact) {
      throw new HttpException(
        'Contact not found for lead',
        HttpStatus.NOT_FOUND,
      );
    }

    let phone = lead.contact.primary_phone;

    if (!phone && lead.contact.phones?.length > 0) {
      // Set first phone as primary if no primary exists
      phone = lead.contact.phones[0];
      lead.contact.primary_phone = phone;
      await this.phoneRepository.save(phone);
    }

    if (!phone) {
      throw new HttpException(
        'Phone number not found for lead',
        HttpStatus.NOT_FOUND,
      );
    }

    return phone;
  }

  /**
   * Finds or creates phone by number
   * Handles country code formatting
   */
  private async findOrCreatePhoneByNumber(
    phoneNumber: string,
    countryCode: string = '+91',
  ): Promise<Phone> {
    // Standardize the phone number and check if it becomes empty
    const standardizedPhone = phoneNumber.replace(/\D/g, '');
    // Skip processing if phone number becomes empty after standardization
    if (!standardizedPhone) {
      this.logger.warn(
        `Phone number becomes empty after standardization: ${phoneNumber}`,
      );
      throw new HttpException(
        'Invalid phone number provided',
        HttpStatus.BAD_REQUEST,
      );
    }

    let phone = await this.phoneRepository.findOne({
      where: { phone_number: standardizedPhone },
      relations: { contact: true },
    });

    if (!phone) {
      phone = new Phone();
      phone.phone_number = standardizedPhone;
      phone.country_code = countryCode;
      phone = await this.phoneRepository.save(phone);
    }

    return phone;
  }

  /**
   * Find phone by phone number
   */
  async findPhoneByNumber(phoneNumber: string): Promise<Phone | null> {
    this.logger.debug(`Looking up phone with number: ${phoneNumber}`);

    // Standardize the phone number and check if it becomes empty
    const standardizedPhone = phoneNumber.replace(/\D/g, '');
    // Skip processing if phone number becomes empty after standardization
    if (!standardizedPhone) {
      this.logger.warn(
        `Phone number becomes empty after standardization: ${phoneNumber}`,
      );
      return null;
    }

    const phone = await this.phoneRepository.findOne({
      where: { phone_number: standardizedPhone },
      relations: {
        contact: {
          leads: true,
          primary_phone: true,
        },
      },
    });

    if (phone) {
      this.logger.debug(
        `Found existing phone with ID ${phone.id} for number ${standardizedPhone}`,
      );
    } else {
      this.logger.debug(
        `No existing phone found for number ${standardizedPhone}`,
      );
    }

    return phone;
  }

  /**
   * Create a new contact record
   */
  async createContact(
    customerName?: string,
    customerLastName?: string,
  ): Promise<Contact> {
    this.logger.debug(
      `Creating new contact for customer: ${customerName || 'Unknown'}`,
    );

    const newContact = new Contact();
    newContact.first_name = customerName || 'Unknown';
    newContact.last_name = customerLastName || '';

    const savedContact = await this.contactRepository.save(newContact);
    this.logger.debug(`Created new contact with ID: ${savedContact.id}`);

    return savedContact;
  }

  /**
   * Create a new phone record associated with a contact
   */
  async createNewPhone(data: any): Promise<Phone> {
    this.logger.debug(`Creating new phone for number: ${data.phone_number}`);

    const newPhone = new Phone();
    newPhone.phone_number = data.customer_phone;
    newPhone.country_code = data.customer_country_code || '+91';

    const savedPhone = await this.phoneRepository.save(newPhone);
    this.logger.debug(`Created new phone with ID: ${savedPhone.id}`);

    return savedPhone;
  }
}
