import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { CallLog } from '../entities/call-log.entity';
import { CallType, CallStatus } from '../enums/call-log.enum';
import { LeadService } from '@modules/leads/services/lead.service';
import { CallLogService } from './call-log.service';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { Contact } from '@modules/contacts/entities/contact.entity';

@Injectable()
export class LeadResolverService {
  private readonly logger = new Logger(LeadResolverService.name);

  constructor(
    private readonly leadService: LeadService,
    private readonly callLogService: CallLogService,
  ) {}

  /**
   * Resolves a lead based on phone number and client context
   * @param phone Phone entity with associated contact
   * @param clientId Client context ID
   * @returns Associated lead or null if not found
   */
  async resolveLead(phone: Phone, clientId?: number): Promise<Lead | null> {
    try {
      if (!clientId) {
        Logger.debug('No client ID provided for lead resolution');
        return null;
      }

      // Try to find lead through existing contact
      if (phone.contact) {
        const lead = await this.findLeadByContact(phone.contact, clientId);
        if (lead) return lead;
      }

      // Try to find lead through phone number directly
      const lead = await this.leadService.findLeadByPhone(phone, clientId);
      if (lead) return lead;

      Logger.debug(
        `No lead found for phone ${phone.phone_number} and client ${clientId}`,
      );
      return null;
    } catch (error) {
      Logger.error('Lead resolution failed:', error);
      throw new HttpException(
        'Failed to resolve lead',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Finds a lead associated with a contact for a specific client
   * @param contact Contact entity to search through
   * @param clientId Client context ID
   */
  private async findLeadByContact(
    contact: Contact,
    clientId: number,
  ): Promise<Lead | null> {
    try {
      const lead = await this.leadService.findByClientAndContact(
        clientId,
        contact.id,
      );

      return lead || null;
    } catch (error) {
      Logger.error('Find lead by contact failed:', error);
      return null;
    }
  }

  /**
   * Handle post-call lead-related updates
   */
  async performPostCallLeadOperations(call: CallLog): Promise<void> {
    this.logger.log(
      `Performing post-call lead operations for call ID: ${call.id}`,
    );

    try {
      // Update missed calls tracking
      await this.updateMissedCallsTracking(call);

      // Update lead's last call date and status
      await this.updateLeadLastCallDate(call);

      this.logger.log(
        `Post-call lead operations completed successfully for call ID: ${call.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error in post-call lead operations: ${error.message}`,
        error.stack,
      );
      // Don't throw here - these are non-critical operations
    }
  }

  /**
   * Update missed calls tracking based on call outcome
   */
  private async updateMissedCallsTracking(call: CallLog): Promise<void> {
    if (
      call.call_type === CallType.OUTGOING ||
      (call.call_type === CallType.INCOMING &&
        call.call_status === CallStatus.ATTENDED)
    ) {
      if (call.lead?.id) {
        this.logger.debug(
          `Removing lead ID ${call.lead.id} from missed calls tracking`,
        );
        await this.callLogService.removeFromMissedCalls(call.lead.id);
      } else {
        this.logger.debug(
          `No lead associated with call, skipping missed calls cleanup`,
        );
      }
    }
  }

  /**
   * Update lead's last call date and status
   */
  private async updateLeadLastCallDate(call: CallLog): Promise<void> {
    if (call.lead?.id) {
      this.logger.debug(`Updating last call date for lead ID: ${call.lead.id}`);
      await this.leadService.updateLastCallDate(
        call.lead.id,
        call.call_status,
        call.start_stamp,
      );
      this.logger.debug(`Lead last call date updated successfully`);
    } else {
      this.logger.debug(
        `No lead associated with call, skipping last call date update`,
      );
    }
  }
}
