export enum CallType {
  INCOMING = 'incoming',
  OUTGOING = 'outgoing',
}

export enum CallStatus {
  MISSED = 'missed',
  ATTENDED = 'attended',
  WAS_MISSED = 'was_missed', // this is for the calls which were missed by spoc but called back (no more missed but not connected)
  NOT_CONNECTED = 'not_connected', // this is for the calls which were not connected to customer
}

export enum CallAttendStatus {
  ANSWERED_BY_USER = 'answered_by_user',
  NOT_ANSWERED_BY_USER = 'not_answered_by_user',
  ANSWERED_BY_CUSTOMER = 'answered_by_customer',
  NOT_ANSWERED_BY_CUSTOMER = 'not_answered_by_customer',
  FAILED = 'failed',
  SUCCESS = 'success',
  HANGUP_BY_CUSTOMER = 'hangup_by_customer',
}

/**
 * Numeric call status values from external API
 */
export enum NumericCallStatus {
  ANSWERED_BY_USER = 1,
  NOT_ANSWERED_BY_USER = 2,
  ANSWERED_BY_CUSTOMER = 3,
  NOT_ANSWERED_BY_CUSTOMER = 4,
  FAILED = 5,
  SUCCESS = 6,
  HANGUP_BY_CUSTOMER = 7,
}

/**
 * Tabs for call logs
 */
export enum Tab {
  missed_calls = 'missed_calls',
  ivr_missed_calls = 'ivr_missed_calls',
  untracked_calls = 'untracked_calls',
  to_update_calls = 'to_update_calls',
  dialled_calls = 'dialled_calls',
  connected_calls = 'connected_calls',
  ivr_today = 'ivr_today',
  mtd_ivr_calls = 'mtd_ivr_calls',
  Call_Logs = 'call_logs',
}
