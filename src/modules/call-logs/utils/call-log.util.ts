import { CallLogResponseDto } from '../dto/call-log-response.dto';
import { CallLog } from '../entities/call-log.entity';
import { CallAttendStatus, NumericCallStatus } from '../enums/call-log.enum';

/**
 * Maps numeric call status values to CallAttendStatus enum values
 * @param numericStatus The numeric call status from external data
 * @returns The corresponding CallAttendStatus enum value or null if not found
 */
export function mapCallStatusToCallAttendStatus(
  numericStatus: number | null | undefined,
): CallAttendStatus | null {
  if (numericStatus === null || numericStatus === undefined) {
    return null;
  }

  switch (numericStatus) {
    case NumericCallStatus.ANSWERED_BY_USER:
      return CallAttendStatus.ANSWERED_BY_USER;

    case NumericCallStatus.NOT_ANSWERED_BY_USER:
      return CallAttendStatus.NOT_ANSWERED_BY_USER;

    case NumericCallStatus.ANSWERED_BY_CUSTOMER:
      return CallAttendStatus.ANSWERED_BY_CUSTOMER;

    case NumericCallStatus.NOT_ANSWERED_BY_CUSTOMER:
      return CallAttendStatus.NOT_ANSWERED_BY_CUSTOMER;

    case NumericCallStatus.FAILED:
      return CallAttendStatus.FAILED;

    case NumericCallStatus.SUCCESS:
      return CallAttendStatus.SUCCESS;

    case NumericCallStatus.HANGUP_BY_CUSTOMER:
      return CallAttendStatus.HANGUP_BY_CUSTOMER;

    default:
      return null;
  }
}

/**
 * Transform a call log entity to a safe DTO without circular references
 */
export function transformCallLogToDto(callLog: CallLog): CallLogResponseDto {
  const dto = new CallLogResponseDto();

  // Copy direct properties
  dto.id = callLog.id;
  dto.call_id = callLog.call_id;
  dto.start_stamp = callLog.start_stamp;
  dto.end_stamp = callLog.end_stamp;
  dto.user_answer_stamp = callLog.user_answer_stamp;
  dto.customer_answer_stamp = callLog.customer_answer_stamp;
  dto.call_type = callLog.call_type;
  dto.call_status = callLog.call_status;
  dto.call_attend_status = callLog.call_attend_status;
  dto.duration = callLog.duration;
  dto.call_recording_url = callLog.call_recording_url;

  // Extract just what's needed from phone
  if (callLog.phone) {
    dto.phone = {
      id: callLog.phone.id,
      phone_number: callLog.phone.phone_number,
      country_code: callLog.phone.country_code,
    };
  }

  // Extract contact info if available through phone
  if (callLog.phone?.contact) {
    dto.contact = {
      id: callLog.phone.contact.id,
      first_name: callLog.phone.contact.first_name,
      last_name: callLog.phone.contact.last_name,
      full_name: callLog.phone.contact.full_name,
    };
  }

  return dto;
}

/**
 * Maps sort field names to their corresponding TypeORM paths for proper sorting
 */
export function getOrderObject(
  sortField: string,
  sortOrder: string = 'DESC',
): any {
  switch (sortField) {
    case 'candidate_id':
      return { lead: { contact: { candidate_id: sortOrder } } };
    case 'name':
      return { lead: { contact: { first_name: sortOrder } } };
    case 'count': //? check what is this field
      return { count: sortOrder };
    case 'created_at':
      return { created_at: sortOrder };
    default:
      return { created_at: 'DESC' };
  }
}
