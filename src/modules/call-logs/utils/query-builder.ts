import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Raw } from 'typeorm';
import { HttpException, HttpStatus } from '@nestjs/common';
import { CallLogFilterDto } from '../dto/call-log-filter.dto';
import { CallStatus, CallType, Tab } from '../enums/call-log.enum';
import { getTodayDateRange, getMonthToDateRange } from './date-utils';
import { PhoneType } from '@modules/contacts/enums/contact.enum';

/**
 * Helper function to add pattern search conditions to a query
 */
function addPatternSearchConditions(condition: any, pattern: string) {
  if (!pattern || pattern.trim() === '') {
    return;
  }

  const trimmedPattern = pattern.trim();
  const phonePattern = `%${trimmedPattern}%`;
  const emailPattern = `%${trimmedPattern.toLowerCase()}%`;

  // Create array of OR conditions for searching
  condition.lead = [
    // Search by candidate ID
    { contact: { candidate_id: <PERSON><PERSON>(phonePattern) } },

    // Search by primary phone and email (direct relationships)
    { contact: { primary_phone: { phone_number: <PERSON><PERSON>(phonePattern) } } },
    { contact: { primary_email: { email: <PERSON><PERSON>(emailPattern) } } },

    // Search in phone collection using raw query
    {
      id: Raw(
        (id) => `
        ${id} IN (
          SELECT l.id FROM lead l
          JOIN contact c ON c.id = l.contact_id
          JOIN phone p ON p.contact_id = c.id
          WHERE p.phone_number ILIKE '${phonePattern}'
        )
      `,
      ),
    },

    // Search in email collection using raw query
    {
      id: Raw(
        (id) => `
        ${id} IN (
          SELECT l.id FROM lead l
          JOIN contact c ON c.id = l.contact_id
          JOIN email e ON e.contact_id = c.id
          WHERE e.email ILIKE '${emailPattern}'
        )
      `,
      ),
    },
  ];
}

/**
 * Builds base query condition with user, date and pattern filters
 */
export function buildBaseCondition(
  filterDto: CallLogFilterDto,
  userIds: number[],
) {
  const { pattern, start_date, end_date } = filterDto;
  const baseCondition: any = {
    spoc: { id: In(userIds) },
  };

  // Add date filtering using the same operators as legacy code (>, <)
  if (start_date && end_date) {
    baseCondition.created_at = Between(start_date, end_date);
  } else if (start_date) {
    baseCondition.created_at = Raw((date) => `${date} > :date`, {
      date: start_date,
    });
  } else if (end_date) {
    baseCondition.created_at = Raw((date) => `${date} < :date`, {
      date: end_date,
    });
  }

  // Add pattern search conditions
  addPatternSearchConditions(baseCondition, pattern);

  return baseCondition;
}

/**
 * Builds query for untracked calls tab
 */
export function buildUntrackedCallsCondition(
  baseCondition: any,
  pattern?: string,
) {
  // For untracked calls, we need a special condition like the legacy code
  const condition = {
    // Match directly on phone entity, not through lead.contact.phones
    phone: {
      phone_number: pattern && pattern !== '' ? pattern : null, // Exact match if pattern provided
      phone_type: PhoneType.lead,
    },
    spoc: baseCondition.spoc,
    created_at: baseCondition.created_at,
  };

  return {
    lead: IsNull(),
    ...condition,
  };
}

/**
 * Builds query for to-update calls tab
 */
export function buildToUpdateCallsCondition(baseCondition: any) {
  return {
    is_disposed: false,
    call_status: CallStatus.ATTENDED,
    phone: { phone_type: PhoneType.lead },
    ...baseCondition,
  };
}

/**
 * Builds query for dialled calls tab
 */
export function buildDialledCallsCondition(baseCondition: any) {
  return {
    call_type: CallType.OUTGOING,
    call_status: CallStatus.MISSED,
    ...baseCondition,
  };
}

/**
 * Builds query for connected calls tab
 */
export function buildConnectedCallsCondition(baseCondition: any) {
  return {
    call_status: CallStatus.ATTENDED,
    ...baseCondition,
  };
}

/**
 * Builds query for IVR today tab
 */
export function buildIVRTodayCallsCondition(
  baseCondition: any,
  start_date?: Date,
  end_date?: Date,
  pattern?: string,
) {
  const { startDate, endDate } = getTodayDateRange();

  // Specific handling for IVR today as in legacy code
  const condition = {
    created_at: Between(start_date || startDate, end_date || endDate),
    call_type: CallType.INCOMING,
    is_IVR: true,
    spoc: baseCondition.spoc,
  };

  // This ensures we don't modify baseCondition (which could affect other functions)
  addPatternSearchConditions(condition, pattern);

  return condition;
}

/**
 * Builds query for missed calls tab
 */
export function buildMissedCallsCondition(baseCondition: any, isIVR: boolean) {
  return {
    call_status: CallStatus.MISSED,
    call_type: CallType.INCOMING,
    is_IVR: isIVR,
    ...baseCondition,
  };
}

/**
 * Builds query for MTD IVR calls tab
 */
export function buildMTDIVRCallsCondition(
  baseCondition: any,
  start_date?: Date,
  end_date?: Date,
) {
  const { startDate, endDate } = getMonthToDateRange();

  return {
    created_at: Between(start_date || startDate, end_date || endDate),
    call_type: CallType.INCOMING,
    is_IVR: true,
    ...baseCondition,
  };
}

/**
 * Builds query for all call logs tab
 */
export function buildAllCallLogsCondition(
  baseCondition: any,
  start_date?: Date,
  end_date?: Date,
  pattern?: string,
) {
  const { startDate, endDate } = getMonthToDateRange();

  const condition = {
    created_at: Between(start_date || startDate, end_date || endDate),
    spoc: baseCondition.spoc,
  };

  // Add pattern search if provided
  if (pattern && pattern.trim() !== '') {
    condition['lead'] = [
      { contact: { phones: { phone_number: pattern } } },
      { contact: { emails: { email: pattern.toLowerCase() } } },
      { contact: { candidate_id: ILike(`%${pattern}%`) } },
    ];
  }

  return condition;
}

/**
 * Main function to build query conditions based on the selected tab
 */
export function buildQueryCondition(
  filterDto: CallLogFilterDto,
  userIds: number[],
) {
  const { tab_id: tab, pattern, start_date, end_date } = filterDto;
  const baseCondition = buildBaseCondition(filterDto, userIds);

  switch (tab) {
    case Tab.missed_calls:
      return buildMissedCallsCondition(baseCondition, false);

    case Tab.ivr_missed_calls:
      return buildMissedCallsCondition(baseCondition, true);

    case Tab.untracked_calls:
      return buildUntrackedCallsCondition(baseCondition, pattern);

    case Tab.to_update_calls:
      return buildToUpdateCallsCondition(baseCondition);

    case Tab.dialled_calls:
      return buildDialledCallsCondition(baseCondition);

    case Tab.connected_calls:
      return buildConnectedCallsCondition(baseCondition);

    case Tab.ivr_today:
      return buildIVRTodayCallsCondition(
        baseCondition,
        start_date,
        end_date,
        pattern,
      );

    case Tab.mtd_ivr_calls:
      return buildMTDIVRCallsCondition(baseCondition, start_date, end_date);

    case Tab.Call_Logs:
      return buildAllCallLogsCondition(
        baseCondition,
        start_date,
        end_date,
        pattern,
      );

    default:
      throw new HttpException('Invalid tab selection', HttpStatus.BAD_REQUEST);
  }
}
