import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionService } from './permission.service';
import { PermissionController } from './permission.controller';
import { Permission } from './enities/permission.entity';
import { RoleModule } from '@modules/roles/role.module';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';

@Module({
  imports: [TypeOrmModule.forFeature([Permission]), RoleModule],
  controllers: [PermissionController],
  providers: [PermissionService, PermissionGuard],
  exports: [PermissionService],
})
export class PermissionModule {}
