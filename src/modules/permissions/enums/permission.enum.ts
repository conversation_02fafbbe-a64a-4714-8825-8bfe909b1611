export enum Action {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  CRUD = 'crud',
  ADMIN = 'admin',
  MANAGE = 'manage',
  // Add other actions as needed
}

export enum Resource {
  USER = 'user',
  CLIENT = 'client',
  ROLE = 'role',
  PERMISSION = 'permission',
  AUTH = 'auth',
  LEAD = 'lead',
  CONTACT = 'contact',
  MILES_OFFICE = 'miles_office',
  LEAD_PROGRAM_INTEREST = 'lead_program_interest',
  CALL = 'call',
  CALL_LOG = 'call_log',

  // Add other resources as needed
}
