import { Action } from '../enums/permission.enum';

/**
 * Defines which actions automatically grant other actions
 * Key: The higher level action
 * Value: Array of actions that are automatically granted
 */
export const ACTION_HIERARCHY: Record<string, string[]> = {
  [Action.CRUD]: [Action.CREATE, Action.READ, Action.UPDATE, Action.DELETE],
  [Action.ADMIN]: [
    Action.CREATE,
    Action.READ,
    Action.UPDATE,
    Action.DELETE,
    Action.CRUD,
    Action.MANAGE,
  ],
  [Action.MANAGE]: [Action.READ, Action.UPDATE],
  // You can add more hierarchies as needed
};

/**
 * Checks if having 'grantedAction' automatically grants 'requestedAction'
 * @param grantedAction The action the user has
 * @param requestedAction The action being checked
 * @param visited Set of actions already checked (for preventing infinite recursion)
 * @returns boolean
 */
export function actionIncludes(
  grantedAction: string,
  requestedAction: string,
  visited: Set<string> = new Set(),
): boolean {
  // If the user has the exact action, return true
  if (grantedAction === requestedAction) {
    return true;
  }

  // If we've already checked this action in the current recursion path,
  // return false to avoid infinite loops
  if (visited.has(grantedAction)) {
    return false;
  }

  // Add current action to visited set
  visited.add(grantedAction);

  // Check if the granted action directly includes the requested action
  const directIncludes =
    ACTION_HIERARCHY[grantedAction]?.includes(requestedAction) || false;
  if (directIncludes) {
    return true;
  }

  // Check for transitive permissions, passing the visited set
  for (const includedAction of ACTION_HIERARCHY[grantedAction] || []) {
    if (actionIncludes(includedAction, requestedAction, visited)) {
      return true;
    }
  }

  return false;
}
