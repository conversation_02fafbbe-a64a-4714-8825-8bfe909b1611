import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreatePermissionDto } from './create-permission.dto';

export class CreatePermissionsBulkDto {
  @ApiProperty({
    type: [CreatePermissionDto],
    description: 'Array of permissions to create (at least one required)',
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one permission is required' })
  @ValidateNested({ each: true })
  @Type(() => CreatePermissionDto)
  permissions: CreatePermissionDto[];
}
