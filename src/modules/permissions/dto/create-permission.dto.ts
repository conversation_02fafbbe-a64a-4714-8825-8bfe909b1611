import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Action, Resource } from '../enums/permission.enum';

export class CreatePermissionDto {
  @ApiProperty({
    enum: Resource,
    description: 'Resource the permission applies to',
  })
  @IsEnum(Resource)
  resource: Resource;

  @ApiProperty({ enum: Action, description: 'Action allowed on the resource' })
  @IsEnum(Action)
  action: Action;

  @ApiPropertyOptional({
    description: 'Optional description of the permission',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
