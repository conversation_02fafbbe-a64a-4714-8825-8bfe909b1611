import { Entity, Column, Index, Unique } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';
import { Action, Resource } from '../enums/permission.enum';

@Entity()
@Unique(['resource', 'action'])
export class Permission extends BaseEntity {
  @Column({
    type: 'enum',
    enum: Resource,
    nullable: false,
  })
  @Index()
  resource: Resource;

  @Column({
    type: 'enum',
    enum: Action,
    nullable: false,
  })
  @Index()
  action: Action;

  @Column({ type: 'text', nullable: true })
  description: string;
}
