import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { Permission } from './enities/permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';

import { Resource } from './enums/permission.enum';
import { Action } from './enums/permission.enum';
import { UpdatePermissionDto } from './dto/update-permission.dto';

/**
 * Service responsible for managing permission entities
 * Handles CRUD operations and validation for permissions
 */
@Injectable()
export class PermissionService {
  /**
   * Initializes the permission service
   * @param permissionRepository - TypeORM repository for Permission entities
   */
  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  /**
   * Creates a new permission
   * @param createPermissionDto - DTO containing permission details
   * @returns Newly created permission entity
   * @throws ConflictException if permission already exists
   */
  async create(createPermissionDto: CreatePermissionDto): Promise<Permission> {
    // Check for existing permission with same resource and action
    const existing = await this.permissionRepository.findOne({
      where: {
        resource: createPermissionDto.resource,
        action: createPermissionDto.action,
      },
    });

    if (existing) {
      throw new ConflictException('Permission already exists');
    }

    // Create and save new permission
    const permission = this.permissionRepository.create(createPermissionDto);
    return this.permissionRepository.save(permission);
  }

  /**
   * Creates multiple permissions in bulk
   * @param permissions - Array of permission DTOs to create
   * @returns Array of created permission entities
   */
  async createInBulk(
    permissions: CreatePermissionDto[],
  ): Promise<Permission[]> {
    const results: Permission[] = [];
    const errors: string[] = [];

    // Use a transaction to ensure all permissions are created atomically
    await this.permissionRepository.manager.transaction(
      async (entityManager) => {
        const permissionRepo = entityManager.getRepository(Permission);

        // Process each permission sequentially
        for (const permDto of permissions) {
          try {
            // Check for existing permission with same resource and action
            const existing = await permissionRepo.findOne({
              where: {
                resource: permDto.resource,
                action: permDto.action,
              },
            });

            if (existing) {
              errors.push(
                `Permission for ${permDto.resource}:${permDto.action} already exists`,
              );
              continue;
            }

            // Create and save new permission
            const permission = permissionRepo.create(permDto);
            const saved = await permissionRepo.save(permission);
            results.push(saved);
          } catch (error) {
            errors.push(
              `Failed to create permission ${permDto.resource}:${permDto.action}: ${error.message}`,
            );
          }
        }

        // If no permissions were created but there were attempts, throw an error
        if (results.length === 0 && permissions.length > 0) {
          throw new ConflictException(
            `Failed to create any permissions: ${errors.join('; ')}`,
          );
        }
      },
    );

    // Log warnings about skipped permissions
    if (errors.length > 0) {
      console.warn('Some permissions were not created:', errors);
    }

    return results;
  }

  /**
   * Retrieves all permissions
   * @returns Array of permission entities
   */
  async findAll(): Promise<Permission[]> {
    return this.permissionRepository.find();
  }

  /**
   * Finds a permission by its ID
   * @param id - Permission ID to search for
   * @returns Permission entity if found
   * @throws NotFoundException if permission doesn't exist
   */
  async findOne(id: number): Promise<Permission> {
    const permission = await this.permissionRepository.findOne({
      where: { id },
    });

    if (!permission) {
      throw new NotFoundException(`Permission with ID ${id} not found`);
    }

    return permission;
  }

  /**
   * Finds a permission by resource and action combination
   * @param resource - Resource type enum value
   * @param action - Action type enum value
   * @returns Permission entity if found
   * @throws NotFoundException if permission doesn't exist
   */
  async findByResourceAndAction(
    resource: Resource,
    action: Action,
  ): Promise<Permission> {
    const permission = await this.permissionRepository.findOne({
      where: { resource, action },
    });

    if (!permission) {
      throw new NotFoundException(
        `Permission for ${resource}:${action} not found`,
      );
    }

    return permission;
  }

  /**
   * Updates an existing permission
   * @param id - ID of permission to update
   * @param updatePermissionDto - DTO containing updated permission details
   * @returns Updated permission entity
   * @throws NotFoundException if permission doesn't exist
   * @throws ConflictException if updated values conflict with existing permission
   */
  async update(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
  ): Promise<Permission> {
    const permission = await this.findOne(id);

    // Validate uniqueness of resource-action combination
    if (updatePermissionDto.resource || updatePermissionDto.action) {
      const existing = await this.permissionRepository.findOne({
        where: {
          resource: updatePermissionDto.resource || permission.resource,
          action: updatePermissionDto.action || permission.action,
          id: Not(id),
        },
      });

      if (existing) {
        throw new ConflictException('Permission combination already exists');
      }
    }

    // Apply updates and save
    Object.assign(permission, updatePermissionDto);
    return this.permissionRepository.save(permission);
  }

  /**
   * Removes a permission
   * @param id - ID of permission to remove
   * @throws NotFoundException if permission doesn't exist
   */
  async remove(id: number): Promise<void> {
    const permission = await this.findOne(id);
    await this.permissionRepository.remove(permission);
  }
}
