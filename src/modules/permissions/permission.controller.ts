import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PermissionService } from './permission.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { Permission } from './enities/permission.entity';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { Resource, Action } from './enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { CreatePermissionsBulkDto } from './dto/create-permissions-bulk.dto';

import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';

@ApiTags('permissions')
@Controller('permissions')
@UseGuards(PermissionGuard)
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post()
  // Uncomment this if permission required
  // @RequirePermission({ resource: Resource.PERMISSION, action: Action.CREATE })
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiBody({ type: CreatePermissionDto })
  create(
    @Body() createPermissionDto: CreatePermissionDto,
  ): Promise<Permission> {
    return this.permissionService.create(createPermissionDto);
  }

  @Post('bulk')
  @RequirePermission({ resource: Resource.PERMISSION, action: Action.CREATE })
  @ApiOperation({ summary: 'Create multiple permissions in bulk' })
  @ApiBody({ type: CreatePermissionsBulkDto })
  createInBulk(
    @Body() bulkDto: CreatePermissionsBulkDto,
  ): Promise<Permission[]> {
    return this.permissionService.createInBulk(bulkDto.permissions);
  }

  @Get()
  @RequirePermission({ resource: Resource.PERMISSION, action: Action.READ })
  @ApiOperation({ summary: 'Get all permissions' })
  findAll(): Promise<Permission[]> {
    return this.permissionService.findAll();
  }

  @Get(':id')
  @RequirePermission({ resource: Resource.PERMISSION, action: Action.READ })
  @ApiOperation({ summary: 'Get a permission by ID' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  findOne(@Param('id') id: string): Promise<Permission> {
    return this.permissionService.findOne(+id);
  }

  @Patch(':id')
  @RequirePermission({ resource: Resource.PERMISSION, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update a permission by ID' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  @ApiBody({ type: UpdatePermissionDto })
  update(
    @Param('id') id: string,
    @Body() updatePermissionDto: UpdatePermissionDto,
  ): Promise<Permission> {
    return this.permissionService.update(+id, updatePermissionDto);
  }

  @Delete(':id')
  @RequirePermission({ resource: Resource.PERMISSION, action: Action.DELETE })
  @ApiOperation({ summary: 'Delete a permission by ID' })
  @ApiParam({ name: 'id', description: 'Permission ID', type: Number })
  remove(@Param('id') id: string): Promise<void> {
    return this.permissionService.remove(+id);
  }
}
