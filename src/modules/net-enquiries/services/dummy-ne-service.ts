import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NetEnquiry, NEType } from '../entities/net-enquiry.entity';
import { CreateNetEnquiryDto } from '../dto/create-net-enquiry.dto';
import { ProgramService } from 'src/modules/programs/services/program.service';
import { CampaignService } from 'src/modules/campaigns/services/campaign.service';
import { LeadProgramInterestService } from 'src/modules/leads/services/lead-program-interest.service';
import { CreateLeadDto } from 'src/modules/leads/dto/create-lead.dto';
import { CreateLeadProgramInterestDto } from 'src/modules/leads/dto/create-lead-program-interest.dto';
import { WebinarService } from 'src/modules/webinars/services/webinar.service';
import { LeadService } from 'src/modules/leads/services/lead.service';
import { LeadProgramInterest } from 'src/modules/leads/entities/lead-program-interest.entity';
import { LeadCallStatus } from '@modules/leads/enums/lead-status.enum';
import { locations } from 'src/common/constants/locations';
import { LeadLevelService } from 'src/modules/leads/services/lead-level.service';
import { LeadHistoryService } from 'src/modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  EngagementSubActionType,
} from 'src/modules/lead-histories/enums/lead-history.enum';
import { UserService } from '@modules/users/services/user.service';
import { AllowedTransitionMethodEnum } from '@modules/leads/enums/lead-level.enum';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadSpoc } from '@modules/leads/entities/lead-spoc.entity';
import { SpocType } from '@modules/leads/enums/spoc.enum';
import { LeadAllocationHistoryService } from '@modules/lead-allocations/services/lead-allocation-history.service';
import { AllocationTypeEnum } from '@modules/lead-allocations/enums/allocation-type.enum';

// Extended DTO for dummy service with direct SPOC allocation
export interface CreateDummyNetEnquiryDto extends CreateNetEnquiryDto {
  spoc_user_id?: number; // Direct SPOC user ID for allocation
  miles_office_id?: number | null; // Optional miles office ID
  enquiry_created_at?: string;
}

@Injectable()
export class DummyNEService {
  private readonly logger = new Logger(DummyNEService.name);

  constructor(
    @InjectRepository(NetEnquiry)
    private readonly netEnquiryRepository: Repository<NetEnquiry>,
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(LeadSpoc)
    private readonly leadSpocRepository: Repository<LeadSpoc>,
    private readonly programService: ProgramService,
    private readonly campaignService: CampaignService,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly leadService: LeadService,
    @Inject(forwardRef(() => WebinarService))
    private readonly webinarService: WebinarService,
    private readonly leadLevelService: LeadLevelService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
    private readonly leadAllocationHistoryService: LeadAllocationHistoryService,
  ) {}

  async create(
    createNetEnquiryDto: CreateDummyNetEnquiryDto,
    userId?: number,
  ): Promise<NetEnquiry> {
    this.logger.log('Starting create dummy net enquiry process');
    const programId = createNetEnquiryDto.program_id;

    if (!programId) {
      this.logger.error('Program ID is missing in createNetEnquiryDto');
      throw new Error('Program ID is required');
    }

    this.logger.log('CREATE DUMMY NET ENQUIRY DTO:', createNetEnquiryDto);

    let program;
    let netEnquiry;
    let campaign;
    let lead;
    let finalLeadProgInt: LeadProgramInterest | null = null;

    try {
      this.logger.log(`Fetching program with ID: ${programId}`);
      program = await this.programService.findOne(programId);

      if (!program) {
        this.logger.error(`Program with ID ${programId} not found`);
        throw new Error(`Program with ID ${programId} not found`);
      }

      this.logger.log('Program found:', program);

      netEnquiry = this.netEnquiryRepository.create({
        ...createNetEnquiryDto,
        created_by: userId,
      });
      netEnquiry.program = program;

      this.logger.log('Net enquiry object created:', netEnquiry);
    } catch (error) {
      this.logger.error(
        `Error initializing net enquiry: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to initialize net enquiry with program');
    }

    if (createNetEnquiryDto.campaign_id) {
      try {
        this.logger.log(
          `Fetching campaign with ID: ${createNetEnquiryDto.campaign_id}`,
        );
        campaign = await this.campaignService.findOne(
          createNetEnquiryDto.campaign_id,
        );
        netEnquiry.campaign = campaign;
        this.logger.log('Campaign found and set:', campaign);
      } catch (error) {
        this.logger.warn(
          `Campaign with ID ${createNetEnquiryDto.campaign_id} not found: ${error.message}`,
          error.stack,
        );
      }
    }

    let milesOfficeId = createNetEnquiryDto.miles_office_id;

    if (!milesOfficeId) {
      if (createNetEnquiryDto.num_verify_location) {
        const numVerifyLocation =
          createNetEnquiryDto.num_verify_location.trim();
        const numVerifyCountryCode =
          createNetEnquiryDto.num_verify_country_code.trim();

        this.logger.log(
          `Finding office for country code ${numVerifyCountryCode} and location ${numVerifyLocation}`,
        );
        const offices = locations.filter((location) => {
          return (
            location.countryShort === numVerifyCountryCode &&
            (location.state === numVerifyLocation ||
              location.mappedStates.includes(numVerifyLocation) ||
              location.mappedCities.includes(numVerifyLocation))
          );
        });

        let office;
        if (offices.length === 0) {
          if (numVerifyCountryCode !== 'IN') {
            office = {
              country: 'UAE',
              countryShort: 'UAE',
              state: 'Dubai',
              city: 'Dubai',
              office_id: 10,
              mappedStates: [],
            };
            this.logger.log('Default UAE office assigned due to no matches');
          } else {
            this.logger.log('No matching office found for IN location');
          }
        } else {
          office = offices[0];
          this.logger.log('Office found:', office);
        }

        milesOfficeId = office?.office_id ?? null;
        this.logger.log(`Miles office ID set to: ${milesOfficeId}`);
      }
    }

    const leadDto: CreateLeadDto = {
      contact_info: createNetEnquiryDto.contact_info
        ? {
            ...createNetEnquiryDto.contact_info,
            miles_office_id: milesOfficeId,
          }
        : {
            first_name: createNetEnquiryDto.first_name,
            last_name: createNetEnquiryDto.last_name,
            email: {
              email: createNetEnquiryDto.email,
            },
            phone: {
              phone_number: createNetEnquiryDto.phone.trim(),
              country_code: createNetEnquiryDto.country_code || '+91',
            },
            miles_office_id: milesOfficeId,
          },
      client_id: program.client_id,
      notes: `Lead created from dummy net enquiry`,
      metadata: createNetEnquiryDto.enquiry_dto || {},
    };

    this.logger.log('Lead DTO prepared:', leadDto);

    try {
      this.logger.log('Handling lead creation');
      const leadResult = await this.leadService.handleLead(leadDto, userId);
      lead = leadResult.lead;
      const isNew = leadResult.isNew;

      this.logger.log(`Lead handled. Lead ID: ${lead.id}, Is New: ${isNew}`);

      if (isNew) {
        this.logger.log(
          'New lead detected, setting default lead level and creating lead program interest',
        );
        const defaultLevel =
          await this.leadLevelService.findDefaultForNewEnquiry(
            program.client_id,
            programId,
          );
        this.logger.log('Default lead level found:', defaultLevel);

        const leadProgramInterestDto: CreateLeadProgramInterestDto = {
          lead_id: lead.id,
          program_id: programId,
          client_id: program.client_id,
          metadata: createNetEnquiryDto.enquiry_dto,
          lead_call_status: LeadCallStatus.NOTCALLED,
          lead_source_id: createNetEnquiryDto.lead_source_id,
          lead_level_id: defaultLevel?.id,
        };

        try {
          const leadProgramInterest =
            await this.leadProgramInterestService.create(
              leadProgramInterestDto,
              lead.id,
              userId,
            );
          try {
            await this.leadLevelService.createLevelChangeHistory({
              programInterestId: leadProgramInterest.id,
              fromLevelId: null,
              toLevelId: defaultLevel?.id,
              comments: 'Initial level assignment',
              transitionMethod: AllowedTransitionMethodEnum.System,
            });
          } catch (error) {
            this.logger.error(
              `Failed to create level change history for new lead: ${error.message}`,
              error.stack,
            );
          }
          netEnquiry.lead_program_interest = leadProgramInterest;
          netEnquiry.ne_type = NEType.NE;
          finalLeadProgInt = leadProgramInterest;

          if (defaultLevel) {
            this.logger.log(
              `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for new enquiry lead ${lead.id}`,
            );
          } else {
            this.logger.warn(
              `No default lead level found for client ${program.client_id}, lead ${lead.id} created without a level`,
            );
          }

          // Direct SPOC allocation if spoc_user_id is provided
          if (createNetEnquiryDto.spoc_user_id) {
            this.logger.log('Allocating SPOC directly with provided user ID');
            await this.allocateSpocDirectly(
              lead.id,
              createNetEnquiryDto.spoc_user_id,
              createNetEnquiryDto.lead_source_id,
              userId,
            );
          }
        } catch (error) {
          this.logger.error(
            `Failed to create lead program interest: ${error.message}`,
            error.stack,
          );
          netEnquiry.ne_type = NEType.NE;
        }
      } else {
        this.logger.log(
          'Re-enquiry detected, updating lead call status and lead program interest',
        );

        lead.lead_call_status = LeadCallStatus.MHP;
        try {
          await this.leadService.update(lead.id, lead, userId);
          this.logger.log(
            `Lead call status updated to MHP for lead ID ${lead.id}`,
          );
        } catch (updateError) {
          this.logger.error(
            `Failed to update lead MHP state: ${updateError.message}`,
            updateError.stack,
          );
        }

        const leadProgramInterestDto: CreateLeadProgramInterestDto = {
          lead_id: lead.id,
          program_id: programId,
          client_id: program.client_id,
          metadata: {
            ...createNetEnquiryDto.enquiry_dto,
          },
          lead_source_id: createNetEnquiryDto.lead_source_id,
          lead_level_id: null,
          lead_call_status: LeadCallStatus.NOTCALLED,
        };

        try {
          this.logger.log(
            'Finding existing lead program interest for re-enquiry',
          );
          const existingInterest =
            await this.leadProgramInterestService.findByLeadIdAndProgramId(
              lead.id,
              programId,
            );

          if (existingInterest) {
            this.logger.log(
              'Existing lead program interest found:',
              existingInterest.id,
            );
            const currentLevelId = existingInterest.lead_level_id;
            let targetLevelId = currentLevelId;

            if (currentLevelId) {
              try {
                this.logger.log(
                  `Handling lead level transition for re-enquiry from level ${currentLevelId}`,
                );
                targetLevelId =
                  await this.leadLevelService.handleReEnquiryLevelTransition(
                    currentLevelId,
                    program.client_id,
                  );
                this.logger.log(
                  `Lead level transitioned from ${currentLevelId} to ${targetLevelId} for lead ${lead.id}`,
                );
              } catch (levelError) {
                this.logger.error(
                  `Failed to handle lead level transition: ${levelError.message}`,
                  levelError.stack,
                );
                targetLevelId = currentLevelId;
              }
            }

            existingInterest.call_status = LeadCallStatus.MHP;
            existingInterest.re_enquiry_date = new Date();
            existingInterest.lead_level_id = targetLevelId;
            existingInterest.metadata = {
              ...existingInterest.metadata,
              ...createNetEnquiryDto.enquiry_dto,
              updated_from: 'net_enquiry',
              update_mechanism: 're-enquiry',
            };

            try {
              const updatedInterest =
                await this.leadProgramInterestService.update(
                  existingInterest.id,
                  existingInterest,
                  userId,
                );
              netEnquiry.lead_program_interest = updatedInterest;
              finalLeadProgInt = updatedInterest;
              this.logger.log(
                `Lead program interest updated for lead ${lead.id}`,
              );

              try {
                if (targetLevelId !== currentLevelId) {
                  await this.leadLevelService.createLevelChangeHistory({
                    programInterestId: updatedInterest.id,
                    fromLevelId: currentLevelId,
                    toLevelId: targetLevelId,
                    comments: `Re-enquiry level transition from ${currentLevelId} to ${targetLevelId}`,
                    transitionMethod: AllowedTransitionMethodEnum.System,
                  });
                }
              } catch (error) {
                this.logger.error(
                  `Failed to create level change history for re-enquiry: ${error.message}`,
                  error.stack,
                );
              }
            } catch (updateError) {
              this.logger.error(
                `Failed to update lead program interest: ${updateError.message}`,
                updateError.stack,
              );
              netEnquiry.lead_program_interest = existingInterest;
              finalLeadProgInt = existingInterest;
            }
          } else {
            this.logger.log(
              'No existing lead program interest found, creating a new one',
            );
            const defaultLevel =
              await this.leadLevelService.findDefaultForNewEnquiry(
                program.client_id,
                programId,
              );

            try {
              const newInterest = await this.leadProgramInterestService.create(
                {
                  ...leadProgramInterestDto,
                  lead_level_id: defaultLevel?.id,
                  metadata: {
                    ...leadProgramInterestDto.metadata,
                    created_from: 'net_enquiry',
                    creation_mechanism: 're-enquiry but no existing interest',
                  },
                },
                lead.id,
                userId,
              );

              netEnquiry.lead_program_interest = newInterest;

              finalLeadProgInt = newInterest;

              try {
                await this.leadLevelService.createLevelChangeHistory({
                  programInterestId: newInterest.id,
                  fromLevelId: null,
                  toLevelId: defaultLevel?.id,
                  comments: 'Initial level assignment',
                  transitionMethod: AllowedTransitionMethodEnum.System,
                });
              } catch (error) {
                this.logger.error(
                  `Failed to create level change history for new lead: ${error.message}`,
                  error.stack,
                );
              }

              if (defaultLevel) {
                this.logger.log(
                  `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for re-enquiry with no existing interest, lead ${lead.id}`,
                );
              } else {
                this.logger.warn(
                  `No default lead level found for client ${program.client_id}, lead program interest created without a level`,
                );
              }
            } catch (createError) {
              this.logger.error(
                `Failed to create new lead program interest: ${createError.message}`,
                createError.stack,
              );
            }
          }
        } catch (findError) {
          this.logger.error(
            `Error finding lead program interest: ${findError.message}`,
            findError.stack,
          );
          try {
            this.logger.log(
              'Creating fallback lead program interest due to error finding existing interest',
            );
            const defaultLevel =
              await this.leadLevelService.findDefaultForNewEnquiry(
                program.client_id,
                programId,
              );

            const newInterest = await this.leadProgramInterestService.create(
              {
                ...leadProgramInterestDto,
                lead_level_id: defaultLevel?.id,
                metadata: {
                  ...leadProgramInterestDto.metadata,
                  created_from: 'net_enquiry',
                  creation_mechanism: 'fallback',
                },
              },
              lead.id,
              userId,
            );
            netEnquiry.lead_program_interest = newInterest;
            finalLeadProgInt = newInterest;

            try {
              await this.leadLevelService.createLevelChangeHistory({
                programInterestId: newInterest.id,
                fromLevelId: null,
                toLevelId: defaultLevel?.id,
                comments: 'Initial level assignment',
                transitionMethod: AllowedTransitionMethodEnum.System,
              });
            } catch (error) {
              this.logger.error(
                `Failed to create level change history for new lead: ${error.message}`,
                error.stack,
              );
            }

            if (defaultLevel) {
              this.logger.log(
                `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for fallback scenario, lead ${lead.id}`,
              );
            }
          } catch (createError) {
            this.logger.error(
              `Failed to create fallback lead program interest: ${createError.message}`,
              createError.stack,
            );
          }
        }
        netEnquiry.ne_type = NEType.MHP;
      }
    } catch (leadError) {
      this.logger.error(
        `Error handling lead creation: ${leadError.message}`,
        leadError.stack,
      );
    }

    if (finalLeadProgInt && createNetEnquiryDto.register_for_webinar) {
      try {
        this.logger.log('Registering lead for all upcoming webinars');
        await this.webinarService.registerLeadForAllUpcomingWebinars(
          {
            leadProgramInterestId: finalLeadProgInt.id,
            campaignId: createNetEnquiryDto.campaign_id,
            clientId: program.client_id,
            programId: programId,
          },
          userId,
        );
        this.logger.log('Lead registered for webinars successfully');
      } catch (error) {
        this.logger.error(
          `Error registering lead for webinars: ${error.message}`,
          error.stack,
        );
      }
    }

    try {
      this.logger.log('Saving net enquiry to database');
      const savedNetEnquiry = await this.netEnquiryRepository.save(netEnquiry);
      this.logger.log(
        'Net enquiry saved successfully, ID:',
        savedNetEnquiry.id,
      );

      // Create lead history for net enquiry
      if (lead) {
        try {
          this.createNetEnquiryHistory(
            savedNetEnquiry,
            lead?.id,
            lead?.contact_id,
            program,
            campaign,
            userId,
          );
        } catch (historyError) {
          this.logger.error(
            `Failed to create lead history for net enquiry: ${historyError.message}`,
            historyError.stack,
          );
        }
      }

      return savedNetEnquiry;
    } catch (saveError) {
      this.logger.error(
        `Failed to save net enquiry: ${saveError.message}`,
        saveError.stack,
      );
      this.logger.log(
        'Attempting to save minimal net enquiry data as fallback',
      );

      try {
        const minimalNetEnquiry = this.netEnquiryRepository.create({
          ...createNetEnquiryDto,
          metadata: {
            ...createNetEnquiryDto,
            failed_save_reason: saveError.message,
            recovery_attempt: true,
          },
          created_by: userId,
        });
        const savedMinimal =
          await this.netEnquiryRepository.save(minimalNetEnquiry);
        this.logger.log(
          'Minimal net enquiry saved successfully as fallback, ID:',
          savedMinimal.id,
        );
        return savedMinimal;
      } catch (finalError) {
        this.logger.error(
          `CRITICAL: All attempts to save net enquiry data failed: ${finalError.message}`,
          finalError.stack,
        );
        throw new Error(
          'All attempts to save net enquiry data failed. Please contact support.',
        );
      }
    }
  }

  /**
   * Direct SPOC allocation method - allocates SPOC immediately and records allocation
   */
  private async allocateSpocDirectly(
    leadId: number,
    spocUserId: number,
    sourceId: number,
    userId?: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Starting direct SPOC allocation for Lead ID: ${leadId}, SPOC User ID: ${spocUserId}`,
      );

      // Get the lead first
      const lead = await this.leadService.findOne(leadId);
      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      // Get the SPOC user
      const spocUser = await this.userService.findOne(spocUserId);
      if (!spocUser) {
        throw new Error(`SPOC User with ID ${spocUserId} not found`);
      }

      // Update the lead's owner directly
      const updateResult = await this.updateLeadOwnerDirect(
        leadId,
        spocUserId,
        lead.client_id,
      );

      if (updateResult) {
        this.logger.log(
          `Successfully allocated SPOC User ID ${spocUserId} to Lead ID ${leadId}`,
        );

        // Record allocation history
        try {
          await this.leadAllocationHistoryService.recordAllocationHistoryNonTransactional(
            {
              lead,
              spocId: spocUserId,
              allocationType: AllocationTypeEnum.MANUAL_REASSIGNMENT,
              allocatedBy: userId ? `user_${userId}` : 'system_direct',
              notes: `Direct SPOC assignment on ${new Date().toISOString()}`,
              clientId: lead.client_id,
              cityId: lead.contact?.miles_office_id,
              sourceId: sourceId,
            },
          );
        } catch (historyError) {
          this.logger.error(
            `Failed to record allocation history: ${historyError.message}`,
            historyError.stack,
          );
        }

        // Create lead history for direct allocation
        try {
          await this.leadHistoryService.createLeadHistory({
            action: ActionType.ENGAGEMENT,
            subAction: EngagementSubActionType.ENQUIRY,
            leadId,
            contactId: lead.contact_id,
            performedByUser: spocUser,
            performedBy: spocUserId,
            details: {
              allocationType: 'Direct Assignment',
              spocName: `${spocUser.first_name} ${spocUser.last_name}`,
              sourceId: sourceId,
              assignedBy: userId ? `User ID: ${userId}` : 'System',
            },
            metadata: {
              direct_allocation: true,
              spoc_user_id: spocUserId,
              source_id: sourceId,
              allocated_at: new Date(),
            },
          });
        } catch (historyError) {
          this.logger.error(
            `Failed to create allocation history: ${historyError.message}`,
            historyError.stack,
          );
        }
      } else {
        this.logger.warn(
          `Direct SPOC allocation returned false for Lead ID: ${leadId}, SPOC User ID: ${spocUserId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error during direct SPOC allocation for Lead ID: ${leadId}, SPOC User ID: ${spocUserId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Direct implementation of lead owner update without transaction dependency
   */
  private async updateLeadOwnerDirect(
    leadId: number,
    ownerId: number,
    clientId: number,
    spocType: SpocType = SpocType.CC,
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Updating lead owner for Lead ID: ${leadId}, Owner ID: ${ownerId}`,
      );

      // Check if this is the first time a SPOC is being assigned to this lead
      const existingSpocs = await this.leadSpocRepository.find({
        where: { lead_id: leadId },
      });

      const isFirstAssignment = !existingSpocs || existingSpocs.length === 0;

      // Check if there's already a spoc with the same type for this lead
      const existingLeadSpoc = existingSpocs?.find(
        (spoc) => spoc.spoc_type === spocType,
      );

      let leadSpocId: number;

      if (existingLeadSpoc) {
        // Update the existing lead spoc entry
        await this.leadSpocRepository.update(
          { id: existingLeadSpoc.id },
          {
            spoc_id: ownerId,
            updated_at: new Date(),
          },
        );
        leadSpocId = existingLeadSpoc.id;
        this.logger.log(
          `Updated existing LeadSpoc with id ${existingLeadSpoc.id} for Lead ${leadId} and type ${spocType}`,
        );
      } else {
        // Create a new LeadSpoc entry for this assignment
        const leadSpoc = this.leadSpocRepository.create({
          lead_id: leadId,
          spoc_id: ownerId,
          spoc_type: spocType,
          is_initial_owner: isFirstAssignment,
          client_id: clientId,
        });

        // Save the new LeadSpoc
        const savedLeadSpoc = await this.leadSpocRepository.save(leadSpoc);
        leadSpocId = savedLeadSpoc.id;
        this.logger.log(
          `Created new LeadSpoc with id ${savedLeadSpoc.id} for Lead ${leadId} and type ${spocType}`,
        );
      }

      // Update the lead's owner and owner_spoc reference
      await this.leadRepository.update(
        { id: leadId },
        {
          owner_spoc_id: leadSpocId,
          updated_at: new Date(),
        },
      );

      this.logger.log(
        `Successfully updated lead ${leadId} owner to SPOC ${ownerId}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Error updating lead owner for Lead ID: ${leadId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Helper method to create lead history for net enquiry
   */
  private async createNetEnquiryHistory(
    netEnquiry: NetEnquiry,
    leadId: number,
    contactId: number,
    program: any,
    campaign: any,
    userId?: number,
  ): Promise<void> {
    const isReEnquiry = netEnquiry.ne_type === NEType.MHP;

    const details = {
      name: `${netEnquiry?.first_name ?? ''} ${netEnquiry?.last_name ?? ''}`.trim(),
      program: program.name,
      campaignName: campaign?.name,
      enquiryType: isReEnquiry ? 'Re-Enquiry' : 'New Enquiry',
      source: netEnquiry?.lead_source?.name,
      comingFrom: netEnquiry?.coming_from,
    };

    this.leadHistoryService.createLeadHistory({
      action: ActionType.ENGAGEMENT,
      subAction: EngagementSubActionType.ENQUIRY,
      leadId,
      contactId,
      performedByUser: userId ? await this.userService.findOne(userId) : null,
      performedBy: userId || null,
      details,
      metadata: netEnquiry,
    });
  }
}
