import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { NetEnquiryService } from './services/net-enquiry.service';
import { CreateNetEnquiryDto } from './dto/create-net-enquiry.dto';
import { UpdateNetEnquiryDto } from './dto/update-net-enquiry.dto';
import { NetEnquiry } from './entities/net-enquiry.entity';
import { Public } from '@modules/auth/decorators/public.decorator';

import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import {
  CreateDummyNetEnquiryDto,
  DummyNEService,
} from './services/dummy-ne-service';

@ApiTags('net-enquiries')
@Controller('enquiries')
export class NetEnquiryController {
  constructor(
    private readonly netEnquiryService: NetEnquiryService,
    private readonly dummyNetEnquiryService: DummyNEService,
  ) {}

  @Public()
  @Post('/add')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new net enquiry (mostly system usage)' })
  @ApiBody({ type: CreateNetEnquiryDto })
  async create(
    @Body() createNetEnquiryDto: CreateNetEnquiryDto,
  ): Promise<NetEnquiry> {
    return this.netEnquiryService.create(createNetEnquiryDto);
  }

  @Public()
  @Post('/add-from-existing-mf')
  async createFromExistingMF(
    @Body() createNetEnquiryDto: CreateDummyNetEnquiryDto,
  ): Promise<NetEnquiry> {
    return this.dummyNetEnquiryService.create(createNetEnquiryDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get net enquiry by ID' })
  @ApiParam({ name: 'id', description: 'Net enquiry ID' })
  async findOne(@Param('id') id: string): Promise<NetEnquiry> {
    return this.netEnquiryService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update net enquiry by ID' })
  @ApiParam({ name: 'id', description: 'Net enquiry ID' })
  @ApiBody({ type: UpdateNetEnquiryDto })
  async update(
    @Param('id') id: string,
    @Body() updateNetEnquiryDto: UpdateNetEnquiryDto,
  ): Promise<NetEnquiry> {
    return this.netEnquiryService.update(+id, updateNetEnquiryDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete net enquiry by ID' })
  @ApiParam({ name: 'id', description: 'Net enquiry ID' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.netEnquiryService.remove(+id);
  }
}
