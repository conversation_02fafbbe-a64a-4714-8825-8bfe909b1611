import { <PERSON>tity, Column, <PERSON>T<PERSON><PERSON>ne, JoinColumn, Index } from 'typeorm';
import { Program } from '../../programs/entities/program.entity';
import { Campaign } from '../../campaigns/entities/campaign.entity';
import { UUID } from 'crypto';
import { BaseEntity } from 'src/common/entities/base.entity';
import { LeadProgramInterest } from 'src/modules/leads/entities/lead-program-interest.entity';
import { LeadSource } from '@modules/lead-allocations/entities';

export enum NEType {
  NE = 'NE',
  MHP = 'MHP',
}

export enum InterestedToWorkInUs {
  YES = 'yes',
  NO = 'no',
  MAYBE = 'maybe',
}

@Entity('net_enquiry', { orderBy: { created_at: 'DESC' } })
export class NetEnquiry extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  first_name: string;

  @Column({ type: 'varchar', nullable: true })
  last_name: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'varchar', nullable: true })
  country_code: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Index()
  @ManyToOne(() => Program)
  @JoinColumn({ name: 'program_id' })
  program: Program;

  @Column({ type: 'varchar', nullable: true })
  coming_from: string;

  @Index()
  @ManyToOne(() => Campaign)
  @JoinColumn({ name: 'campaign_id' })
  campaign: Campaign;

  @ManyToOne(() => LeadSource, { nullable: true })
  @JoinColumn({ name: 'lead_source_id' })
  lead_source: LeadSource;

  @Column({ type: 'integer', nullable: true })
  lead_source_id: number;

  @Column({ type: 'varchar', nullable: true })
  gcl_id: string;

  @Column({ type: 'boolean', nullable: true, default: true })
  whatsapp_opt_in: boolean;

  @Column({ type: 'boolean', nullable: true, default: false })
  register_for_webinar: boolean;

  @Column({ type: 'boolean', nullable: true })
  intrested_to_work_in_us: boolean;

  @Column({ type: 'enum', enum: InterestedToWorkInUs, nullable: true })
  intrested_for_work_in_us: InterestedToWorkInUs;

  @Column({ type: 'varchar', nullable: true })
  graduation_year: string;

  @Column({ type: 'varchar', nullable: true })
  page_full_url: string;

  @Column({ type: 'varchar', nullable: true })
  url_page_path: string;

  @Column({ type: 'varchar', nullable: true })
  linkedin_url: string;

  // @Index()
  // @ManyToOne(() => Qualification)
  // education_qualification: Qualification;

  @Column({ type: 'boolean', nullable: false, default: false })
  isFromWelcomeBack: boolean;

  @Index()
  @ManyToOne(
    () => LeadProgramInterest,
    (leadProgram) => leadProgram.net_enquiries,
  )
  @JoinColumn({ name: 'lead_program_interest_id' })
  lead_program_interest: LeadProgramInterest;

  @Column({ type: 'integer', nullable: true })
  lead_program_interest_id: number;

  @Column({ type: 'uuid', nullable: true, default: () => 'gen_random_uuid()' })
  uuid: UUID;

  @Column({ type: 'enum', enum: NEType, nullable: true })
  ne_type: NEType;

  @Column({ type: 'varchar', nullable: true })
  company_website_url: string;

  @Column({ type: 'varchar', nullable: true })
  state: string;

  @Column({ type: 'varchar', nullable: true })
  nationality: string;

  @Column({ type: 'varchar', nullable: true })
  country_of_residence: string;

  @Column({ type: 'varchar', nullable: true })
  highest_education: string;

  @Column({ type: 'varchar', nullable: true })
  hospital_name: string;

  @Column({ type: 'jsonb', nullable: true, default: {} })
  enquiry_dto: any;
}
