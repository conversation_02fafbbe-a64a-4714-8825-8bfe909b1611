import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, ValidateIf } from 'class-validator';
import { LargeEventFormType } from './large-event-form.dto';

export class LargeEventRegistrationDto {
  @ApiProperty()
  @IsNotEmpty()
  type: LargeEventFormType;

  @ApiProperty()
  @IsOptional()
  orientation_id?: number;

  @ApiProperty()
  @IsOptional()
  organization_name?: string;

  @ApiProperty()
  @IsOptional()
  organization_id?: number;

  @ApiProperty()
  @IsOptional()
  educational_qualification: string;

  @ApiProperty()
  @IsOptional()
  college_name: string;

  @ApiProperty()
  @IsOptional()
  college_id: number;

  @ApiProperty()
  @IsOptional()
  is_graduate: boolean;

  @ApiProperty()
  @IsOptional()
  graduation_year: number;

  @ApiProperty()
  @IsOptional()
  on_spot_registration: boolean;
}
