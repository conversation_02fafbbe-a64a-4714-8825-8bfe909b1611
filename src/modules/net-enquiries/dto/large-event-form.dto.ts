import {
  IsEmail,
  IsEnum,
  IsString,
  IsNotEmpty,
  ValidateIf,
  IsOptional,
  IsBoolean,
  IsNumber,
} from 'class-validator';

export enum LargeEventFormType {
  UNIVERSITY = 'university',
  CORPORATE = 'corporate',
  ENROLLED = 'enrolled',
  NOT_ENROLLED = 'not_enrolled',
}

export class LargeEventFormDto {
  @IsEnum(LargeEventFormType)
  @IsNotEmpty()
  type: LargeEventFormType;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  first_name: string;

  @IsString()
  @IsOptional()
  last_name: string;

  @IsString()
  @IsOptional()
  coming_from: string;

  @IsString()
  @IsOptional()
  contact_number: string;

  @IsEmail()
  @IsOptional()
  email: string;

  @IsString()
  @IsOptional()
  location: string;

  @ValidateIf((o) => o.type === LargeEventFormType.CORPORATE)
  @IsString()
  @IsOptional()
  organization_name: string;

  @IsNumber()
  @IsOptional()
  orientation_id: number;

  @IsNumber()
  @IsOptional()
  campaign_id: number;

  @IsNumber()
  @IsOptional()
  organization_id: number;

  @ValidateIf((o) => o.type === LargeEventFormType.NOT_ENROLLED)
  @IsString()
  educational_qualification: string;

  @ValidateIf((o) => o.type === LargeEventFormType.UNIVERSITY)
  @IsString()
  @IsOptional()
  college_name: string;

  @IsNumber()
  @IsOptional()
  college_id: number;

  @IsBoolean()
  @IsOptional()
  is_graduate: boolean;

  @IsNumber()
  @IsOptional()
  graduation_year: number;

  @IsBoolean()
  @IsOptional()
  on_spot_registration: boolean = false;

  @IsNumber()
  @IsOptional()
  course_id: number;
}
