import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NetEnquiryController } from './net-enquiry.controller';
import { NetEnquiry } from './entities/net-enquiry.entity';
import { NetEnquiryService } from './services/net-enquiry.service';
import { ProgramModule } from '../programs/program.module';
import { CampaignModule } from '../campaigns/campaign.module';
import { LeadModule } from '../leads/lead.module';
import { ContactModule } from '../contacts/contact.module';
import { WebinarModule } from '../webinars/webinar.module';
import { LeadAllocationModule } from '../lead-allocations/lead-allocation.module';
import { DummyNEService } from './services/dummy-ne-service';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadSpoc } from '@modules/leads/entities/lead-spoc.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([NetEnquiry, Lead, LeadSpoc]),
    ProgramModule,
    CampaignModule,
    forwardRef(() => LeadModule),
    ContactModule,
    forwardRef(() => WebinarModule), // <-- wrap in forwardRef
    LeadAllocationModule,
  ],
  controllers: [NetEnquiryController],
  providers: [NetEnquiryService, DummyNEService],
  exports: [NetEnquiryService, DummyNEService],
})
export class NetEnquiryModule {}
