import { CreateContactDto } from 'src/modules/contacts/dto/create-contact.dto';
import { CreateNetEnquiryDto } from '../dto/create-net-enquiry.dto';
import { CreateEmailDto } from 'src/modules/contacts/dto/create-email.dto';
import { CreatePhoneDto } from 'src/modules/contacts/dto/create-phone.dto';

export function createContactDtoFromNetEnquiry(
  netEnquiryDto: CreateNetEnquiryDto,
): CreateContactDto {
  const emailDto: CreateEmailDto = netEnquiryDto.email
    ? {
        email: netEnquiryDto.email,
        label: 'Personal',
      }
    : null;

  // Create phone DTO if phone exists
  const phoneDto: CreatePhoneDto = netEnquiryDto.phone
    ? {
        phone_number: netEnquiryDto.phone,
        country_code: netEnquiryDto.country_code || '+91',
        label: 'Mobile',
      }
    : null;

  // Create contact DTO
  const contactDto: CreateContactDto = {
    first_name: netEnquiryDto.first_name,
    last_name: netEnquiryDto.last_name,
    city: netEnquiryDto.city,
    email: emailDto,
    phone: phoneDto,
  };

  return contactDto;
}
