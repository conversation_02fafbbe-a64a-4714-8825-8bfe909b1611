import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { MilesOfficeService } from './services/miles-office.service';
import { CreateMilesOfficeDto } from './dto/create-miles-office.dto';
import { UpdateMilesOfficeDto } from './dto/update-miles-office.dto';
import { ClientAccessGuard } from '../user-clients/guards/client-access.guard';
import { MilesOffice } from './entities/miles-office.entity';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { Resource } from '@modules/permissions/enums/permission.enum';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { Public } from '@modules/auth/decorators/public.decorator';

@ApiTags('miles-offices')
@Controller('miles-offices')
@UseGuards(ClientAccessGuard)
@ApiResource(Resource.MILES_OFFICE)
@UseGuards(PermissionGuard)
export class MilesOfficeController {
  constructor(private readonly milesOfficesService: MilesOfficeService) {}

  @Post()
  @UseGuards(ClientAccessGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new miles office' })
  @ApiBody({ type: CreateMilesOfficeDto })
  async create(
    @Body() createMilesOfficeDto: CreateMilesOfficeDto,
  ): Promise<MilesOffice> {
    return this.milesOfficesService.create(createMilesOfficeDto);
  }

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get all miles offices with pagination' })
  @ApiQuery({ type: PaginationDto })
  findAll(@Query() paginationDto: PaginationDto) {
    const { page = 1, size = 10 } = paginationDto;
    Logger.log('Fetching all miles offices', 'MilesOfficeController');
    return this.milesOfficesService.findAll(page, size);
  }

  @Get('warehouses')
  @ApiOperation({ summary: 'Get all miles warehouses' })
  async getWarehouses(): Promise<MilesOffice[]> {
    return this.milesOfficesService.getWarehouses();
  }

  @Get('country/:country')
  @ApiOperation({ summary: 'Get miles offices filtered by country' })
  @ApiParam({ name: 'country', description: 'Country name or code' })
  async findByCountry(
    @Param('country') country: string,
  ): Promise<MilesOffice[]> {
    return this.milesOfficesService.findByCountry(country);
  }

  @Get('cities')
  @ApiOperation({ summary: 'Get miles offices filtered by list of cities' })
  @ApiQuery({
    name: 'cities',
    description: 'Comma-separated list of city names',
    example: 'Delhi,Mumbai,Bangalore',
  })
  async findByCities(@Query('cities') cities: string): Promise<MilesOffice[]> {
    const cityList = cities.split(',');
    return this.milesOfficesService.findByCities(cityList);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get miles office by ID' })
  @ApiParam({ name: 'id', description: 'Miles office ID' })
  async findOne(@Param('id') id: string): Promise<MilesOffice> {
    return this.milesOfficesService.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(ClientAccessGuard)
  @ApiOperation({ summary: 'Update a miles office by ID' })
  @ApiParam({ name: 'id', description: 'Miles office ID' })
  @ApiBody({ type: UpdateMilesOfficeDto })
  async update(
    @Param('id') id: string,
    @Body() updateMilesOfficeDto: UpdateMilesOfficeDto,
  ): Promise<MilesOffice> {
    return this.milesOfficesService.update(+id, updateMilesOfficeDto);
  }

  @Delete(':id')
  @UseGuards(ClientAccessGuard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a miles office by ID' })
  @ApiParam({ name: 'id', description: 'Miles office ID' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.milesOfficesService.remove(+id);
  }
}
