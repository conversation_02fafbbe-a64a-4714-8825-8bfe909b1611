import { BaseEntity } from 'src/common/entities/base.entity';
import { Entity, Column } from 'typeorm';

@Entity()
export class MilesOffice extends BaseEntity {
  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  prefix: string;

  @Column({ type: 'varchar', nullable: true })
  contact: string;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'text', nullable: true })
  location_url: string;

  @Column({
    type: 'varchar',
    nullable: true,
    comment: 'Geographic latitude coordinate of the office location',
  })
  latitude: string;

  @Column({
    type: 'varchar',
    nullable: true,
    comment: 'Geographic longitude coordinate of the office location',
  })
  longitude: string;

  @Column({ type: 'varchar', nullable: true })
  ivr: string;

  @Column({ type: 'varchar', nullable: true })
  color: string;

  @Column({ type: 'boolean', default: false })
  is_warehouse: boolean;

  @Column({ type: 'varchar', nullable: true })
  avatar: string;

  @Column({ type: 'varchar', nullable: true })
  website_flag: string;
}
