import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MilesOfficeService } from './services/miles-office.service';
import { MilesOfficeController } from './miles-office.controller';
import { UserClientModule } from '../user-clients/user-client.module';
import { MilesOffice } from './entities/miles-office.entity';
import { RoleModule } from '@modules/roles/role.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MilesOffice]),
    UserClientModule,
    RoleModule,
  ],
  controllers: [MilesOfficeController],
  providers: [MilesOfficeService],
  exports: [MilesOfficeService],
})
export class MilesOfficeModule {}
