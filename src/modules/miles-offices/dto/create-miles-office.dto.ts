import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class CreateMilesOfficeDto {
  @ApiPropertyOptional({ description: 'Address of the office' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ description: 'City where the office is located' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ description: 'Prefix code for the office' })
  @IsOptional()
  @IsString()
  prefix?: string;

  @ApiPropertyOptional({ description: 'Contact number for the office' })
  @IsOptional()
  @IsString()
  contact?: string;

  @ApiPropertyOptional({ description: 'Country where the office is located' })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({ description: 'Email address of the office' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'URL to the office location map or info',
  })
  @IsOptional()
  @IsString()
  location_url?: string;

  @ApiPropertyOptional({ description: 'Latitude coordinate of the office' })
  @IsOptional()
  @IsString()
  latitude?: string;

  @ApiPropertyOptional({ description: 'Longitude coordinate of the office' })
  @IsOptional()
  @IsString()
  longitude?: string;

  @ApiPropertyOptional({ description: 'IVR code or identifier for the office' })
  @IsOptional()
  @IsString()
  ivr?: string;

  @ApiPropertyOptional({ description: 'Color code associated with the office' })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiPropertyOptional({
    description: 'Flag to indicate if this office is a warehouse',
  })
  @IsOptional()
  @IsBoolean()
  is_warehouse?: boolean;

  @ApiPropertyOptional({
    description: 'Avatar image URL or identifier for the office',
  })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({
    description: 'Flag for website display or other purposes',
  })
  @IsOptional()
  @IsString()
  website_flag?: string;
}
