import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MilesOffice } from '../entities/miles-office.entity';
import { CreateMilesOfficeDto } from '../dto/create-miles-office.dto';
import { UpdateMilesOfficeDto } from '../dto/update-miles-office.dto';

@Injectable()
export class MilesOfficeService {
  constructor(
    @InjectRepository(MilesOffice)
    private readonly milesOfficeRepository: Repository<MilesOffice>,
  ) {}

  async create(
    createMilesOfficeDto: CreateMilesOfficeDto,
  ): Promise<MilesOffice> {
    const milesOffice = this.milesOfficeRepository.create(createMilesOfficeDto);
    return this.milesOfficeRepository.save(milesOffice);
  }

  async findAll(
    page = 1,
    size = 10,
  ): Promise<{ data: MilesOffice[]; meta: any }> {
    const skip = (page - 1) * size;

    const queryBuilder =
      this.milesOfficeRepository.createQueryBuilder('milesOffice');

    // Optional: add any filters or joins here if needed

    const [offices, total] = await queryBuilder
      .skip(skip)
      .take(size)
      .getManyAndCount();

    const totalPages = Math.ceil(total / size);

    return {
      data: offices,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: number): Promise<MilesOffice> {
    const milesOffice = await this.milesOfficeRepository.findOneBy({ id });
    if (!milesOffice) {
      throw new NotFoundException(`Miles office with ID ${id} not found`);
    }
    return milesOffice;
  }

  async update(
    id: number,
    updateMilesOfficeDto: UpdateMilesOfficeDto,
  ): Promise<MilesOffice> {
    const milesOffice = await this.findOne(id);

    // Update properties
    Object.assign(milesOffice, updateMilesOfficeDto);

    return this.milesOfficeRepository.save(milesOffice);
  }

  async remove(id: number): Promise<void> {
    const result = await this.milesOfficeRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Miles office with ID ${id} not found`);
    }
  }

  async findByCities(cities: string[]): Promise<MilesOffice[]> {
    return this.milesOfficeRepository.find({
      where: cities.map((city) => ({ city })),
    });
  }

  async findByCountry(country: string): Promise<MilesOffice[]> {
    return this.milesOfficeRepository.find({
      where: { country },
    });
  }

  async getWarehouses(): Promise<MilesOffice[]> {
    return this.milesOfficeRepository.find({
      where: { is_warehouse: true },
    });
  }
}
