import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import { MessageTemplatesService } from './message-templates.service';
import { CreateMessageTemplateDto } from './dto/create-message-template.dto';
import { UpdateMessageTemplateDto } from './dto/update-message-template.dto';
import { SendWhatsAppDto } from './dto/send-whatsapp.dto';
import { SendEmailDto } from './dto/send-email.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Message Templates')
@ApiBearerAuth()
@Controller('message-templates')
export class MessageTemplatesController {
  constructor(
    private readonly messageTemplatesService: MessageTemplatesService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new message template' })
  @ApiResponse({
    status: 201,
    description: 'Message template created successfully',
  })
  create(
    @Body() createMessageTemplateDto: CreateMessageTemplateDto,
    @Request() req,
  ) {
    return this.messageTemplatesService.create(
      createMessageTemplateDto,
      req.user.currentClientId,
      req.user.id,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all message templates' })
  @ApiResponse({ status: 200, description: 'List of message templates' })
  findAll(@Request() req) {
    return this.messageTemplatesService.findAll(req.user.currentClientId);
  }

  @Get('whatsapp-templates')
  @ApiOperation({ summary: 'Get WhatsApp templates only' })
  @ApiResponse({ status: 200, description: 'List of WhatsApp templates' })
  getWhatsAppTemplates(@Request() req) {
    return this.messageTemplatesService.getWhatsAppTemplates(
      req.user.currentClientId,
    );
  }

  @Get('email-templates')
  @ApiOperation({ summary: 'Get Email templates only' })
  @ApiResponse({ status: 200, description: 'List of Email templates' })
  getEmailTemplates(@Request() req) {
    return this.messageTemplatesService.getEmailTemplates(
      req.user.currentClientId,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a message template by ID' })
  @ApiResponse({ status: 200, description: 'Message template details' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.messageTemplatesService.findOne(+id, req.user.currentClientId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a message template' })
  @ApiResponse({
    status: 200,
    description: 'Message template updated successfully',
  })
  update(
    @Param('id') id: string,
    @Body() updateMessageTemplateDto: UpdateMessageTemplateDto,
    @Request() req,
  ) {
    updateMessageTemplateDto.id = +id;
    return this.messageTemplatesService.update(
      updateMessageTemplateDto,
      req.user.currentClientId,
      req.user.id,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a message template' })
  @ApiResponse({
    status: 200,
    description: 'Message template deleted successfully',
  })
  remove(@Param('id') id: string, @Request() req) {
    return this.messageTemplatesService.remove(
      +id,
      req.user.currentClientId,
      req.user.id,
    );
  }

  @Post('send-whatsapp')
  @ApiOperation({ summary: 'Send WhatsApp message using template' })
  @ApiResponse({
    status: 200,
    description: 'WhatsApp message sent successfully',
  })
  sendWhatsApp(@Body() sendWhatsAppDto: SendWhatsAppDto, @Request() req) {
    return this.messageTemplatesService.sendWhatsApp(
      sendWhatsAppDto,
      req.user.currentClientId,
    );
  }

  @Post('send-email')
  @ApiOperation({ summary: 'Send Email using template' })
  @ApiResponse({ status: 200, description: 'Email sent successfully' })
  sendEmail(@Body() sendEmailDto: SendEmailDto, @Request() req) {
    return this.messageTemplatesService.sendEmail(
      sendEmailDto,
      req.user.currentClientId,
    );
  }
}
