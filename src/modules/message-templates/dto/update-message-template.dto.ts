import { PartialType } from '@nestjs/mapped-types';
import { CreateMessageTemplateDto } from './create-message-template.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export class UpdateMessageTemplateDto extends PartialType(
  CreateMessageTemplateDto,
) {
  @ApiProperty({
    description: 'ID of the message template',
    example: 1,
  })
  @IsNumber()
  id: number;
}
