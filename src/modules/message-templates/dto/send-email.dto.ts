import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsEmail,
} from 'class-validator';

export class SendEmailDto {
  @ApiProperty({
    description: 'Email address to send email',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description:
      'Email subject (optional - will use template subject if not provided)',
    example: 'Welcome to our service',
    required: false,
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiProperty({
    description: 'Event name to get the template',
    example: 'lead_created',
  })
  @IsString()
  @IsNotEmpty()
  event_name: string;

  @ApiProperty({
    description: 'Variables to replace in the template',
    example: { name: '<PERSON>', company: 'ABC Corp' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;
}
