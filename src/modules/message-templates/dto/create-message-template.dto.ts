import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNotEmpty,
  IsEnum,
} from 'class-validator';
import { EventNames } from '../enums/email-templates.enums';

export class CreateMessageTemplateDto {
  @ApiProperty({
    description: 'Name of the event',
    enum: EventNames,
    example: EventNames.CALLED_POSITIVE,
  })
  @IsEnum(EventNames)
  @IsNotEmpty()
  event_name: EventNames;

  @ApiProperty({
    description: 'Definition of the template',
    example: 'Template used when a new lead is created',
    required: false,
  })
  @IsOptional()
  @IsString()
  definition?: string;

  @ApiProperty({
    description: 'Description of the template',
    example: 'This template sends a welcome message to new leads',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Email template name',
    example: 'Welcome Email Template',
    required: false,
  })
  @IsOptional()
  @IsString()
  email_template_name?: string;

  @ApiProperty({
    description: 'WhatsApp template name',
    example: 'Welcome WhatsApp Template',
    required: false,
  })
  @IsOptional()
  @IsString()
  whatsapp_template_name?: string;

  @ApiProperty({
    description: 'Email subject line',
    example: 'Welcome to {{company}}!',
    required: false,
  })
  @IsOptional()
  @IsString()
  email_subject?: string;

  @ApiProperty({
    description: 'WhatsApp message template text',
    example: 'Hello {{name}}, welcome to our service!',
    required: false,
  })
  @IsOptional()
  @IsString()
  whatsapp_message_template?: string;

  @ApiProperty({
    description: 'Email message template text',
    example: 'Dear {{name}}, thank you for joining us!',
    required: false,
  })
  @IsOptional()
  @IsString()
  email_message_template?: string;

  @ApiProperty({
    description: 'Whether this template is for WhatsApp',
    example: true,
  })
  @IsBoolean()
  is_whatsapp: boolean;

  @ApiProperty({
    description: 'Whether this template is for Email',
    example: true,
  })
  @IsBoolean()
  is_email: boolean;
}
