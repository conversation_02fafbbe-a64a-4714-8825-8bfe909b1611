import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNotEmpty,
} from 'class-validator';

export class CreateMessageTemplateDto {
  @ApiProperty({
    description: 'Name of the event',
    example: 'lead_created',
  })
  @IsString()
  @IsNotEmpty()
  event_name: string;

  @ApiProperty({
    description: 'WhatsApp message template text',
    example: 'Hello {{name}}, welcome to our service!',
    required: false,
  })
  @IsOptional()
  @IsString()
  whatsapp_message_template?: string;

  @ApiProperty({
    description: 'Email message template text',
    example: 'Dear {{name}}, thank you for joining us!',
    required: false,
  })
  @IsOptional()
  @IsString()
  email_message_template?: string;

  @ApiProperty({
    description: 'Whether this template is for WhatsApp',
    example: true,
  })
  @IsBoolean()
  is_whatsapp: boolean;

  @ApiProperty({
    description: 'Whether this template is for Email',
    example: true,
  })
  @IsBoolean()
  is_email: boolean;
}
