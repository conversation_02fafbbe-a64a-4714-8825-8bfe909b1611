import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsObject, IsOptional } from 'class-validator';

export class SendWhatsAppDto {
  @ApiProperty({
    description: 'Phone number to send WhatsApp message',
    example: '+1234567890',
  })
  @IsString()
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty({
    description: 'Event name to get the template',
    example: 'lead_created',
  })
  @IsString()
  @IsNotEmpty()
  event_name: string;

  @ApiProperty({
    description: 'Variables to replace in the template',
    example: { name: '<PERSON>', company: 'ABC Corp' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  variables?: Record<string, any>;
}
