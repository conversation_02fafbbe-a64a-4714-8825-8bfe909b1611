import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MessageTemplatesService } from './message-templates.service';
import { MessageTemplatesController } from './message-templates.controller';
import { MessageTemplate } from './entities/event-names-enum';
import { LeadModule } from '@modules/leads/lead.module';

@Module({
  imports: [TypeOrmModule.forFeature([MessageTemplate]), LeadModule],
  controllers: [MessageTemplatesController],
  providers: [MessageTemplatesService],
  exports: [MessageTemplatesService],
})
export class MessageTemplatesModule {}
