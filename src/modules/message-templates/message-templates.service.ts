import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessageTemplate } from './entities/message-template.entity';
import { CreateMessageTemplateDto } from './dto/create-message-template.dto';
import { UpdateMessageTemplateDto } from './dto/update-message-template.dto';
import { SendWhatsAppDto } from './dto/send-whatsapp.dto';
import { SendEmailDto } from './dto/send-email.dto';
import { LeadService } from '@modules/leads/services/lead.service';
import { Lead } from '@modules/leads/entities/lead.entity';

@Injectable()
export class MessageTemplatesService {
  constructor(
    @InjectRepository(MessageTemplate)
    private readonly messageTemplateRepository: Repository<MessageTemplate>,
    private readonly leadService: LeadService,
  ) {}

  async create(
    dto: CreateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = this.messageTemplateRepository.create({
      ...dto,
      client_id: clientId,
      created_by: userId,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async findAll(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: { client_id: clientId, is_active: true },
      order: { event_name: 'ASC' },
    });
  }

  async findOne(id: number, clientId: number): Promise<MessageTemplate> {
    const messageTemplate = await this.messageTemplateRepository.findOne({
      where: { id, client_id: clientId, is_active: true },
    });

    if (!messageTemplate) {
      throw new NotFoundException('Message template not found');
    }

    return messageTemplate;
  }

  async update(
    dto: UpdateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = await this.findOne(dto.id, clientId);

    Object.assign(messageTemplate, {
      event_name: dto.event_name ?? messageTemplate.event_name,
      definition: dto.definition ?? messageTemplate.definition,
      description: dto.description ?? messageTemplate.description,
      email_template_name:
        dto.email_template_name ?? messageTemplate.email_template_name,
      whatsapp_template_name:
        dto.whatsapp_template_name ?? messageTemplate.whatsapp_template_name,
      email_subject: dto.email_subject ?? messageTemplate.email_subject,
      whatsapp_message_template:
        dto.whatsapp_message_template ??
        messageTemplate.whatsapp_message_template,
      email_message_template:
        dto.email_message_template ?? messageTemplate.email_message_template,
      is_whatsapp: dto.is_whatsapp ?? messageTemplate.is_whatsapp,
      is_email: dto.is_email ?? messageTemplate.is_email,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async remove(id: number, clientId: number, userId: number): Promise<void> {
    const messageTemplate = await this.findOne(id, clientId);

    messageTemplate.is_active = false;
    messageTemplate.updated_by = userId;

    await this.messageTemplateRepository.save(messageTemplate);
  }

  async getWhatsAppTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: {
        client_id: clientId,
        is_active: true,
        is_whatsapp: true,
      },
      order: { id: 'ASC' },
    });
  }

  async getEmailTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: {
        client_id: clientId,
        is_active: true,
        is_email: true,
      },
      order: { id: 'ASC' },
    });
  }

  private replaceVariables(
    template: string,
    variables: Record<string, any> = {},
  ): string {
    let result = template;
    Object.keys(variables).forEach((key) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, variables[key] || '');
    });
    return result;
  }

  async sendWhatsApp(
    dto: SendWhatsAppDto,
    clientId: number,
  ): Promise<{ success: boolean; message: string }> {
    // Find WhatsApp template by event name
    const template = await this.messageTemplateRepository.findOne({
      where: {
        client_id: clientId,
        event_name: dto.event_name,
        is_whatsapp: true,
        is_active: true,
      },
    });

    if (!template) {
      throw new NotFoundException(
        `WhatsApp template not found for event: ${dto.event_name}`,
      );
    }

    if (!template.whatsapp_message_template) {
      throw new NotFoundException('WhatsApp message template is empty');
    }

    const lead = await this.leadService.findOne(dto.lead_id);

    if (!lead) {
      throw new NotFoundException(`Lead not found: ${dto.lead_id}`);
    }

    if (!lead.contact.primary_phone) {
      throw new NotFoundException(
        `Primary phone not found for lead: ${dto.lead_id}`,
      );
    }

    return {
      success: true,
      message: 'WhatsApp message sent successfully',
    };
  }

  getWhatsappmessageTemplate(event_name: string, lead: Lead) {
    switch (event_name) {
      case :
        return {
          lead_id: lead.id,
          first_name: lead.contact.first_name,
          uuid: lead.lead_uuid,
          email: lead.contact.primary_email?.email,
          phone: lead.contact.primary_phone?.phone_number,
          country_code: lead.contact.primary_phone?.country_code,
          event_name: event_name,
        };
      default:
        return null;
    }
  }

  async sendEmail(
    dto: SendEmailDto,
    clientId: number,
  ): Promise<{ success: boolean; message: string }> {
    // Find Email template by event name
    const template = await this.messageTemplateRepository.findOne({
      where: {
        client_id: clientId,
        event_name: dto.event_name,
        is_email: true,
        is_active: true,
      },
    });

    if (!template) {
      throw new NotFoundException(
        `Email template not found for event: ${dto.event_name}`,
      );
    }

    if (!template.email_message_template) {
      throw new NotFoundException('Email message template is empty');
    }

    // Replace variables in template
    const emailBody = this.replaceVariables(
      template.email_message_template,
      dto.variables,
    );

    // Use template subject if not provided in DTO
    const emailSubject =
      dto.subject ||
      (template.email_subject
        ? this.replaceVariables(template.email_subject, dto.variables)
        : 'No Subject');

    // TODO: Integrate with actual Email service
    // For now, just log the email
    console.log('Sending Email:', {
      to: dto.email,
      subject: emailSubject,
      body: emailBody,
    });

    return {
      success: true,
      message: 'Email sent successfully',
    };
  }
}
