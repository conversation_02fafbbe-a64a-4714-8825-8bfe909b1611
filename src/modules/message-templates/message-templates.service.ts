import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessageTemplate } from './entities/message-template.entity';
import { CreateMessageTemplateDto } from './dto/create-message-template.dto';
import { UpdateMessageTemplateDto } from './dto/update-message-template.dto';
import { SendWhatsAppDto } from './dto/send-whatsapp.dto';
import { SendEmailDto } from './dto/send-email.dto';

@Injectable()
export class MessageTemplatesService {
  constructor(
    @InjectRepository(MessageTemplate)
    private readonly messageTemplateRepository: Repository<MessageTemplate>,
  ) {}

  async create(
    dto: CreateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = this.messageTemplateRepository.create({
      ...dto,
      client_id: clientId,
      created_by: userId,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async findAll(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: { client_id: clientId, is_active: true },
      order: { event_name: 'ASC' },
    });
  }

  async findOne(id: number, clientId: number): Promise<MessageTemplate> {
    const messageTemplate = await this.messageTemplateRepository.findOne({
      where: { id, client_id: clientId, is_active: true },
    });

    if (!messageTemplate) {
      throw new NotFoundException('Message template not found');
    }

    return messageTemplate;
  }

  async update(
    dto: UpdateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = await this.findOne(dto.id, clientId);

    Object.assign(messageTemplate, {
      event_name: dto.event_name ?? messageTemplate.event_name,
      whatsapp_message_template: dto.whatsapp_message_template ?? messageTemplate.whatsapp_message_template,
      email_message_template: dto.email_message_template ?? messageTemplate.email_message_template,
      is_whatsapp: dto.is_whatsapp ?? messageTemplate.is_whatsapp,
      is_email: dto.is_email ?? messageTemplate.is_email,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async remove(id: number, clientId: number, userId: number): Promise<void> {
    const messageTemplate = await this.findOne(id, clientId);
    
    messageTemplate.is_active = false;
    messageTemplate.updated_by = userId;
    
    await this.messageTemplateRepository.save(messageTemplate);
  }

  async getWhatsAppTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: { 
        client_id: clientId, 
        is_active: true, 
        is_whatsapp: true 
      },
      order: { event_name: 'ASC' },
    });
  }

  async getEmailTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: { 
        client_id: clientId, 
        is_active: true, 
        is_email: true 
      },
      order: { event_name: 'ASC' },
    });
  }

  private replaceVariables(template: string, variables: Record<string, any> = {}): string {
    let result = template;
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, variables[key] || '');
    });
    return result;
  }

  async sendWhatsApp(dto: SendWhatsAppDto, clientId: number): Promise<{ success: boolean; message: string }> {
    // Find WhatsApp template by event name
    const template = await this.messageTemplateRepository.findOne({
      where: {
        client_id: clientId,
        event_name: dto.event_name,
        is_whatsapp: true,
        is_active: true,
      },
    });

    if (!template) {
      throw new NotFoundException(`WhatsApp template not found for event: ${dto.event_name}`);
    }

    if (!template.whatsapp_message_template) {
      throw new NotFoundException('WhatsApp message template is empty');
    }

    // Replace variables in template
    const message = this.replaceVariables(template.whatsapp_message_template, dto.variables);

    // TODO: Integrate with actual WhatsApp API
    // For now, just log the message
    console.log('Sending WhatsApp message:', {
      phone: dto.phone_number,
      message,
    });

    return {
      success: true,
      message: 'WhatsApp message sent successfully',
    };
  }

  async sendEmail(dto: SendEmailDto, clientId: number): Promise<{ success: boolean; message: string }> {
    // Find Email template by event name
    const template = await this.messageTemplateRepository.findOne({
      where: {
        client_id: clientId,
        event_name: dto.event_name,
        is_email: true,
        is_active: true,
      },
    });

    if (!template) {
      throw new NotFoundException(`Email template not found for event: ${dto.event_name}`);
    }

    if (!template.email_message_template) {
      throw new NotFoundException('Email message template is empty');
    }

    // Replace variables in template
    const emailBody = this.replaceVariables(template.email_message_template, dto.variables);

    // TODO: Integrate with actual Email service
    // For now, just log the email
    console.log('Sending Email:', {
      to: dto.email,
      subject: dto.subject,
      body: emailBody,
    });

    return {
      success: true,
      message: 'Email sent successfully',
    };
  }
}
