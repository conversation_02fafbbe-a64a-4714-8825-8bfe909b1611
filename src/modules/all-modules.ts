//? This file is used to export all modules in the project to be imported in the app.module.ts file.
import { ContactModule } from './contacts/contact.module';
import { ClientModule } from './clients/client.module';
import { UserModule } from './users/user.module';
import { UserClientModule } from './user-clients/user-client.module';
import { MilesOfficeModule } from './miles-offices/miles-office.module';
import { AuthModule } from './auth/auth.module';
import { RoleModule } from './roles/role.module';
import { PermissionModule } from './permissions/permission.module';
import { LeadModule } from './leads/lead.module';
import { ProgramModule } from './programs/program.module';
import { CampaignModule } from './campaigns/campaign.module';
import { NetEnquiryModule } from './net-enquiries/net-enquiry.module';
import { WebinarModule } from './webinars/webinar.module';
import { CacheModule } from 'src/common/cache/cache.module';
import { CallLogModule } from './call-logs/call-log.module';
import { LeadAllocationModule } from './lead-allocations/lead-allocation.module';
import { MeetAndVisitModule } from './meet-and-visit/meet-and-visit.module';
// import { GoogleServicesModule } from 'src/services/google/google-services.module';
import { LeadHistoryModule } from './lead-histories/lead-history.module';
import { AttachmentsModule } from './attachments/attachments.module';
import { DispositionModule } from './dispositions/disposition.module';
import { InterviewsModule } from './interviews/interviews.module';
import { SessionModule } from './session/session.module';
import { ApplicationsModule } from './applications/applications.module';
import { MessageTemplatesModule } from './message-templates/message-templates.module';
import { MongoLogModule } from './audit/general_log/mongo_log_module';

export const allModules = [
  // GoogleServicesModule,
  PermissionModule,
  AuthModule,
  RoleModule,
  ClientModule,
  ContactModule,
  UserModule,
  UserClientModule,
  MilesOfficeModule,
  LeadModule,
  ProgramModule,
  CampaignModule,
  NetEnquiryModule,
  WebinarModule,
  CacheModule,
  CallLogModule,
  LeadAllocationModule,
  MeetAndVisitModule,
  LeadHistoryModule,
  AttachmentsModule,
  DispositionModule,
  InterviewsModule,
  SessionModule,
  ApplicationsModule,
  MessageTemplatesModule,
  MongoLogModule,
];
