import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Program } from '../../programs/entities/program.entity';
import { Entity, Column, ManyToOne, Index, JoinColumn } from 'typeorm';
import { ZoomAccount } from '../enums/zoom.enum';

export enum EnrolStatus {
  ENROLLED = 'enrolled',
  NOT_ENROLLED = 'not_enrolled',
  NONE = 'none',
}

@Entity()
export class Webinar extends ClientAwareEntity {
  @Column({ type: 'varchar' })
  webinar_id: string;

  @Column({ type: 'varchar' })
  webinar_name: string;

  @Column({ type: 'varchar', nullable: true })
  host: string;

  @Column({ type: 'varchar' })
  title_on_website: string;

  @Column({ type: 'varchar' })
  date_string: string;

  @Column({ type: 'varchar' })
  time_string: string;

  @Column({ type: 'timestamp', nullable: true })
  date_time: Date;

  @Column({ type: 'int' })
  duration: number;

  @Column({ type: 'varchar' })
  webinar_type: string;

  // ✅ Raw foreign key column
  @Column()
  program_id: number;

  // ✅ Relation using that foreign key
  @ManyToOne(() => Program)
  @JoinColumn({ name: 'program_id' }) // binds relation to programId column
  program: Program;

  @Column({ type: 'text', nullable: true })
  tagline_1: string;

  @Column({ type: 'text', nullable: true })
  tagline_2: string;

  @Column({ type: 'text', nullable: true })
  about: string;

  @Column({ type: 'text', nullable: true })
  text_highlight: string;

  @Column({ type: 'jsonb', nullable: true })
  what_you_learn: string[];

  @Column({ type: 'integer', nullable: true })
  acc_city_id: number;

  @Column({ type: 'boolean', nullable: true })
  isOffline: boolean;

  @Column({ type: 'text', nullable: true })
  card_banner_image: string;

  @Column({
    type: 'enum',
    enum: ZoomAccount,
    nullable: true,
  })
  zoomAccount: ZoomAccount;

  @Column({
    type: 'enum',
    enum: EnrolStatus,
    nullable: true,
    default: EnrolStatus.NONE,
  })
  enrol_status: EnrolStatus;

  @Column({ type: 'boolean', nullable: true, default: false })
  auto_register: boolean;

  @Column({ type: 'boolean', nullable: true })
  is_meet_and_greet: boolean;
}
