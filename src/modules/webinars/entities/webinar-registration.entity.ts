import { Entity, ManyToOne, Column, Index } from 'typeorm';
import { Webinar } from './webinar.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Campaign } from 'src/modules/campaigns/entities/campaign.entity';
import { SourceType } from 'src/common/enums/source.enum';
import { LeadProgramInterest } from 'src/modules/leads/entities/lead-program-interest.entity';

export enum WebinarStatus {
  attended = 'attended',
  not_attended = 'not_attended',
  registered = 'registered',
  cancelled = 'cancelled',
}

export enum AttendStatus {
  pending = 'pending',
  not_attending = 'not_attending',
  confirmed = 'confirmed',
}

@Entity()
export class WebinarRegistration extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Webinar)
  webinar: Webinar;

  @Index()
  @ManyToOne(() => LeadProgramInterest, (lead) => lead.webinar_registrations)
  lead_program_interest: LeadProgramInterest;

  @Column({ type: 'text', nullable: true })
  email: string;

  @Column({ type: 'text', nullable: true })
  join_url: string;

  @Column({
    type: 'enum',
    enum: WebinarStatus,
    nullable: true,
    default: WebinarStatus.registered,
  })
  webinar_status: WebinarStatus;

  @Column({ type: 'enum', enum: SourceType, nullable: true })
  registration_source: SourceType;

  @Index()
  @ManyToOne(() => Campaign)
  campaign: Campaign;

  @Column({ type: 'timestamp with time zone', nullable: true })
  joined_at: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  left_time: Date;

  @Column({ default: false })
  in_session: boolean;

  @Column({ type: 'int', nullable: true, default: 0 })
  duration_in_seconds: number;

  @Column({
    type: 'enum',
    enum: AttendStatus,
    nullable: true,
    default: AttendStatus.pending,
  })
  attendStatus: AttendStatus;
}
