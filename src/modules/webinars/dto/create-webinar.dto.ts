import { ApiProperty } from '@nestjs/swagger/dist/decorators/api-property.decorator';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsEnum,
  IsOptional,
} from 'class-validator';
import { ZoomAccount } from '../zoom';
import { EnrolStatus } from '../entities/webinar.entity';

export class CreateWebinarDto {
  @IsNotEmpty()
  @ApiProperty()
  client_id: number;

  @IsOptional()
  @IsString()
  @ApiProperty()
  webinar_id: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  webinar_name: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  host: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  title_on_website: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  date_string: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  time_string: string;

  @IsNotEmpty()
  @ApiProperty()
  date_time: Date;

  @IsNotEmpty()
  @ApiProperty()
  duration: number;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  webinar_type: string;

  @IsNumber()
  @ApiProperty()
  course_id: number;

  @IsEnum(ZoomAccount)
  @ApiProperty()
  zoomAccount: ZoomAccount;

  @ApiProperty()
  @IsOptional()
  enrol_status: EnrolStatus;

  @ApiProperty()
  @IsOptional()
  autoRegister: boolean;

  @ApiProperty()
  @IsOptional()
  is_meet_and_greet: boolean;
}
