import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { AttendStatus } from '../entities/webinar-registration.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class UpdateWebinarStatusDto {
  @ApiProperty({
    type: 'enum',
    enum: AttendStatus,
    description: 'Webinar status enum',
  })
  @IsNotEmpty()
  @Transform(({ value, obj }) => obj.webinar_status || value)
  attend_status: AttendStatus;

  @ApiProperty({
    type: 'number',
    description: 'Optional webinar registration id for the status update',
    required: false,
  })
  @IsNotEmpty()
  webinarRegistrationId?: number;
}
