import { IsNotEmpty, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RegisterSingleLeadForWebinarDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsNotEmpty()
  @IsNumber()
  leadId: number;

  @ApiProperty({ description: 'ID of the webinar' }) // This ID is the primary key of the webnar table
  @IsNotEmpty()
  @IsNumber()
  webinarId: number;

  @ApiProperty({
    description: 'program Id for which the lead is registering for the webinar',
  })
  @IsNumber()
  @IsNotEmpty()
  programId: number;
}
