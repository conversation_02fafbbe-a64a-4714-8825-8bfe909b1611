import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RegisterLeadForWebinarsDto {
  @ApiProperty({ description: 'ID of the lead program interest' })
  @IsNotEmpty()
  @IsNumber()
  leadProgramInterestId: number;

  @ApiProperty({ description: 'ID of the client' })
  @IsNotEmpty()
  @IsNumber()
  clientId: number;

  @ApiPropertyOptional({ description: 'Optional ID of the campaign' })
  @IsOptional()
  @IsNumber()
  campaignId?: number;

  @ApiPropertyOptional({ description: 'Optional ID of the program' })
  @IsOptional()
  @IsNumber()
  programId?: number;
}
