import { Injectable, Logger } from '@nestjs/common';
import { WebinarService } from './webinar.service';
import { Webinar } from '../entities/webinar.entity';
import { WebinarRegistration } from '../entities/webinar-registration.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { LeadService } from '@modules/leads/services/lead.service';

@Injectable()
export class WebinarLeadService {
  private readonly logger = new Logger(WebinarService.name);
  constructor(
    @InjectRepository(Webinar)
    private readonly webinarRepository: Repository<Webinar>,
    @InjectRepository(WebinarRegistration)
    private readonly webinarRegistrationRepository: Repository<WebinarRegistration>,
    private readonly leadService: LeadService,
  ) {}

  async findUpcomingWebinarsForLead(
    lead_id: number,
  ): Promise<{ data: any[]; meta: any }> {
    this.logger.log(`Fetching upcoming webinars for lead ID: ${lead_id}`);
    const leadDetails = await this.leadService.findOne(lead_id);

    // Fetch all upcoming active webinars
    const allUpcomingWebinars = await this.webinarRepository.find({
      where: {
        is_active: true,
        date_time: MoreThan(new Date()),
      },
      order: { date_time: 'ASC' }, // ASC to show nearest first
    });

    // Fetch registrations for this lead
    const registeredWebinars = await this.webinarRegistrationRepository.find({
      where: {
        lead_program_interest: {
          lead: { id: lead_id },
          client_id: leadDetails.client_id,
        },
        webinar: {
          date_time: MoreThan(new Date()),
          is_active: true,
        },
      },
      relations: {
        webinar: true,
      },
    });

    // Create a map of webinar IDs to their registration details
    const registrationMap = new Map(
      registeredWebinars.map((registration) => [
        registration.webinar.id,
        registration,
      ]),
    );

    // Combine both sets with registration details where applicable
    const combinedWebinars = allUpcomingWebinars.map((webinar) => {
      const registration = registrationMap.get(webinar.id) || null;
      return {
        ...webinar,
        registration: registration,
        is_registered: !!registration,
      };
    });

    this.logger.log(
      `Found ${combinedWebinars.length} upcoming webinars (${registeredWebinars.length} registered) for lead ID: ${lead_id}`,
    );

    return {
      data: combinedWebinars,
      meta: {
        lead_id: lead_id,
        total: combinedWebinars.length,
        registered: registeredWebinars.length,
        unregistered: combinedWebinars.length - registeredWebinars.length,
      },
    };
  }
}
