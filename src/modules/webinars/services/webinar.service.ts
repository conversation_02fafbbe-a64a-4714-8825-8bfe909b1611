import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON>Than, <PERSON>Than } from 'typeorm';
import { Webinar } from '../entities/webinar.entity';
import {
  WebinarRegistration,
  WebinarStatus,
} from '../entities/webinar-registration.entity';
import { LeadProgramInterestService } from '../../leads/services/lead-program-interest.service';
import { RegisterLeadForWebinarsDto } from '../dto/register-lead-for-webinars.dto';
import { SourceType } from 'src/common/enums/source.enum';
import { CampaignService } from 'src/modules/campaigns/services/campaign.service';
import { CreateWebinarDto } from '../dto/create-webinar.dto';
import { ProgramService } from '@modules/programs/services/program.service';
import { UpdateWebinarDto } from '../dto/update-webinar.dto';
import { zonedTimeToUtc } from 'date-fns-tz';
import getToken, { ZoomAccount } from '../zoom';
import { CreateNetEnquiryDto } from '@modules/net-enquiries/dto/create-net-enquiry.dto';
import { InboundCallbackService } from '@modules/callbacks/inbounds/services/inbound.callback.service';
import { registerLeadToZoomWebinar } from 'src/common/utils/register_for_zoom';
import { OutboundCallbackService } from '@modules/callbacks/outbounds/services/outbound.callback.service';
import { NetEnquiryService } from '@modules/net-enquiries/services/net-enquiry.service';
import { Campaign } from '@modules/campaigns/entities/campaign.entity';
import { LeadService } from '@modules/leads/services/lead.service';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { RegisterSingleLeadForWebinarDto } from '../dto/register-single-lead-for-webinar.dto';
import { UpdateWebinarStatusDto } from '../dto/update-webinar-status.dto';
import { UserService } from '@modules/users/services/user.service';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  EngagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';

@Injectable()
export class WebinarService {
  private readonly logger = new Logger(WebinarService.name);
  constructor(
    @InjectRepository(Webinar)
    private readonly webinarRepository: Repository<Webinar>,
    @InjectRepository(WebinarRegistration)
    private readonly webinarRegistrationRepository: Repository<WebinarRegistration>,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly campaignService: CampaignService,
    private readonly programService: ProgramService,
    @Inject(forwardRef(() => InboundCallbackService))
    private inboundCallbackService: InboundCallbackService,
    @Inject(forwardRef(() => LeadService))
    private leadService: LeadService,
    private readonly outboundCallbackService: OutboundCallbackService,
    private readonly netEnquiryService: NetEnquiryService,
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  async findByZoomId(zoomId: string): Promise<Webinar | null> {
    return this.webinarRepository.findOne({
      where: { webinar_id: zoomId },
    });
  }

  async handleWebinarJoin(
    participantEmail: string,
    webinarId: string | number,
    zoomAccount: ZoomAccount,
  ) {
    console.log('participantEmail in handleWebinarJoin', participantEmail);
    console.log('webinarId in handleWebinarJoin', webinarId);
    console.log('zoomAccount in handleWebinarJoin', zoomAccount);

    try {
      this.logger.log(
        `Handling webinar join event for participant: ${participantEmail}, Webinar ID: ${webinarId}`,
      );
      const registration = await this.webinarRegistrationRepository.findOne({
        where: {
          email: participantEmail,
          webinar: {
            webinar_id: String(webinarId),
          },
        },
        relations: {
          webinar: { program: true },
          // lead: {
          //   programs: { level: true },
          //   phones: true,
          //   spoc: true,
          // },
          lead_program_interest: {
            lead: {
              contact: {
                primary_phone: true,
              },
              owner_spoc: true,
            },
            lead_level: true, // If you want the lead_level info from LeadProgramInterest
            program: true, // Also, if you want the program info of LeadProgramInterest
          },
          campaign: true,
        },
      });

      if (!registration) {
        throw new Error('Webinar registration not found');
      }

      this.logger.log(
        `Registration found for participant: ${participantEmail}, updating webinar status to 'attended'`,
      );

      registration.webinar_status = WebinarStatus.attended;
      registration.joined_at = new Date();
      registration.left_time = null;
      registration.in_session = true;
      await this.webinarRegistrationRepository.save(registration);

      this.logger.log(
        `Webinar registration updated for participant: ${participantEmail}`,
      );
      try {
        console.log(
          'sending registeration payload to webinarAttended  callback service',
          registration,
        );
        this.inboundCallbackService.onWebinarAttended(registration);
      } catch (error) {
        this.logger.error(
          `Error in inbound callback for participant: ${participantEmail}`,
          error.stack,
        );
        console.error(
          `Error in inbound callback for participant: ${participantEmail}`,
          error,
        );
      }

      //create a net enquiry after the user has joined the webinar
      const data: CreateNetEnquiryDto = {
        first_name:
          registration?.lead_program_interest?.lead.contact?.first_name,
        last_name: registration?.lead_program_interest?.lead.contact?.last_name,
        email: participantEmail,
        phone:
          registration?.lead_program_interest?.lead.contact?.primary_phone
            ?.phone_number,
        country_code: undefined,
        campaign_id: 1345,
        whatsapp_opt_in: true,
        coming_from: 'Webinar Join',
        program_id: registration?.lead_program_interest?.program_id,
        register_for_webinar: false,
        clevertap_id: null,
        level: null,
        spoc_email: null,
        large_event_registration_info: null,
        city: null,
        campaign_tk: null,
        gcl_id: null,
        intrested_to_work_in_us: false,
        linked_in_url: null,
        page_full_url: null,
        url_page_path: null,
        education_qualification: null,
        isFromWelcomeBack: false,
        interested_webinar_id: null,
        call_opt_in: true,
        email_opt_in: true,
        sms_opt_in: true,
        year_of_graduation: null,
        // interested_for_work_in_us: InterestedToWorkInUs.NO,
        company_website_url: null,
        state: null,
        nationality: null,
        country_of_residence: null,
        highest_education: null,
        hospital_name: null,
        designation: null,
        organisation: null,
      };

      console.log('Creating net enquiry for participant:', data);

      await this.netEnquiryService.create(data);

      this.logger.log(
        `Net enquiry created for participant: ${participantEmail}`,
      );

      try {
        this.logger.log(
          `Creating lead history for webinar attendence: ${registration?.id}`,
        );
        this.createWebinarLeadHistory({
          registration: registration,
          userId: null,
        });
      } catch (err) {
        this.logger.error(
          'Error in creating lead history for user attendance joined',
          err.message,
        );
        console.error(
          'XLRI:: Facing error in creating lead history for user attendance joined',
          err.message,
        );
      }
    } catch (error) {
      Logger.error('Error handling webinar join event:', error);
      throw new HttpException(
        'Failed to process webinar join event',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async handleWebinarLeft(
    participantEmail: string,
    webinarId: string | number,
  ) {
    this.logger.log(
      `Handling webinar left event for participant: ${participantEmail}, Webinar ID: ${webinarId}`,
    );
    try {
      const registration = await this.webinarRegistrationRepository.findOne({
        where: {
          email: participantEmail,
          webinar: {
            webinar_id: String(webinarId),
          },
        },
        relations: {
          webinar: { program: true },
          // lead: {
          //   programs: { level: true },
          //   phones: true,
          //   spoc: true,
          // },
          lead_program_interest: {
            lead: {
              contact: {
                primary_phone: true,
              },
              owner_spoc: true,
            },
            lead_level: true, // If you want the lead_level info from LeadProgramInterest
            program: true, // Also, if you want the program info of LeadProgramInterest
          },
          campaign: true,
        },
      });

      if (!registration) {
        this.logger.error(
          `Webinar registration not found for email: ${participantEmail} and Webinar ID: ${webinarId}`,
        );
        throw new Error('Webinar registration not found');
      }

      console.log('registration in handleWebinarLeft', registration);

      if (registration?.in_session) {
        const now = new Date();

        registration.left_time = now;
        registration.in_session = false;

        if (registration?.joined_at) {
          const sessionDurationInMs =
            now.getTime() - registration?.joined_at?.getTime();
          const sessionDurationInSeconds = Math.floor(
            sessionDurationInMs / 1000,
          );
          registration.duration_in_seconds =
            (registration?.duration_in_seconds || 0) + sessionDurationInSeconds;
        }

        await this.webinarRegistrationRepository.save(registration);
        this.logger.log(
          `Webinar left time recorded for participant: ${participantEmail}`,
        );
      }

      try {
        //TODO Create Lead History for Webinar Participant Left, Refer Old MF if needed
      } catch (err) {
        this.logger.error(
          'Error in creating lead history for user attendance left',
          err.message,
        );
        console.error(
          'XLRI:: Facing error in creating lead history for user attendance left',
          err.message,
        );
      }
    } catch (error) {
      this.logger.error('Error handling webinar left event:', error);
      throw new HttpException(
        'Failed to process webinar left event',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async handleWebinarEnd(zoomWebinarId: string) {
    this.logger.log(
      `Handling webinar end event for Zoom Webinar ID: ${zoomWebinarId}`,
      'WebinarService',
    );

    console.log(
      `Start processing webinar end event for Zoom Webinar ID: ${zoomWebinarId}`,
    );

    //TODO Create a log entry for webinar End, Refer Old MF if needed
    // const webinarData = new this.webinarLogModel({ data: {} });

    try {
      const webinar = await this.webinarRepository.findOne({
        where: {
          webinar_id: zoomWebinarId,
        },
      });

      if (!webinar) {
        this.logger.error(
          `Webinar with Zoom ID ${zoomWebinarId} not found`,
          'WebinarService',
        );
        throw new NotFoundException(
          `Webinar with Zoom ID ${zoomWebinarId} not found`,
        );
      }

      const registrations = await this.webinarRegistrationRepository.find({
        where: {
          webinar: { id: webinar.id },
          lead_program_interest: {
            lead: {
              contact: {
                primary_phone: true,
              },
              owner_spoc: true,
            },
            lead_level: true, // If you want the lead_level info from LeadProgramInterest
            program: true, // Also, if you want the program info of LeadProgramInterest
          },
        },
      });

      console.log('registrations in handleWebinarEnd', registrations);

      registrations.forEach(async (registration) => {
        // Use the spread operator to copy the properties from registration to registrationData
        let registrationData: WebinarRegistration = new WebinarRegistration();

        // Spread the properties from registration into the new WebinarRegistration instance
        registrationData = {
          ...registrationData, // Spread any existing properties of the entity (if necessary)
          in_session: false, // Override with custom property
          // You can add or modify other properties as needed here
        };

        if (!registration?.joined_at) {
          console.log(
            'not registration.joined_at in handleWebinarEnd',
            registration.joined_at,
          );
          registration.webinar_status = WebinarStatus.not_attended;
          if (webinar.is_meet_and_greet == true) {
            this.logger.log(
              `Handling meet-and-greet for participant: ${registration.id}`,
            );

            //TODO Handle Level change if needed

            registrationData = {
              ...registrationData,
              webinar_status: WebinarStatus.not_attended,
            };
          }
        } else if (registration.joined_at) {
          console.log(
            'registration.joined_at in handleWebinarEnd',
            registration.joined_at,
          );
          registration.webinar_status = WebinarStatus.attended;
          if (webinar.is_meet_and_greet == true) {
            this.logger.log(
              `Handling meet-and-greet for participant: ${registration.id}`,
            );
            //TODO Auto Push TO ATS if needed
          }
          registrationData = {
            ...registrationData,
            webinar_status: WebinarStatus.attended,
          };
        }
        await this.webinarRegistrationRepository.save(registration); // Save the updated registration
        console.log('registraion saved');
        //TODO Store registration Data as history in MongoDB
      });
    } catch (err) {
      this.logger.error('Error in handling webinar end event:', err);
      throw new HttpException(
        'Failed to process webinar end event',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async createZoomWebinar(dto: CreateWebinarDto, user: any) {
    const zoomApiUrl = 'https://api.zoom.us/v2/users/me/webinars';

    const token = await getToken(dto.zoomAccount || ZoomAccount.ACCOUNT_7);

    const webinarData = {
      topic: dto.webinar_name,
      type: 5,
      start_time: new Date(dto.date_time).toISOString(),
      duration: dto.duration,
      timezone: 'Asia/Kolkata',
      agenda: dto.title_on_website,
      settings: {
        host_video: true,
        panelists_video: true,
        participant_video: true,
        mute_upon_entry: true,
        approval_type: 0,
        registrants_email_notification: true,
        attendees_and_panelists_reminder_email_notification: {
          enable: true,
          type: 3, // to send reminder emails 1 hour and 1 day before the webinar
        },
      },
    };
    const response = await fetch(zoomApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(webinarData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to create Zoom webinar: ${errorData.message}`);
    }

    return response;
  }

  async create(dto: CreateWebinarDto, user: any) {
    Logger.log('Creating webinar with data:', dto);
    const webinar = new Webinar();
    const course = await this.programService.findOneCourse(dto.course_id);
    webinar.status = true;
    webinar.program_id = course.id;
    webinar.host = dto.host;
    webinar.created_by = user.id;
    webinar.date_string = dto.date_string;
    const timeZone = 'Asia/Kolkata';
    const utcDate = zonedTimeToUtc(new Date(dto.date_time), timeZone);
    webinar.date_time = utcDate;
    webinar.duration = dto.duration;
    webinar.time_string = dto.time_string;
    webinar.title_on_website = dto.title_on_website;
    // webinar.webinar_id = dto.webinar_id;
    webinar.webinar_name = dto.webinar_name;
    webinar.webinar_type = dto.webinar_type;
    webinar.zoomAccount = dto.zoomAccount;
    webinar.auto_register = dto.autoRegister;
    webinar.enrol_status = dto.enrol_status;
    webinar.is_meet_and_greet = dto.is_meet_and_greet;
    webinar.client_id = dto.client_id;

    // Create a Zoom webinar

    try {
      Logger.log('Creating Zoom webinar with data:', dto);
      const zoomResponse = await this.createZoomWebinar(dto, user);

      const zoomData = await zoomResponse.json();

      Logger.log('Zoom Webinar Created:', zoomData);

      webinar.webinar_id = zoomData.id;
    } catch (error) {
      Logger.error('Error creating Zoom webinar:', error);
      throw new HttpException(
        `Failed to create Zoom webinar: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    return await this.webinarRepository.manager.save(webinar);
  }

  async update(dto: UpdateWebinarDto, user: any) {
    const webinar = await this.webinarRepository.findOne({
      where: { id: dto.id },
    });
    const course = await this.programService.findOneCourse(dto.course_id);
    webinar.status = true;
    webinar.program_id = course.id;
    webinar.updated_by = user.id;
    webinar.host = dto.host;
    webinar.date_string = dto.date_string;
    webinar.date_time = dto.date_time;
    webinar.duration = dto.duration;
    webinar.time_string = dto.time_string;
    webinar.title_on_website = dto.title_on_website;
    webinar.webinar_id = dto.webinar_id;
    webinar.webinar_name = dto.webinar_name;
    webinar.webinar_type = dto.webinar_type;
    webinar.enrol_status = dto.enrol_status;
    return await this.webinarRepository.manager.save(webinar);
  }

  async remove(id: number, user: any) {
    const webinar = await this.webinarRepository.findOne({ where: { id: id } });
    webinar.status = false;
    webinar.updated_by = user.id;
    return await this.webinarRepository.manager.save(webinar);
  }

  findOne(id: number) {
    return this.webinarRepository.findOne({ where: { id: id } });
  }

  async findAllUpcomingWebinars(
    clientId: number,
    programId?: number,
  ): Promise<Webinar[]> {
    const today = new Date();

    const queryConditions: any = {
      client_id: clientId,
      date_time: MoreThan(today),
    };

    // Add program filter if programId is provided
    if (programId) {
      queryConditions.program_id = programId;
    }

    return this.webinarRepository.find({
      where: queryConditions,
      relations: ['program'],
      order: {
        date_time: 'ASC',
      },
    });
  }

  async findAllUpcomingAutoRegisterWebinars(
    clientId: number,
    programId?: number,
  ): Promise<Webinar[]> {
    console.log('client id in findAllUpcomingAutoRegisterWebinars', clientId);
    console.log('program id in findAllUpcomingAutoRegisterWebinars', programId);

    const today = new Date();

    const queryConditions: any = {
      client_id: clientId,
      date_time: MoreThan(today),
      auto_register: true,
    };

    // Add program filter if programId is provided
    if (programId) {
      queryConditions.program_id = programId;
    }

    return this.webinarRepository.find({
      where: queryConditions,
      relations: ['program'],
      order: {
        date_time: 'ASC',
      },
    });
  }

  async registerLeadForAllUpcomingWebinars(
    registerDto: RegisterLeadForWebinarsDto,
    userId?: number,
  ): Promise<{ registered: number; webinars: WebinarRegistration[] }> {
    const { leadProgramInterestId, clientId, programId } = registerDto;
    this.logger.log(
      `Starting webinar registration for LeadProgramInterest ID: ${leadProgramInterestId}`,
    );

    console.log(
      'user id  in registerr lead for all upcomingwebinars is',
      userId,
    );

    // Get the lead program interest
    this.logger.log(
      `Fetching LeadProgramInterest with ID: ${leadProgramInterestId}`,
    );
    const leadProgramInterest = await this.leadProgramInterestService.findOne(
      leadProgramInterestId,
    );

    if (!leadProgramInterest) {
      this.logger.error(
        `Lead Program Interest with ID ${leadProgramInterestId} not found`,
      );
      throw new NotFoundException(
        `Lead Program Interest with ID ${leadProgramInterestId} not found`,
      );
    }
    this.logger.log(
      `Lead Program Interest found for lead ID: ${leadProgramInterest.lead.id}`,
    );

    // Get upcoming webinars to auto-register
    this.logger.log(
      `Fetching upcoming auto-register webinars for clientId: ${clientId}, programId: ${programId}`,
    );
    const upcomingWebinars = await this.findAllUpcomingAutoRegisterWebinars(
      clientId,
      programId,
    );

    if (upcomingWebinars.length === 0) {
      this.logger.log('No upcoming webinars found for auto-registration');
      return { registered: 0, webinars: [] };
    }
    this.logger.log(
      `Found ${upcomingWebinars.length} upcoming webinars for auto-registration`,
    );

    const registrations: WebinarRegistration[] = [];

    for (const webinar of upcomingWebinars) {
      this.logger.log(
        `Processing webinar ID: ${webinar.id}, Title: ${webinar.title_on_website}`,
      );

      // Check if already registered
      const existingRegistration =
        await this.webinarRegistrationRepository.findOne({
          where: {
            webinar: { id: webinar.id },
            lead_program_interest: { id: leadProgramInterestId },
            client_id: clientId,
          },
        });

      if (existingRegistration) {
        this.logger.log(
          `Lead already registered for webinar ID ${webinar.id}, skipping`,
        );
        continue;
      }

      this.logger.log(`Fetching campaign with ID: ${registerDto.campaignId}`);
      const campaign = await this.campaignService.findOne(
        registerDto.campaignId,
      );
      this.logger.log(`Campaign fetched: ${campaign?.campaign_name ?? 'N/A'}`);

      // Create new registration
      const registration = this.webinarRegistrationRepository.create({
        webinar,
        lead_program_interest: leadProgramInterest,
        campaign,
        client_id: clientId,
        webinar_status: WebinarStatus.registered,
        registration_source: SourceType.MILES_FORCE,
        created_by: userId,
      });

      const email = leadProgramInterest.lead.contact?.primary_email?.email;
      const first_name = leadProgramInterest.lead?.contact?.first_name;
      const last_name = leadProgramInterest.lead?.contact?.last_name;
      this.logger.log(
        `Registering lead (email: ${email}) to Zoom webinar ID: ${webinar.id}`,
      );

      const registrationBody = { email };
      const lead = { first_name, last_name };

      let zoomResponse;
      try {
        zoomResponse = await registerLeadToZoomWebinar(
          webinar,
          registrationBody,
          lead,
        );
        this.logger.log(`Received Zoom join URL for webinar ID ${webinar.id}`);
      } catch (zoomError) {
        this.logger.error(
          `Error registering lead to Zoom webinar: ${zoomError.message}`,
        );
        continue; // skip to next webinar
      }

      registration.join_url = zoomResponse.join_url;
      registration.email = registrationBody.email;
      registration.created_by = userId;

      console.log('zoom registrationBody', registrationBody);

      const savedRegistration =
        await this.webinarRegistrationRepository.save(registration);
      this.logger.log(`Saved webinar registration ID: ${savedRegistration.id}`);

      registrations.push(savedRegistration);

      try {
        await this.outboundCallbackService.sendWebinarRegistrationWebhook(
          savedRegistration,
        );

        Logger.log(
          `Webhook notification sent for webinar registration: ${savedRegistration.id}`,
          'WebinarService',
        );

        //TODO Log success to MongoDB or any logging service
      } catch (error) {
        //TODO Log error to MongoDB or any logging service

        Logger.error(
          `Error sending webhook notification for webinar registration: ${error.message}`,
          'WebinarService',
        );
      }

      this.logger.log(
        `Creating lead history for webinar registration: ${savedRegistration.id}`,
      );
      this.createWebinarLeadHistory({
        registration: savedRegistration,
        userId: userId,
        webinar: webinar,
      });
    }

    return {
      registered: registrations.length,
      webinars: registrations,
    };
  }

  async findWebinarRegistrationsForLead(
    leadProgramInterestId: number,
    clientId: number,
  ): Promise<WebinarRegistration[]> {
    return this.webinarRegistrationRepository.find({
      where: {
        lead_program_interest: { id: leadProgramInterestId },
        client_id: clientId,
      },
      relations: ['webinar'],
      order: {
        created_at: 'DESC',
      },
    });
  }

  async findWebinarRegistrationsForLeadWithUpcomingWebinars(
    leadId: number,
  ): Promise<PaginatedResponse<WebinarRegistration>> {
    const today = new Date();

    const leadDetails = await this.leadService.findOne(leadId);
    if (!leadDetails) {
      throw new NotFoundException(`Lead with ID ${leadId} not found`);
    }
    const webinarRegistrations = await this.webinarRegistrationRepository.find({
      where: {
        lead_program_interest: { lead: { id: leadId } },
        client_id: leadDetails.client_id,
        webinar: { date_time: LessThan(today) },
      },
      relations: ['webinar'],
      order: {
        webinar: {
          date_time: 'DESC',
        },
      },
    });

    return {
      data: webinarRegistrations,
      meta: {
        total: webinarRegistrations.length,
        page: 1,
        size: webinarRegistrations.length,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };
  }

  async registerLeadForWebinar(
    dto: RegisterSingleLeadForWebinarDto,
    userId: number,
  ): Promise<WebinarRegistration> {
    try {
      const webinar = await this.webinarRepository.findOne({
        where: { id: dto.webinarId },
        relations: ['program'],
      });

      if (!webinar) {
        throw new NotFoundException(`Webinar not found`);
      }

      const leadProgramInterest =
        await this.leadProgramInterestService.findByLeadIdAndProgramId(
          dto.leadId,
          dto.programId,
        );

      if (!leadProgramInterest) {
        throw new NotFoundException(
          `Lead Program Interest not found for lead ID ${dto.leadId} and program ID ${dto.programId}`,
        );
      }

      // Check if already registered
      const existingRegistration =
        await this.webinarRegistrationRepository.findOne({
          where: {
            webinar: { id: webinar.id },
            lead_program_interest: { lead_id: dto.leadId },
          },
        });

      if (existingRegistration) {
        throw new HttpException(
          'Lead is already registered for this webinar',
          HttpStatus.CONFLICT,
        );
      }

      const email = leadProgramInterest.lead.contact?.primary_email?.email;
      const first_name = leadProgramInterest.lead?.contact?.first_name;
      const last_name = leadProgramInterest.lead?.contact?.last_name;
      this.logger.log(
        `Registering lead (email: ${email}) to Zoom webinar ID: ${webinar.id}`,
      );

      const registrationBody = { email };
      const lead = { first_name, last_name };

      let zoomResponse;
      try {
        zoomResponse = await registerLeadToZoomWebinar(
          webinar,
          registrationBody,
          lead,
        );
        this.logger.log(`Received Zoom join URL for webinar ID ${webinar.id}`);
      } catch (zoomError) {
        this.logger.error(
          `Error registering lead to Zoom webinar: ${zoomError.message}`,
        );
        throw new HttpException(
          `Failed to register lead for Zoom webinar: ${zoomError.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }
      const register = await this.webinarRegistrationRepository.save({
        webinar: webinar,
        lead_program_interest: leadProgramInterest,
        client_id: webinar.client_id,
        webinar_status: WebinarStatus.registered,
        registration_source: SourceType.MILES_FORCE,
        join_url: zoomResponse.join_url,
        email: registrationBody.email,
        created_by: userId,
      });

      this.logger.log(`Saved webinar registration ID: ${register.id}`);

      // Create lead history for webinar registration
      this.logger.log(
        `Creating lead history for webinar registration: ${register.id}`,
      );
      this.createWebinarLeadHistory({
        registration: register,
        userId: userId,
        webinar: webinar,
      });

      return register;
    } catch (error) {
      throw new HttpException(
        `Error registering lead for webinar: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async updateWebinarStatus(
    dto: UpdateWebinarStatusDto,
  ): Promise<WebinarRegistration> {
    try {
      if (!dto.webinarRegistrationId) {
        throw new HttpException(
          'Either WebinarRegistrationId must be provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Updating the registration status
      await this.webinarRegistrationRepository.update(
        { id: dto.webinarRegistrationId },
        {
          attendStatus: dto.attend_status,
        },
      );

      // Return the updated registration
      return await this.webinarRegistrationRepository.findOne({
        where: { id: dto.webinarRegistrationId },
      });
    } catch (error) {
      throw new HttpException(
        `Error updating webinar status: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Creates a lead history entry for webinar-related events
   */
  private async createWebinarLeadHistory({
    registration,
    userId,
    webinar,
  }: {
    registration: WebinarRegistration;
    userId?: number | null;
    webinar?: Webinar | null;
  }) {
    const targetWebinar = webinar || registration?.webinar;
    const leadProgramInterest = registration?.lead_program_interest;

    return this.leadHistoryService.createLeadHistory({
      action: ActionType.ENGAGEMENT,
      subAction: EngagementSubActionType.WEBINAR,
      leadId: leadProgramInterest?.lead_id,
      contactId: leadProgramInterest?.lead?.contact_id,
      performedByUser: userId ? await this.userService.findOne(userId) : null,
      performedBy: userId,
      details: {
        webinarRegistrationId: registration?.id,
        webinarTitle: targetWebinar?.webinar_name,
        eventDate: targetWebinar?.date_time.toISOString(),
        visitStatus: registration?.webinar_status,
        program: leadProgramInterest?.program?.name,
        host: targetWebinar?.host,
        source: SourceType.MILES_FORCE,
        date: targetWebinar?.date_time.toISOString(),
        joinUrl: registration?.join_url,
      },
    });
  }
}
