import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Webinar } from './entities/webinar.entity';
import { WebinarRegistration } from './entities/webinar-registration.entity';
import { WebinarService } from './services/webinar.service';
import { LeadModule } from '../leads/lead.module';
import { WebinarController } from './webinar.controller';
import { CampaignModule } from '../campaigns/campaign.module';
import { Program } from '@modules/programs/entities/program.entity';
import { ProgramModule } from '@modules/programs/program.module';
import { CallbackModule } from '@modules/callbacks/callback.module';
import { Campaign } from '@modules/campaigns/entities/campaign.entity';
import { NetEnquiryModule } from '@modules/net-enquiries/net-enquiry.module';
import { WebinarLeadService } from './services/webinar-lead.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Webinar, WebinarRegistration, Program, Campaign]),
    forwardRef(() => LeadModule),
    CampaignModule,
    ProgramModule,
    CallbackModule,
    forwardRef(() => NetEnquiryModule),
  ],
  controllers: [WebinarController],
  providers: [WebinarService, WebinarLeadService],
  exports: [WebinarService, WebinarLeadService],
})
export class WebinarModule {}
