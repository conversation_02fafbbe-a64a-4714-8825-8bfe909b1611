import { Buffer } from 'buffer';
import * as dotenv from 'dotenv';
dotenv.config({ path: `.env.${process.env.STAGE || 'development'}` });

// These account names are u\tify the Zoom account that the webinar belongs to, and the callback urls too
export enum ZoomAccount {
  ACCOUNT_1 = '<EMAIL>',
  ACCOUNT_2 = '<EMAIL>',
  ACCOUNT_3 = '<EMAIL>',
  ACCOUNT_4 = '<EMAIL>',
  ACCOUNT_5 = '<EMAIL>',
  ACCOUNT_6 = '<EMAIL>',
  ACCOUNT_7 = '<EMAIL>',
}

// Configuration object for each account
export const ZOOM_CONFIGS = {
  [ZoomAccount.ACCOUNT_1]: {
    accountId: process.env.ZOOM_ACCOUNT_1_ID,
    clientId: process.env.ZOOM_ACCOUNT_1_CLIENT_ID,
    clientSecret: process.env.ZOOM_ACCOUNT_1_CLIENT_SECRET,
    webhookToken: process.env.ZOOM_ACCOUNT_1_WEBHOOK_TOKEN,
  },
  [ZoomAccount.ACCOUNT_2]: {
    accountId: process.env.ZOOM_ACCOUNT_2_ID,
    clientId: process.env.ZOOM_ACCOUNT_2_CLIENT_ID,
    clientSecret: process.env.ZOOM_ACCOUNT_2_CLIENT_SECRET,
    webhookToken: process.env.ZOOM_ACCOUNT_2_WEBHOOK_TOKEN,
  },
  [ZoomAccount.ACCOUNT_3]: {
    accountId: process.env.ZOOM_ACCOUNT_3_ID,
    clientId: process.env.ZOOM_ACCOUNT_3_CLIENT_ID,
    clientSecret: process.env.ZOOM_ACCOUNT_3_CLIENT_SECRET,
    webhookToken: process.env.ZOOM_ACCOUNT_3_WEBHOOK_TOKEN,
  },
  [ZoomAccount.ACCOUNT_7]: {
    accountId: process.env.ZOOM_ACCOUNT_7_ID,
    clientId: process.env.ZOOM_ACCOUNT_7_CLIENT_ID,
    clientSecret: process.env.ZOOM_ACCOUNT_7_CLIENT_SECRET,
    webhookToken: process.env.ZOOM_ACCOUNT_7_WEBHOOK_TOKEN,
  },
};

// Modified getToken function to handle multiple accounts
async function getToken(
  account: ZoomAccount = ZoomAccount.ACCOUNT_7,
): Promise<string> {
  const config = ZOOM_CONFIGS[account];
  if (!config) {
    throw new Error(`Invalid Zoom account: ${account}`);
  }

  const url = 'https://zoom.us/oauth/token';
  const credentials = Buffer.from(
    `${config.clientId}:${config.clientSecret}`,
  ).toString('base64');

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${credentials}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=account_credentials&account_id=' + config.accountId,
    });

    if (!response.ok) {
      throw new Error(`Failed to get Zoom token: ${response.statusText}`);
    }
    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error(`Error getting Zoom token for account ${account}:`, error);
    throw error;
  }
}

// Add a helper function to verify webhook tokens
export function verifyZoomWebhook(token: string): ZoomAccount | null {
  for (const [account, config] of Object.entries(ZOOM_CONFIGS)) {
    if (config.webhookToken === token) {
      return account as ZoomAccount;
    }
  }
  return null;
}

export default getToken;
