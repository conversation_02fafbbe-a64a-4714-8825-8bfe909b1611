import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsInt,
  IsString,
  IsOptional,
} from 'class-validator';

export class RescheduleMeetAndVisitDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  meeting_id: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  new_start_time: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  new_end_time: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  comment: string;
}
