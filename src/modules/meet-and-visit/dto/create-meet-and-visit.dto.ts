import {
  IsOptional,
  IsInt,
  IsEnum,
  IsDateString,
  IsString,
  IsArray,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { MeetingType } from '../enumes/meet-type.enum';

export class CreateMeetAndVisitDto {
  @ApiProperty()
  @IsOptional()
  @IsInt()
  lead_id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  assigned_user_id?: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  event_title?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  start_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  end_time?: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(MeetingType)
  meeting_type?: MeetingType;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  miles_office_id?: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  session_id?: number;

  @ApiProperty({
    description: 'Array of program IDs',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  program_ids?: number[];
}
