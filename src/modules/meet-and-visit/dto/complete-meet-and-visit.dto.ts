import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CompleteVisitDto {
  @ApiProperty({
    description: 'ID of the visit to complete',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  visit_id: number;

  @ApiProperty({
    description: 'ID of the miles office to uopdate location',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  milesOffice_id?: number;

  @ApiProperty({
    description: 'Date and time of the visit',
    example: '2024-01-15T10:00:00Z',
  })
  @IsOptional()
  visit_date_time?: string;

  @ApiProperty({
    description: 'Duration of the visit in seconds',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    description: 'Session config id',
    example: 100,
  })
  @IsOptional()
  @IsNumber()
  session_id?: number;

  @ApiProperty({
    description: 'Proof attachment id',
    example: 'Visit completed successfully',
  })
  @IsNumber()
  proof_attachment_id?: number;

  @ApiProperty({
    description: 'Additional comments about the visit completion',
    example: '1',
  })
  @IsOptional()
  @IsString()
  comment?: string;
}
