import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { MeetAndVisit } from './meet-and-visit.entity';
import { BaseEntity } from 'src/common/entities/base.entity';

@Entity('meet_attendees')
export class MeetAttendees extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => MeetAndVisit, (meetAndVisit) => meetAndVisit.meetAttendees, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'meet_and_visit_id' })
  meetAndVisit: MeetAndVisit;

  @Column({ type: 'integer', nullable: false })
  meet_and_visit_id: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'integer', nullable: true })
  duration: number;

  @Column({ type: 'varchar', nullable: true })
  attendee_email: string;

  @Column({ type: 'timestamp with time zone', nullable: true })
  join_time: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  leave_time: Date;

  @Column({ type: 'integer', nullable: true })
  session_count: number;
}
