import {
  <PERSON><PERSON><PERSON>,
  Colum<PERSON>,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index,
  OneToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Lead } from '@modules/leads/entities/lead.entity';
import { User } from '@modules/users/entities/user.entity';
import { MeetAttendees } from './meet-attendees.entity';
import { MilesOffice } from '@modules/miles-offices/entities/miles-office.entity';
import { SessionConfig } from '@modules/session/entities/session-config.entity';
import { BaseEntity } from 'src/common/entities/base.entity';
import { MeetingType } from '../enumes/meet-type.enum';
import { MeetStatus } from '../enumes/meet-status.enum';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Attachment } from '@modules/attachments/entities/attachment.entity';
import { Program } from '@modules/programs/entities/program.entity';

@Entity('meet_and_visit')
export class MeetAndVisit extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Lead, { nullable: true })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @Column({ type: 'integer', nullable: true })
  lead_id: number;

  @Index()
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_user_id' })
  assignedUser: User;

  @Column({ type: 'integer', nullable: true })
  assigned_user_id: number;

  @Column({ type: 'varchar', nullable: true })
  assigned_user_email: string;

  @Column({ type: 'text', array: true, nullable: true })
  participant_emails: string[];

  @Column({ type: 'varchar', nullable: true })
  event_title: string;

  @Column({ type: 'timestamp with time zone', nullable: true })
  start_time: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  end_time: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  visit_date_time: Date;

  @Column({
    type: 'enum',
    enum: MeetStatus,
    default: MeetStatus.PENDING,
  })
  meet_status: MeetStatus;

  @Column({
    type: 'enum',
    enum: MeetingType,
    nullable: true,
  })
  meeting_type: MeetingType;

  @Column({ type: 'varchar', nullable: true })
  event_id: string;

  @Column({ type: 'varchar', nullable: true })
  google_meet_link: string;

  @Column({ type: 'text', array: true, nullable: true })
  recording_urls: string[];

  @OneToOne(() => Attachment)
  @JoinColumn({ name: 'proof_attachment_id' })
  proof_attachment: Attachment;

  @Column({ type: 'integer', nullable: true })
  proof_attachment_id: number;

  @Index()
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approver_user_id' })
  meeting_approver: User;

  @Column({ type: 'integer', nullable: true })
  approver_user_id: number;

  @Index()
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'scheduled_by_user_id' })
  scheduled_by: User;

  @Column({ type: 'integer', nullable: true })
  scheduled_by_user_id: number;

  @Index()
  @ManyToOne(() => MilesOffice, { nullable: true })
  @JoinColumn({ name: 'miles_office_id' })
  miles_office: MilesOffice;

  @Column({ type: 'integer', nullable: true })
  miles_office_id: number;

  @Column({ type: 'jsonb', nullable: true })
  attendance_data: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  recording_data: Record<string, any>;

  @Column({ type: 'integer', nullable: true })
  total_duration: number;

  @Column({ type: 'integer', nullable: true })
  mutual_duration: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @OneToMany(() => MeetAttendees, (meetAttendees) => meetAttendees.meetAndVisit)
  meetAttendees: MeetAttendees[];

  @Index()
  @ManyToOne(() => SessionConfig, { nullable: true })
  @JoinColumn({ name: 'session_id' })
  session: SessionConfig;

  @Column({ type: 'integer', nullable: true })
  session_id: number;

  @ManyToMany(() => Program)
  @JoinTable({
    name: 'meet_and_visit_programs',
    joinColumn: { name: 'meet_and_visit_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'program_id', referencedColumnName: 'id' },
  })
  programs: Program[];

  @OneToOne(() => MeetAndVisit)
  @JoinColumn({ name: 'rescheduled_meeting_id' })
  rescheduledMeeting: MeetAndVisit;

  @Column({ type: 'integer', nullable: true })
  rescheduled_meeting_id: number;

  @Column({ type: 'boolean', nullable: true })
  is_aproval_required: boolean;

  @Column({ type: 'text', nullable: true })
  comment: string;
}
