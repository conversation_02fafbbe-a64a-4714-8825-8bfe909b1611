import {
  <PERSON>,
  Post,
  Body,
  Req,
  Get,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { Request } from 'express';
import { CreateMeetAndVisitDto } from './dto/create-meet-and-visit.dto';
import { RescheduleMeetAndVisitDto } from './dto/reschedule-meet-and-visit.dto';
import { MeetAndVisitService } from './services/meet-and-visit.service';
import { CompleteVisitDto } from './dto/complete-meet-and-visit.dto';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { MeetAndVisit } from './entities/meet-and-visit.entity';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { CancelMeetAndVisitDto } from './dto/cancel-meet-and-visit.dto';

@ApiTags('meet-and-visit')
@Controller('meet-and-visit')
export class MeetAndVisitController {
  constructor(private readonly meetAndVisitService: MeetAndVisitService) {}

  @Post('add')
  create(
    @Body() createMeetAndVisitDto: CreateMeetAndVisitDto,
    @Req() req: Request,
  ) {
    return this.meetAndVisitService.create(createMeetAndVisitDto, req.user);
  }

  @Post('reschedule')
  rescheduleVisit(@Body() dto: RescheduleMeetAndVisitDto, @Req() req: Request) {
    return this.meetAndVisitService.reschedule(dto, req.user);
  }

  @Post('cancel')
  cancelVisit(
    @Body() cancelMeetAndVisitDto: CancelMeetAndVisitDto,
    @Req() req: Request,
  ) {
    return this.meetAndVisitService.cancel(cancelMeetAndVisitDto, req.user);
  }

  @Get('get-lead-meets/:leadId')
  getLeadMeets(
    @Param('leadId', ParseIntPipe) leadId: number,
  ): Promise<PaginatedResponse<MeetAndVisit>> {
    return this.meetAndVisitService.getMeetingsByLead(leadId);
  }

  @Get('get-user-meets/:userId')
  getUserMeets(@Param('userId', ParseIntPipe) userId: number) {
    return this.meetAndVisitService.getMeetingsByAssignedUser(userId);
  }

  @Get('upcoming/:leadId')
  @ApiOperation({ summary: 'Get upcoming meetings and visits for a lead' })
  @ApiParam({
    name: 'leadId',
    description: 'ID of the lead to fetch meetings for',
    type: 'number',
  })
  getUpcomingLeadMeetVisits(@Param('leadId', ParseIntPipe) leadId: number) {
    return this.meetAndVisitService.findUpcomingMeetVisit(leadId);
  }

  @Get('complete-meet/:meetId')
  completeGoogleMeet(
    @Param('meetId', ParseIntPipe) meetId: number,
    @Req() req: Request,
  ) {
    return this.meetAndVisitService.completeGoogleMeet(meetId, req.user);
  }

  @Post('complete-visit')
  completeOfficeVisit(
    @Body() completeMeetAndVisitDto: CompleteVisitDto,
    @Req() req: Request,
  ) {
    return this.meetAndVisitService.completeOfficeVisit(
      completeMeetAndVisitDto,
      req.user,
    );
  }
}
