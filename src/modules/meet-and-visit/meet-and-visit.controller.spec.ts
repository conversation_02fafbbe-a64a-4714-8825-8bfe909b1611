import { Test, TestingModule } from '@nestjs/testing';
import { MeetAndVisitController } from './meet-and-visit.controller';
import { MeetAndVisitService } from './meet-and-visit.service';

describe('MeetAndVisitController', () => {
  let controller: MeetAndVisitController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MeetAndVisitController],
      providers: [MeetAndVisitService],
    }).compile();

    controller = module.get<MeetAndVisitController>(MeetAndVisitController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
