import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In } from 'typeorm';
import { <PERSON><PERSON>han, Not, Repository } from 'typeorm';
import {
  GoogleCalendarService,
  CalendarEventData,
} from 'src/common/service/google/google-calendar.service';
import { TaskService } from 'src/common/service/google/task.service';
import { TaskType } from 'src/modules/callbacks/enums/task.enum';
import { User } from '@modules/users/entities/user.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { MilesOffice } from '@modules/miles-offices/entities/miles-office.entity';
import { MeetAndVisit } from '../entities/meet-and-visit.entity';
import { CreateMeetAndVisitDto } from '../dto/create-meet-and-visit.dto';
import { RescheduleMeetAndVisitDto } from '../dto/reschedule-meet-and-visit.dto';
import { SessionService } from '@modules/session/session.service';
import { EngagementType } from '@modules/session/enums/engagement-type.enum';
import { FetchRecordingTaskDataDto } from '../dto/fetch-recording-task.dto';
import { TaskScheduleDto } from 'src/common/service/google/dto/task-schedule.dto';
import { MeetAttendees } from '../entities/meet-attendees.entity';
import { MeetingType } from '../enumes/meet-type.enum';
import { MeetStatus } from '../enumes/meet-status.enum';
import { CompleteVisitDto } from '../dto/complete-meet-and-visit.dto';
import { Program } from '@modules/programs/entities/program.entity';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { LeadProgramInterestService } from '@modules/leads/services/lead-program-interest.service';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { ActionType } from '@modules/lead-histories/enums/lead-history.enum';
import { MeetAndVisitSubAction } from '../enumes/meet-and-visit-subactions.enum';
import { LeadSessionStatus } from '@modules/session/enums/lead-session-status.enum';
import { CancelMeetAndVisitDto } from '../dto/cancel-meet-and-visit.dto';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';

@Injectable()
export class MeetAndVisitService {
  private readonly logger = new Logger(MeetAndVisitService.name);
  constructor(
    @InjectRepository(MeetAndVisit)
    private readonly meetAndVisitRepository: Repository<MeetAndVisit>,
    @InjectRepository(MeetAttendees)
    private readonly meetAttendeesRepository: Repository<MeetAttendees>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(MilesOffice)
    private readonly milesOfficeRepository: Repository<MilesOffice>,
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(Program)
    private readonly programRepository: Repository<Program>,
    private readonly googleCalendarService: GoogleCalendarService,
    private readonly taskService: TaskService,
    private readonly sessionConfigService: SessionService,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly leadHistoryService: LeadHistoryService,
  ) {}

  async create(dto: CreateMeetAndVisitDto, user: Express.User) {
    try {
      // Validating no child session is already created for the lead
      if (dto.lead_id && dto.session_id) {
        const lead = await this.leadRepository.findOne({
          where: { id: dto.lead_id },
          relations: {
            lead_sessions: {
              session: {
                child: true,
                parent: true,
              },
            },
          },
        });
        if (lead && lead.lead_sessions && lead.lead_sessions.length > 0) {
          const childs = await this.sessionConfigService.findAllChildSessions(
            dto.session_id,
          );
          const isAlreadyScheduled = lead.lead_sessions.some(
            (ls) =>
              ls.session.id === dto.session_id &&
              (ls.session.child_id ||
                (ls.session.parent_id &&
                  (ls.complete_status === LeadSessionStatus.PENDING ||
                    ls.complete_status === LeadSessionStatus.COMPLETED))),
          );
          const isChildAlreadyScheduled = lead.lead_sessions.some((ls) =>
            childs.some(
              (child) =>
                (child &&
                  child.id &&
                  child.id === ls.session.id &&
                  ls.complete_status === LeadSessionStatus.COMPLETED) ||
                ls.complete_status === LeadSessionStatus.PENDING,
            ),
          );

          if (isAlreadyScheduled || isChildAlreadyScheduled) {
            throw new BadRequestException(
              'A session is already scheduled for this lead with the same session or its child.',
            );
          }
        }
      }
      const session = await this.sessionConfigService.findOne(dto.session_id);
      if (
        session &&
        session.is_program_based &&
        (!dto.program_ids || dto.program_ids.length <= 0)
      ) {
        throw new BadRequestException(
          'Programs are required for program based session',
        );
      }

      // Create method with object
      const meetAndVisit = this.meetAndVisitRepository.create({
        lead_id: dto.lead_id,
        assigned_user_id: dto.assigned_user_id,
        event_title: dto.event_title ? dto.event_title : 'Meet with Miles',
        start_time: dto.start_time ? new Date(dto.start_time) : undefined,
        end_time: dto.end_time ? new Date(dto.end_time) : undefined,
        meeting_type: dto.meeting_type,
        miles_office_id: dto.miles_office_id,
        session_id: dto.session_id,
        created_by: user.id,
        approver_user_id: user.manager_id,
        scheduled_by_user_id: user.id,
        client_id: user.currentClientId,
      });

      // Get assigned user email from database using assigned_user_id
      let assignedUserEmail = '';
      if (dto.assigned_user_id) {
        const assignedUser = await this.userRepository.findOne({
          where: { id: dto.assigned_user_id },
        });
        if (assignedUser && assignedUser.email) {
          assignedUserEmail = assignedUser.email;
          meetAndVisit.assigned_user_email = assignedUserEmail;
        }
      }
      const scheduledUser = await this.userRepository.findOne({
        where: { id: user.id },
      });

      // Get lead primary email for participant_emails
      let participantEmails: string[] = [];
      let leadName = 'Lead';
      let lead;
      if (dto.lead_id) {
        lead = await this.leadRepository.findOne({
          where: { id: dto.lead_id },
          relations: {
            program_interests: {
              program: true,
              lead_level: true,
            },
            contact: {
              primary_email: true,
            },
          },
        });
        if (
          lead &&
          lead.contact &&
          lead.contact.primary_email &&
          lead.contact.primary_email.email
        ) {
          participantEmails = [lead.contact.primary_email.email];
          leadName =
            `${lead.contact.first_name || ''} ${lead.contact.last_name || ''}`.trim() ||
            'Lead';
          meetAndVisit.participant_emails = participantEmails;
        }
      }

      // If meeting type is Google Meet or Office Visit, create calendar events
      if (dto.meeting_type) {
        try {
          // Prepare attendees list (assigned user + all participants)
          const attendeesEmails = [
            assignedUserEmail,
            scheduledUser?.email,
            ...participantEmails,
          ].filter((email) => email);
          const milesOffice = await this.milesOfficeRepository.findOne({
            where: { id: dto.miles_office_id },
          });
          // Prepare event data
          const eventData: CalendarEventData = {
            summary: meetAndVisit.event_title || `Meeting with ${leadName}`,
            description: `Meeting with ${leadName}`,
            startTime: meetAndVisit.start_time.toISOString(),
            endTime: meetAndVisit.end_time.toISOString(),
            attendeesEmails: attendeesEmails,
            location: milesOffice?.address,
          };

          // Create calendar event based on meeting type
          if (dto.meeting_type === MeetingType.GMEET) {
            // Create Google Meet
            const meetResponse =
              await this.googleCalendarService.createGoogleMeetEvent(eventData);

            // Update meeting with Google Meet link
            meetAndVisit.google_meet_link = meetResponse.meetLink;
            meetAndVisit.event_id = meetResponse.eventId;
          } else if (dto.meeting_type === MeetingType.OFFICE_VISIT) {
            // Create regular calendar event
            const calendarResponse =
              await this.googleCalendarService.createCalendarEvent(eventData);

            // Update meeting with calendar event ID
            meetAndVisit.event_id = calendarResponse.eventId;
          }
        } catch (error) {
          this.logger.error(
            `Failed to create calendar event: ${error.message}`,
            error.stack,
          );
          // Continue with the meeting creation even if calendar event fails
        }
      }

      // Save the meeting at the end with all data
      let savedMeeting = await this.meetAndVisitRepository.save(meetAndVisit);

      // Handle programs if provided
      if (dto.program_ids && dto.program_ids.length > 0) {
        const programs = await this.programRepository.findBy({
          id: In(dto.program_ids),
        });
        savedMeeting.programs = programs;
        const response = await this.meetAndVisitRepository.save(savedMeeting);
      }

      const leadSession = await this.sessionConfigService.createLeadSession(
        savedMeeting.lead_id,
        savedMeeting.session_id,
        EngagementType.MEET_AND_VISIT,
        savedMeeting.id,
        {},
        savedMeeting.assigned_user_id,
        user.id,
      );
      savedMeeting.is_aproval_required = leadSession.is_approval_required;
      this.meetAndVisitRepository.save(savedMeeting);

      // Create lead history entry
      savedMeeting = await this.meetAndVisitRepository.findOne({
        where: { id: savedMeeting.id },
        relations: {
          miles_office: true,
          lead: {
            program_interests: {
              program: true,
              lead_level: true,
            },
          },
          assignedUser: true,
          session: true,
        },
      });
      try {
        if (savedMeeting.meeting_type === MeetingType.OFFICE_VISIT) {
          this.meetAndVisitHistory(
            MeetAndVisitSubAction.OFFICE_VISIT_SCHEDULED,
            savedMeeting.lead_id,
            user,
            {
              date: savedMeeting.start_time.toISOString(),
              duration: savedMeeting.total_duration || 1800,
              counselledBy:
                savedMeeting.assignedUser?.first_name +
                '' +
                savedMeeting.assignedUser?.last_name,
              counselledById: savedMeeting.assigned_user_id,
              counsellingLevel: savedMeeting.session?.session_name || '', //string
              location: savedMeeting.miles_office?.city || '', //string
            },
            lead?.program_interests?.map((pi: LeadProgramInterest) => ({
              program: pi.program.name,
              initial: pi.lead_level.name,
              next: pi.lead_level.name,
              type: pi.lead_level.temperature,
              comment: pi.comments,
            })),
          );
        } else if (savedMeeting.meeting_type === MeetingType.GMEET) {
          this.meetAndVisitHistory(
            MeetAndVisitSubAction.GOOGLE_MEET_SCHEDULED,
            savedMeeting.lead_id,
            user,
            {
              date: savedMeeting.start_time.toISOString(),
              duration: savedMeeting.total_duration || 1800,
              counselledBy:
                savedMeeting.assignedUser?.first_name +
                '' +
                savedMeeting.assignedUser?.last_name,
              counselledById: savedMeeting.assigned_user_id || 0,
              counsellingLevel: savedMeeting.session?.session_name || '',
            },
            lead?.program_interests?.map((pi: LeadProgramInterest) => ({
              program: pi.program.name,
              initial: pi.lead_level.name,
              next: pi.lead_level.name,
              type: pi.lead_level.temperature,
              comment: pi.comments,
            })),
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to create history: ${error.message}`,
          error.stack,
        );
      }

      return {
        success: true,
        message: `${
          savedMeeting.meeting_type == MeetingType.GMEET
            ? 'Google Meet'
            : 'Office Visit'
        } created successfully`,
        data: savedMeeting,
      };
    } catch (error) {
      this.logger.error(
        `Error creating meeting: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        'Failed to create meeting : ' + error.message,
      );
    }
  }

  async reschedule(dto: RescheduleMeetAndVisitDto, user: Express.User) {
    // 1. Find the existing meeting
    const existingMeeting = await this.meetAndVisitRepository.findOne({
      where: { id: dto.meeting_id },
      relations: {
        lead: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
        session: true,
        assignedUser: true,
        miles_office: true,
        programs: true,
      },
    });

    if (!existingMeeting) {
      throw new NotFoundException(
        `Meeting with ID ${dto.meeting_id} not found`,
      );
    }

    // Check if meeting can be rescheduled
    if (existingMeeting.meet_status === MeetStatus.COMPLETED) {
      throw new BadRequestException('Cannot reschedule a completed meeting');
    }

    if (existingMeeting.meet_status === MeetStatus.CANCELLED) {
      throw new BadRequestException('Cannot reschedule a cancelled meeting');
    }

    // 2. Cancel the existing meeting
    existingMeeting.meet_status = MeetStatus.RESCHEDULED;
    existingMeeting.updated_by = user.id;
    await this.meetAndVisitRepository.save(existingMeeting);

    this.sessionConfigService.updateLeadSessionStatus(
      existingMeeting.lead_id,
      existingMeeting.session_id,
      user.id,
      LeadSessionStatus.RESCHEDULED,
    );

    // 3. If there's a calendar event, delete it
    if (existingMeeting.event_id) {
      try {
        await this.googleCalendarService.deleteEvent(existingMeeting.event_id);
      } catch (error) {
        this.logger.error(
          `Failed to delete calendar event: ${error.message}`,
          error.stack,
        );
        // Continue with rescheduling even if calendar deletion fails
      }
    }

    // 4. Create new meeting with same details but updated date/time
    const newMeeting = this.meetAndVisitRepository.create({
      client_id: existingMeeting.client_id,
      lead_id: existingMeeting.lead_id,
      event_title: existingMeeting.event_title,
      assigned_user_id: existingMeeting.assigned_user_id,
      assigned_user_email: existingMeeting.assigned_user_email,
      participant_emails: existingMeeting.participant_emails,
      start_time: new Date(dto.new_start_time),
      end_time: new Date(dto.new_end_time),
      meeting_type: existingMeeting.meeting_type,
      miles_office_id: existingMeeting.miles_office_id,
      meet_status: MeetStatus.PENDING, // New meeting starts as pending
      created_by: user.id,
      scheduled_by_user_id: user.id,
      approver_user_id: existingMeeting.approver_user_id, // Keep the same approver
      session_id: existingMeeting.session_id, // Keep the same session if applicable
      rescheduled_meeting_id: existingMeeting.id, // Link to the original meeting
    });

    const savedMeeting = await this.meetAndVisitRepository.save(newMeeting);

    // Copy programs from existing meeting if any
    if (existingMeeting.programs && existingMeeting.programs.length > 0) {
      savedMeeting.programs = existingMeeting.programs;
      await this.meetAndVisitRepository.save(savedMeeting);
    }
    this.sessionConfigService.createLeadSession(
      savedMeeting.lead_id,
      savedMeeting.session_id,
      EngagementType.MEET_AND_VISIT,
      savedMeeting.id,
      {},
      user.id,
    );
    // 5. Create new calendar event if needed
    let lead;
    if (existingMeeting.meeting_type) {
      try {
        // Get assigned user email
        let assignedUserEmail = '';
        if (existingMeeting.assigned_user_id) {
          const assignedUser = await this.userRepository.findOne({
            where: { id: existingMeeting.assigned_user_id },
          });
          if (assignedUser && assignedUser.email) {
            assignedUserEmail = assignedUser.email;
          }
        }

        // Get lead email
        let leadEmail = '';
        let leadName = 'Lead';

        if (existingMeeting.lead_id) {
          lead = await this.leadRepository.findOne({
            where: { id: existingMeeting.lead_id },
            relations: {
              program_interests: {
                program: true,
                lead_level: true,
              },
              contact: {
                primary_email: true,
              },
            },
          });
          if (
            lead &&
            lead.contact &&
            lead.contact.primary_email &&
            lead.contact.primary_email.email
          ) {
            leadEmail = lead.contact.primary_email.email;
            leadName =
              `${lead.contact.first_name || ''} ${lead.contact.last_name || ''}`.trim() ||
              'Lead';
          }
        }

        // Prepare attendees list
        const attendeesEmails = [assignedUserEmail, leadEmail].filter(
          (email) => email,
        );
        const milesOffice = await this.milesOfficeRepository.findOne({
          where: { id: existingMeeting.miles_office_id },
        });

        // Prepare event data
        const eventData: CalendarEventData = {
          summary: `Meeting with ${leadName}`,
          description: `Meeting ID: ${savedMeeting.id}`,
          startTime: savedMeeting.start_time.toISOString(),
          endTime: savedMeeting.end_time.toISOString(),
          attendeesEmails: attendeesEmails,
          location: milesOffice?.address,
        };

        // Create calendar event based on meeting type
        if (existingMeeting.meeting_type === MeetingType.GMEET) {
          // Create Google Meet
          const meetResponse =
            await this.googleCalendarService.createGoogleMeetEvent(eventData);

          // Update meeting with Google Meet link
          savedMeeting.google_meet_link = meetResponse.meetLink;
          savedMeeting.event_id = meetResponse.eventId;
          await this.meetAndVisitRepository.save(savedMeeting);
        } else if (existingMeeting.meeting_type === MeetingType.OFFICE_VISIT) {
          // Create regular calendar event
          const calendarResponse =
            await this.googleCalendarService.createCalendarEvent(eventData);

          // Update meeting with calendar event ID
          savedMeeting.event_id = calendarResponse.eventId;
          await this.meetAndVisitRepository.save(savedMeeting);
        }
      } catch (error) {
        this.logger.error(
          `Failed to create calendar event: ${error.message}`,
          error.stack,
        );
        // Continue with the meeting creation even if calendar event fails
      }
    }
    try {
      if (savedMeeting.meeting_type === MeetingType.OFFICE_VISIT) {
        this.meetAndVisitHistory(
          MeetAndVisitSubAction.OFFICE_VISIT_RESCHEDULED,
          savedMeeting.lead_id,
          user,
          {
            date: savedMeeting.start_time.toISOString(),
            duration: savedMeeting.total_duration || 1800,
            counselledBy:
              existingMeeting.assignedUser?.first_name +
              '' +
              existingMeeting.assignedUser?.last_name,
            counselledById: existingMeeting.assigned_user_id || 0,
            counsellingLevel: existingMeeting.session?.session_name || '',
            location: existingMeeting.miles_office?.city || '',
          },
          lead?.program_interests?.map((pi: LeadProgramInterest) => ({
            program: pi.program.name,
            initial: pi.lead_level.name,
            next: pi.lead_level.name,
            type: pi.lead_level.temperature,
            comment: pi.comments,
          })),
        );
      } else if (savedMeeting.meeting_type === MeetingType.GMEET) {
        this.meetAndVisitHistory(
          MeetAndVisitSubAction.GOOGLE_MEET_RESCHEDULED,
          savedMeeting.lead_id,
          user,
          {
            date: savedMeeting.start_time.toISOString(),
            duration: savedMeeting.total_duration || 1800,
            counselledBy:
              existingMeeting.assignedUser?.first_name +
              '' +
              existingMeeting.assignedUser?.last_name,
            counselledById: existingMeeting.assigned_user_id || 0,
            counsellingLevel: existingMeeting.session?.session_name || '',
          },
          lead?.program_interests?.map((pi: LeadProgramInterest) => ({
            program: pi.program.name,
            initial: pi.lead_level.name,
            next: pi.lead_level.name,
            type: pi.lead_level.temperature,
            comment: pi.comments,
          })),
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to create lead session: ${error.message}`,
        error.stack,
      );
      // Continue with the meeting creation even if lead session fails
    }

    return {
      success: true,
      message: `${
        savedMeeting.meeting_type == MeetingType.GMEET
          ? 'Google Meet'
          : 'Office Visit'
      } rescheduled successfully`,
      data: savedMeeting,
    };
  }

  async cancel(cancelDto: CancelMeetAndVisitDto, user: Express.User) {
    // 1. Find the existing meeting
    const existingMeeting = await this.meetAndVisitRepository.findOne({
      where: { id: cancelDto.meeting_id },
      relations: {
        lead: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
        assignedUser: true,
        miles_office: true,
        programs: true,
        session: true,
      },
    });

    if (!existingMeeting) {
      throw new NotFoundException(
        `Meeting with ID ${cancelDto.meeting_id} not found`,
      );
    }

    // 2. Check if meeting can be cancelled
    if (existingMeeting.meet_status === MeetStatus.COMPLETED) {
      throw new BadRequestException('Cannot cancel a completed meeting');
    }

    if (existingMeeting.meet_status === MeetStatus.CANCELLED) {
      throw new BadRequestException('Meeting is already cancelled');
    }

    if (existingMeeting.meet_status === MeetStatus.COMPLETION_PROCESSING) {
      throw new BadRequestException(
        'Meeting is already in completion processing',
      );
    }

    // 3. Cancel the meeting
    existingMeeting.meet_status = MeetStatus.CANCELLED;
    existingMeeting.status = false;
    existingMeeting.is_active = false;
    existingMeeting.updated_by = user.id;
    existingMeeting.comment = cancelDto.comment;

    // 4. If there's a calendar event, delete it
    if (existingMeeting.event_id) {
      try {
        await this.googleCalendarService.deleteEvent(existingMeeting.event_id);
      } catch (error) {
        this.logger.error(
          `Failed to delete calendar event: ${error.message}`,
          error.stack,
        );
        // Continue with cancellation even if calendar deletion fails
      }
    }
    this.sessionConfigService.updateLeadSessionStatus(
      existingMeeting.lead_id,
      existingMeeting.session_id,
      user.id,
      LeadSessionStatus.CANCELLED,
    );
    await this.meetAndVisitRepository.save(existingMeeting);
    // 5. Create lead history entry for cancellation
    try {
      if (existingMeeting.meeting_type === MeetingType.OFFICE_VISIT) {
        this.meetAndVisitHistory(
          MeetAndVisitSubAction.OFFICE_VISIT_CANCELLED,
          existingMeeting.lead_id,
          user,
          {
            date: existingMeeting.start_time.toISOString(),
            duration: Math.floor(
              (existingMeeting.end_time.getTime() -
                existingMeeting.start_time.getTime()) /
                1000,
            ),
            reason: existingMeeting.comment || 'Cancelled by user',
            counselledBy:
              existingMeeting.assignedUser?.first_name +
              '' +
              existingMeeting.assignedUser?.last_name,
            counselledById: existingMeeting.assigned_user_id || 0,
            counsellingLevel: existingMeeting.session?.session_name || '',
            location: existingMeeting.miles_office?.city || '',
          },
          existingMeeting?.lead?.program_interests?.map(
            (pi: LeadProgramInterest) => ({
              program: pi.program.name,
              initial: pi.lead_level.name,
              next: pi.lead_level.name,
              type: pi.lead_level.temperature,
              comment: pi.comments,
            }),
          ),
        );
      } else if (existingMeeting.meeting_type === MeetingType.GMEET) {
        this.meetAndVisitHistory(
          MeetAndVisitSubAction.GOOGLE_MEET_CANCELLED,
          existingMeeting.lead_id,
          user,
          {
            date: existingMeeting.start_time.toISOString(),
            duration: existingMeeting.total_duration,
            reason: existingMeeting.comment || 'Cancelled by user',
            counselledBy:
              existingMeeting.assignedUser?.first_name +
              '' +
              existingMeeting.assignedUser?.last_name,
            counselledById: existingMeeting.assigned_user_id || 0,
            counsellingLevel: existingMeeting.session?.session_name || '',
          },
          existingMeeting?.lead?.program_interests?.map(
            (pi: LeadProgramInterest) => ({
              program: pi.program.name,
              initial: pi.lead_level.name,
              next: pi.lead_level.name,
              type: pi.lead_level.temperature,
              comment: pi.comments,
            }),
          ),
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to create lead history: ${error.message}`,
        error.stack,
      );
      // Continue with cancellation even if lead history fails
    }

    return {
      success: true,
      message: `${
        existingMeeting.meeting_type == MeetingType.GMEET
          ? 'Google Meet'
          : 'Office Visit'
      } cancelled successfully`,
      data: existingMeeting,
    };
  }

  async getMeetingsByLead(
    leadId: number,
  ): Promise<PaginatedResponse<MeetAndVisit>> {
    try {
      const data = await this.meetAndVisitRepository.find({
        where: { lead_id: leadId },
        relations: {
          lead: true,
          assignedUser: true,
          meeting_approver: true,
          scheduled_by: true,
          miles_office: true,
          meetAttendees: true,
          session: true,
          programs: true,
        },
        order: { start_time: 'DESC' },
        select: {
          id: true,
          event_id: true,
          event_title: true,
          start_time: true,
          end_time: true,
          meeting_type: true,
          meet_status: true,
          google_meet_link: true,
          assigned_user_id: true,
          assigned_user_email: true,
          miles_office: {
            id: true,
            city: true,
            address: true,
          },
          session: {
            id: true,
            session_name: true,
          },
          programs: {
            id: true,
            name: true,
            code: true,
          },
        },
      });
      return {
        data,
        meta: null,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching meetings for lead ${leadId}: ${error.message}`,
        error.stack,
      );
      throw new NotFoundException(`Meetings for lead ID ${leadId} not found`);
    }
  }

  async getMeetingsByAssignedUser(userId: number): Promise<MeetAndVisit[]> {
    return this.meetAndVisitRepository.find({
      where: { assigned_user_id: userId },
      relations: [
        'lead',
        'assignedUser',
        'meeting_approver',
        'scheduled_by',
        'miles_office',
        'meetAttendees',
        'programs',
      ],
      order: { created_at: 'DESC' },
    });
  }

  async completeGoogleMeet(visitId: number, user: Express.User) {
    // 1. Find the existing meeting
    const existingMeeting = await this.meetAndVisitRepository.findOne({
      where: { id: visitId },
    });
    if (!existingMeeting) {
      throw new NotFoundException(`Meeting with ID ${visitId} not found`);
    }
    // 2. Validate meeting type is Google Meet
    if (existingMeeting.meeting_type !== MeetingType.GMEET) {
      throw new BadRequestException(
        'Only Google Meet visits can be completed through this API',
      );
    }
    // 3. Check if meeting can be completed
    if (existingMeeting.meet_status === MeetStatus.COMPLETED) {
      throw new BadRequestException('Meeting is already completed');
    }
    if (existingMeeting.meet_status === MeetStatus.CANCELLED) {
      throw new BadRequestException('Cannot complete a cancelled meeting');
    }
    if (existingMeeting.meet_status === MeetStatus.RESCHEDULED) {
      throw new BadRequestException('Cannot complete a rescheduled meeting');
    }
    // 4. Check if meeting is not in the future
    const currentTime = new Date();
    if (
      existingMeeting.start_time &&
      existingMeeting.start_time > currentTime
    ) {
      throw new BadRequestException('Cannot complete a future meeting');
    }
    // 5. Schedule task after 30 minutes
    existingMeeting.meet_status = MeetStatus.COMPLETION_PROCESSING;
    this.meetAndVisitRepository.save(existingMeeting);
    this.scheduleRecordingFetchTask(existingMeeting.id, 0, existingMeeting);
    try {
    } catch (error) {}
    return {
      success: true,
      message: 'Google Meet complete task processing...',
    };
  }

  async scheduleRecordingFetchTask(
    visitId: number,
    retry_count: number,
    meet?: MeetAndVisit,
  ): Promise<void> {
    const scheduleTime = new Date();
    scheduleTime.setMinutes(scheduleTime.getMinutes() + 20); // Schedule for 20 minutes later

    if (!meet) {
      meet = await this.getMeetAndVisitById(visitId);
    }
    const scheduleData: FetchRecordingTaskDataDto = {
      visitId: meet.id,
      userId: meet.assigned_user_id, // User ID can be set if needed
      event_id: meet.event_id, // Event ID can be set if needed
      assigned_user_id: meet.assigned_user_id, // Assigned user ID can be set if needed
      retryCount: retry_count, // Initial retry count
    };

    const taskPayload: TaskScheduleDto = {
      task: TaskType.FETCH_GMEET_RECORDING_AND_ATTENDANCE,
      data: scheduleData,
    };
    return this.taskService.scheduleTask(taskPayload, scheduleTime);
  }

  async completeOfficeVisit(
    completeVisitDto: CompleteVisitDto,
    user: Express.User,
  ): Promise<{ success: any; message: string }> {
    // 1. Find the existing meeting
    const existingMeeting = await this.meetAndVisitRepository.findOne({
      where: { id: completeVisitDto.visit_id },
      relations: {
        lead: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
        assignedUser: true,
        meeting_approver: true,
        scheduled_by: true,
        miles_office: true,
        meetAttendees: true,
        session: {
          session_program_levels: {
            program: true,
            level: true,
          },
        },
        programs: true,
      },
    });
    if (!existingMeeting) {
      throw new NotFoundException(
        `Meeting with ID ${completeVisitDto.visit_id} not found`,
      );
    }
    // 2. Validate meeting type is Office Visit
    if (existingMeeting.meeting_type !== MeetingType.OFFICE_VISIT) {
      throw new BadRequestException(
        'Only Office Visits can be completed through this API',
      );
    }
    // 3. Check if meeting can be completed
    if (existingMeeting.meet_status === MeetStatus.COMPLETED) {
      throw new BadRequestException('Meeting is already completed');
    }
    if (existingMeeting.meet_status === MeetStatus.CANCELLED) {
      throw new BadRequestException('Cannot complete a cancelled meeting');
    }
    if (existingMeeting.meet_status === MeetStatus.RESCHEDULED) {
      throw new BadRequestException('Cannot complete a rescheduled meeting');
    }
    // 4. Check if meeting is not in the future
    const currentTime = new Date();
    if (
      existingMeeting.start_time &&
      existingMeeting.start_time > currentTime
    ) {
      throw new BadRequestException('Cannot complete a future meeting');
    }
    if (completeVisitDto.milesOffice_id) {
      existingMeeting.miles_office_id = completeVisitDto.milesOffice_id;
    }

    if (completeVisitDto.visit_date_time) {
      existingMeeting.visit_date_time = new Date(
        completeVisitDto.visit_date_time,
      );
    }
    if (completeVisitDto.duration) {
      existingMeeting.total_duration = completeVisitDto.duration;
    }
    if (completeVisitDto.session_id) {
      existingMeeting.session_id = completeVisitDto.session_id;
      this.sessionConfigService.updateLeadSession(
        existingMeeting.lead_id,
        existingMeeting.session_id,
        completeVisitDto.session_id,
        user.id,
      );
    }
    if (completeVisitDto.proof_attachment_id) {
      existingMeeting.proof_attachment_id =
        completeVisitDto.proof_attachment_id;
    }
    if (completeVisitDto.comment) {
      existingMeeting.metadata = {
        comment: completeVisitDto.comment,
      };
    }

    existingMeeting.meet_status = MeetStatus.COMPLETED;
    existingMeeting.updated_by = user.id;
    let lead;
    if (
      existingMeeting.session.is_program_based &&
      existingMeeting.programs.length > 0
    ) {
      const programLevels: { targetLevel: number; programId: number }[] = [];
      for (const program of existingMeeting.programs) {
        if (
          existingMeeting.session.session_program_levels.some(
            (spl) => spl.program_id === program.id,
          )
        ) {
          programLevels.push({
            programId: program.id,
            targetLevel: existingMeeting.session.session_program_levels.find(
              (spl) => spl.program_id === program.id,
            ).level_id,
          });
        }
      }
      let failedResult =
        await this.leadProgramInterestService.updateLeadLevelsForLead({
          leadId: existingMeeting.lead_id,
          levelsAndPrograms: programLevels,
        });
      lead = await this.leadRepository.findOne({
        where: { id: existingMeeting.lead_id },
        relations: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
      });
      this.sessionConfigService.completeLeadSession(
        existingMeeting.lead_id,
        existingMeeting.session_id,
        {
          recordings: [],
          total_duration: existingMeeting.total_duration || 0,
          mutual_duration: existingMeeting.mutual_duration || 0,
        },
        null,
        failedResult?.failedUpdates,
      );
    }
    await this.meetAndVisitRepository.save(existingMeeting);
    try {
      this.meetAndVisitHistory(
        MeetAndVisitSubAction.OFFICE_VISIT_COMPLETED,
        existingMeeting.lead_id,
        user,
        {
          date: existingMeeting.start_time.toISOString(),
          duration: existingMeeting.total_duration || 0,
          counselledBy:
            existingMeeting.assignedUser?.first_name +
            '' +
            existingMeeting.assignedUser?.last_name,
          counselledById: existingMeeting.assigned_user_id || 0,
          counsellingLevel: existingMeeting.session?.session_name || '',
          location: existingMeeting.miles_office?.city || '',
          attachmentProof: existingMeeting.recording_urls || [],
          attachmentProofId: [existingMeeting.proof_attachment_id],
        },
        existingMeeting?.lead?.program_interests?.map(
          (pi: LeadProgramInterest) => ({
            program: pi.program.name,
            initial: pi.lead_level.name,
            next: lead.program_interests.find(
              (p) => p.program_id === pi.program_id,
            ).lead_level.name,
            type: pi.lead_level.temperature,
            comment: pi.comments,
          }),
        ),
      );
    } catch (error) {
      this.logger.error(
        `Failed to create lead session: ${error.message}`,
        error.stack,
      );
      // Continue with the meeting completion even if lead session fails
    }
    // return existingMeeting;

    return {
      success: true,
      message: 'Office visit completed successfully',
    };
  }

  getMeetAndVisitById(id: number): Promise<MeetAndVisit> {
    return this.meetAndVisitRepository.findOne({
      where: { id: id },
      relations: {
        lead: true,
        assignedUser: true,
        meeting_approver: true,
        scheduled_by: true,
        miles_office: true,
        meetAttendees: true,
        programs: true,
      },
    });
  }

  async updateGoogleMeetAttendance(data: any) {
    let visit = await this.meetAndVisitRepository.findOne({
      where: { id: data.visitID },
      relations: {
        assignedUser: true,
        lead: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
        meetAttendees: true,
        session: {
          session_program_levels: {
            program: true,
            level: true,
          },
        },
        programs: true,
      },
    });
    if (!visit) {
      throw new NotFoundException(`Visit with ID ${data.visitId} not found`);
    }
    visit.recording_urls = data.s3Urls || [];
    const meetingAttendees: MeetAttendees[] = [];
    data.attendanceReport?.participants?.forEach((attendee: any) => {
      if (
        !visit.meetAttendees.some((a) => a.attendee_email === attendee.email)
      ) {
        const attendeeEntity = new MeetAttendees();
        attendeeEntity.meet_and_visit_id = visit.id;
        attendeeEntity.name = attendee.displayName || '';
        attendeeEntity.attendee_email = attendee.email || '';
        attendeeEntity.join_time = attendee.firstJoinTime
          ? new Date(attendee.firstJoinTime)
          : null;
        attendeeEntity.leave_time = attendee.lastLeaveTime
          ? new Date(attendee.lastLeaveTime)
          : null;
        attendeeEntity.session_count = attendee.sessionCount || 0;
        attendeeEntity.duration = attendee.totalDurationSeconds || false;
        attendeeEntity.client_id = visit.client_id;
        meetingAttendees.push(attendeeEntity);
      }
    });
    if (meetingAttendees.length > 0) {
      await this.meetAttendeesRepository.save(meetingAttendees);
      visit.meetAttendees.push(...meetingAttendees);
    }
    visit.meet_status = MeetStatus.COMPLETED;
    visit.mutual_duration = data.mutualDurationSeconds || 0;
    visit.attendance_data = data.attendanceReport || {};
    visit.recording_data = data.rawRecordingData || {};
    visit.total_duration = data.totalDurationSeconds || 0;
    let lead;
    if (visit.is_aproval_required) {
      visit.meet_status = MeetStatus.PENDING_APPROVAL;
      //todo! need to send notification to approver
    } else {
      let failedResult;
      visit.meet_status = MeetStatus.COMPLETED;
      if (visit.session.is_program_based && visit.programs.length > 0) {
        const programLevels: { targetLevel: number; programId: number }[] = [];
        for (const program of visit.programs) {
          if (
            visit.session.session_program_levels.some(
              (spl) => spl.program_id === program.id,
            )
          ) {
            programLevels.push({
              programId: program.id,
              targetLevel: visit.session.session_program_levels.find(
                (spl) => spl.program_id === program.id,
              ).level_id,
            });
          }
        }
        failedResult =
          await this.leadProgramInterestService.updateLeadLevelsForLead({
            leadId: visit.lead_id,
            levelsAndPrograms: programLevels,
          });
      }
      this.sessionConfigService.completeLeadSession(
        visit.lead_id,
        visit.session_id,
        {
          recordings: data.s3Urls || [],
          total_duration: visit.total_duration || 0,
          mutual_duration: visit.mutual_duration || 0,
        },
        null,
        failedResult?.failedUpdates,
      );
      lead = await this.leadRepository.findOne({
        where: { id: visit.lead_id },
        relations: {
          program_interests: {
            program: true,
            lead_level: true,
          },
        },
      });
    }
    // Save the updated visit with attendance data
    visit = await this.meetAndVisitRepository.save(visit);
    try {
      const expressUser = {
        id: visit.assigned_user_id,
        currentClientId: visit.client_id,
        email: visit.assignedUser?.email || '',
        manager_id: visit.assignedUser?.manager_id || null,
      } as Express.User;
      const details: any = {
        date: visit.start_time.toISOString(),
        duration: visit.mutual_duration || 0,
        counselledBy:
          visit.assignedUser?.first_name + '' + visit.assignedUser?.last_name,
        counselledById: visit.assigned_user_id || 0,
        counsellingLevel: visit.session?.session_name || '',
        location: visit.miles_office?.city || '',
      };
      if (visit.recording_urls) {
        details.attachmentProof = visit.recording_urls;
      }
      if (visit.proof_attachment_id) {
        details.attachmentProofId = [visit.proof_attachment.id];
      }
      this.meetAndVisitHistory(
        MeetAndVisitSubAction.GOOGLE_MEET_COMPLETED,
        visit.lead_id,
        expressUser,
        details,
        visit?.lead?.program_interests?.map((pi: LeadProgramInterest) => ({
          program: pi.program.name,
          initial: pi.lead_level.name,
          next: lead?.program_interests.find(
            (p) => p.program_id === pi.program_id,
          ).lead_level.name,
          type: pi.lead_level.temperature,
          comment: pi.comments,
        })),
      );
    } catch (error) {
      this.logger.error(
        `Failed to create lead session: ${error.message}`,
        error.stack,
      );
      // Continue with the meeting creation even if lead session fails
    }
  }

  async findUpcomingMeetVisit(
    leadId: number,
  ): Promise<{ data: MeetAndVisit[] }> {
    try {
      const today = new Date();
      const upcomingMeetings = await this.meetAndVisitRepository.find({
        where: {
          lead_id: leadId,
          start_time: MoreThan(today),
          meet_status: Not(MeetStatus.CANCELLED),
        },
        order: { start_time: 'ASC' },
        relations: ['lead', 'assignedUser', 'miles_office'],
      });

      return {
        data: upcomingMeetings,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching upcoming meetings for lead ${leadId} : ${error.message}`,
        error.stack,
      );
      throw new NotFoundException(
        `Upcoming meetings for lead ID ${leadId} not found`,
      );
    }
  }

  async meetAndVisitHistory(
    subAction: string,
    leadId: number,
    user: Express.User,
    meetAndVisitDetails: any,
    levels: any[] = [],
  ) {
    const lead = await this.leadRepository.findOne({
      where: { id: leadId },
      relations: {
        contact: true,
      },
    });
    this.leadHistoryService.createLeadHistory({
      action: ActionType.APPOINTMENT,
      subAction: subAction,
      leadId: leadId,
      contactId: lead?.contact?.id,
      performedByUser: user,
      performedBy: user.id,
      details: meetAndVisitDetails,
      levels,
    });
  }
}
