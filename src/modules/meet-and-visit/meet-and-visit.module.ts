import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { MeetAndVisitController } from './meet-and-visit.controller';
import { MeetAndVisit } from './entities/meet-and-visit.entity';
import { MeetAttendees } from './entities/meet-attendees.entity';
import { TaskService } from 'src/common/service/google/task.service';
import { User } from '@modules/users/entities/user.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { MeetAndVisitService } from './services/meet-and-visit.service';
import { MilesOffice } from '@modules/miles-offices/entities/miles-office.entity';
import { GoogleCalendarService } from 'src/common/service/google/google-calendar.service';
import { UserModule } from '@modules/users/user.module';
import { SessionModule } from '@modules/session/session.module';
import { Program } from '@modules/programs/entities/program.entity';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { LeadModule } from '@modules/leads/lead.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MeetAndVisit,
      MeetAttendees,
      User,
      Lead,
      MilesOffice,
      Program,
    ]),
    ConfigModule,
    UserModule,
    SessionModule,
    forwardRef(() => LeadModule),
  ],
  controllers: [MeetAndVisitController],
  providers: [MeetAndVisitService, TaskService, GoogleCalendarService],
  exports: [MeetAndVisitService, TaskService, GoogleCalendarService],
})
export class MeetAndVisitModule {}
