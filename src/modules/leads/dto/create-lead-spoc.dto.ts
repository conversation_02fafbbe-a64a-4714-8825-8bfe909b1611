import {
  IsNotEmpty,
  IsInt,
  <PERSON>Enum,
  IsOptional,
  IsObject,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SpocType } from '../enums/spoc.enum';

export class CreateLeadSpocDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsNotEmpty()
  @IsInt()
  lead_id: number;

  @ApiProperty({ description: 'ID of the SPOC (Single Point of Contact)' })
  @IsNotEmpty()
  @IsInt()
  spoc_id: number;

  @ApiProperty({ enum: SpocType, description: 'Type of SPOC' })
  @IsEnum(SpocType)
  @IsNotEmpty()
  spoc_type: SpocType;

  @ApiPropertyOptional({
    description: 'Optional metadata as key-value pairs',
    type: 'object',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
