import { Controller, Get, Body, Patch, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody } from '@nestjs/swagger';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { LeadLevelService } from '../services/lead-level.service';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { UpdateLeadLevelDto } from '../dto/update-lead.dto';

@ApiTags('leads/levels')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads/levels')
export class LeadLevelController {
  constructor(private readonly leadLevelService: LeadLevelService) {}

  @Get('all')
  @ApiOperation({ summary: 'Get all lead levels' })
  async getAllLeadLevels(
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<any>> {
    const clientId = req.user?.currentClientId;
    return this.leadLevelService.getLevelsByClientId(clientId);
  }

  @Patch('program-interest')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update lead program interest level' })
  @ApiBody({ type: UpdateLeadLevelDto })
  async updateLeadProgInterestLevel(
    @Body() data: UpdateLeadLevelDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<LeadProgramInterest> {
    const userId = req.user?.id || null;
    return this.leadLevelService.updateLeadProgIntLevel({
      programInterestId: data.leadProgInterestId,
      currentLevelId: data.currentLevelId,
      newLevelId: data.newLevelId,
      nextFollowUpDate: data.nextFollowUpDate,
      comments: data.comments,
      userId: userId,
    });
  }
}
