import {
  <PERSON>,
  Post,
  Body,
  Param,
  Req,
  UseGuards,
  Get,
  Patch,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource } from '@modules/permissions/enums/permission.enum';
import { WebinarService } from '../../webinars/services/webinar.service';
import { RegisterSingleLeadForWebinarDto } from '@modules/webinars/dto/register-single-lead-for-webinar.dto';
import { UpdateWebinarStatusDto } from '@modules/webinars/dto/update-webinar-status.dto';
import { WebinarLeadService } from '@modules/webinars/services/webinar-lead.service';

@ApiTags('leads/webinars')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads')
export class LeadWebinarController {
  constructor(
    private readonly webinarService: WebinarService,
    private readonly webinarleadService: WebinarLeadService,
  ) {}

  /**
   * !Commented out due to confusing nature of the endpoint.
   */

  // Webinar registration
  // @Post('program-interests/:id/register-for-webinars')
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Register lead for all upcoming webinars' })
  // @ApiParam({ name: 'id', description: 'Lead program interest ID' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       clientId: { type: 'number' },
  //       programId: { type: 'number', nullable: true },
  //     },
  //     required: ['clientId'],
  //   },
  // })

  // async registerForAllWebinars(
  //   @Param('id') id: string,
  //   @Body('clientId') clientId: number,
  //   @Req() req: AuthenticatedRequest,
  //   @Body('programId') programId?: number,
  // ): Promise<{ registered: number; message: string }> {
  //   const userId = req.user?.id || null;
  //   const result = await this.webinarService.registerLeadForAllUpcomingWebinars(
  //     {
  //       leadProgramInterestId: +id,
  //       clientId: clientId,
  //       programId: programId,
  //     },
  //     userId,
  //   );
  //   return {
  //     registered: result.registered,
  //     message:
  //       result.registered > 0
  //         ? `Successfully registered for ${result.registered} upcoming webinars`
  //         : 'No new webinar registrations were created. Either all webinars are already registered or there are no upcoming webinars with auto-registration enabled.',
  //   };
  // }

  @Get(':id/completed-webinars')
  @ApiOperation({
    summary: 'Get all Completed webinars for a lead',
  })
  @ApiParam({ name: 'id', description: 'Lead Id' })
  async getUpcomingLeadWebinars(@Param('id') id: string): Promise<any> {
    return this.webinarService.findWebinarRegistrationsForLeadWithUpcomingWebinars(
      +id,
    );
  }

  @Post('register-single-lead')
  @ApiOperation({
    summary: 'Register a single lead for a specific webinar',
  })
  @ApiBody({ type: RegisterSingleLeadForWebinarDto })
  async registerSingleLeadForWebinar(
    @Body() dto: RegisterSingleLeadForWebinarDto,
    @Req() req: any,
  ): Promise<any> {
    const userId = req.user.id; // Assuming user ID is available in request context
    return this.webinarService.registerLeadForWebinar(dto, userId);
  }

  @Patch('update-status/webinar')
  @ApiOperation({
    summary: 'Update the status of a webinar registration',
  })
  @ApiBody({ type: UpdateWebinarStatusDto })
  async updateWebinarStatus(@Body() dto: UpdateWebinarStatusDto): Promise<any> {
    return this.webinarService.updateWebinarStatus(dto);
  }

  /**
   * This endpoint retrieves all upcoming webinars that a lead has not registered for and all upcoming webinars lead has registered for.
   */
  @Get('unregistered-webinars/:leadId')
  @ApiOperation({
    summary: 'Get unregistered webinars for a lead',
  })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  async getUnregisteredWebinarsForLead(
    @Param('leadId') leadId: string,
  ): Promise<any> {
    return this.webinarleadService.findUpcomingWebinarsForLead(+leadId);
  }
}
