import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import { LeadSpocService } from '../services/lead-spoc.service';
import { CreateLeadSpocDto } from '../dto/create-lead-spoc.dto';
import { LeadSpoc } from '../entities/lead-spoc.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';

@ApiTags('leads/spocs')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads')
export class LeadSpocController {
  constructor(private readonly leadSpocService: LeadSpocService) {}

  @Get('spocs/:id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get SPOC by ID' })
  @ApiParam({ name: 'id', description: 'SPOC ID' })
  async findOneLeadSpoc(@Param('id') id: string): Promise<LeadSpoc> {
    return this.leadSpocService.findOne(+id);
  }

  @Post(':leadId/spocs')
  @HttpCode(HttpStatus.CREATED)
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Create a SPOC for a lead' })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  @ApiBody({ type: CreateLeadSpocDto })
  async createLeadSpoc(
    @Param('leadId') leadId: string,
    @Body() createLeadSpocDto: CreateLeadSpocDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<LeadSpoc> {
    const clientId = req.user?.currentClientId;
    createLeadSpocDto.lead_id = +leadId;
    return this.leadSpocService.create({
      ...createLeadSpocDto,
      client_id: clientId,
    });
  }

  @Get(':leadId/spocs')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get all SPOCs assigned to a lead' })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  async findAllLeadSpocs(@Param('leadId') leadId: string): Promise<LeadSpoc[]> {
    return this.leadSpocService.findByLeadId(+leadId);
  }

  @Patch('spocs/:id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update SPOC details' })
  @ApiParam({ name: 'id', description: 'SPOC ID' })
  @ApiBody({ schema: { type: 'object', additionalProperties: true } }) // Partial update, allow any properties
  async updateLeadSpoc(
    @Param('id') id: string,
    @Body() updateLeadSpocData: Partial<LeadSpoc>,
  ): Promise<LeadSpoc> {
    return this.leadSpocService.update(+id, updateLeadSpocData);
  }

  @Delete('spocs/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequirePermission({ resource: Resource.LEAD, action: Action.DELETE })
  @ApiOperation({ summary: 'Delete a SPOC by ID' })
  @ApiParam({ name: 'id', description: 'SPOC ID' })
  async removeLeadSpoc(@Param('id') id: string): Promise<void> {
    return this.leadSpocService.remove(+id);
  }
}
