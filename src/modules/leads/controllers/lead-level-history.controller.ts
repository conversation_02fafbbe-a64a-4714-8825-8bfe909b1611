import {
  Controller,
  Get,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { LeadLevelHistoryService } from '../services/lead-level-history.service';
import { LeadLevelHistory } from '../entities/lead-level-history.entity';
import { PaginatedResponse } from '../../../common/interfaces/pagination.interface';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';

@ApiTags('leads/level-history')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads')
export class LeadLevelHistoryController {
  constructor(
    private readonly leadLevelHistoryService: LeadLevelHistoryService,
  ) {}

  @Get('program-interests/:programInterestId/level-history')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get level history for a program interest' })
  @ApiParam({ name: 'programInterestId', description: 'Program interest ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Level history retrieved successfully',
    type: [LeadLevelHistory],
  })
  async getHistoryByProgramInterest(
    @Param('programInterestId', ParseIntPipe) programInterestId: number,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 20,
  ): Promise<PaginatedResponse<LeadLevelHistory>> {
    return this.leadLevelHistoryService.getHistoryByProgramInterest(
      programInterestId,
      page,
      limit,
    );
  }

  @Get(':leadId/level-history')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({
    summary: 'Get level history for all program interests of a lead',
  })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Level history retrieved successfully',
    type: [LeadLevelHistory],
  })
  async getHistoryByLead(
    @Param('leadId', ParseIntPipe) leadId: number,
    @Query('page', ParseIntPipe) page: number = 1,
    @Query('limit', ParseIntPipe) limit: number = 20,
  ): Promise<PaginatedResponse<LeadLevelHistory>> {
    return this.leadLevelHistoryService.getHistoryByLead(leadId, page, limit);
  }
}
