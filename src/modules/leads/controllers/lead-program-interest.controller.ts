import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import { LeadProgramInterestService } from '../services/lead-program-interest.service';
import { CreateLeadProgramInterestDto } from '../dto/create-lead-program-interest.dto';
import { UpdateLeadProgramInterestDto } from '../dto/update-lead-program-interest.dto';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';

@ApiTags('leads/program-interests')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads')
export class LeadProgramInterestController {
  constructor(
    private readonly leadProgramInterestService: LeadProgramInterestService,
  ) {}

  @Get('program-interests/:id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get program interest by ID' })
  @ApiParam({ name: 'id', description: 'Program interest ID' })
  async findOneProgramInterest(
    @Param('id') id: string,
  ): Promise<LeadProgramInterest> {
    return this.leadProgramInterestService.findOne(+id);
  }

  @Post(':leadId/program-interests')
  @HttpCode(HttpStatus.CREATED)
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Create a program interest for a lead' })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  @ApiBody({ type: CreateLeadProgramInterestDto })
  async createProgramInterest(
    @Param('leadId') leadId: string,
    @Body() createLeadProgramInterestDto: CreateLeadProgramInterestDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<LeadProgramInterest> {
    const userId = req.user?.id || null;
    return this.leadProgramInterestService.create(
      createLeadProgramInterestDto,
      +leadId,
      userId,
    );
  }

  @Get(':leadId/program-interests')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get all program interests for a lead' })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  async findAllProgramInterests(
    @Param('leadId') leadId: string,
  ): Promise<LeadProgramInterest[]> {
    return this.leadProgramInterestService.findAllByLeadId(+leadId);
  }

  @Patch('program-interests/:id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update program interest details' })
  @ApiParam({ name: 'id', description: 'Program interest ID' })
  @ApiBody({ type: UpdateLeadProgramInterestDto })
  async updateProgramInterest(
    @Param('id') id: string,
    @Body() updateLeadProgramInterestDto: UpdateLeadProgramInterestDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<LeadProgramInterest> {
    const userId = req.user?.id || null;
    return this.leadProgramInterestService.update(
      +id,
      updateLeadProgramInterestDto,
      userId,
    );
  }

  @Patch(':leadId/program-interests/:programId/set-primary')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Set primary program for a lead' })
  @ApiParam({ name: 'leadId', description: 'Lead ID' })
  @ApiParam({ name: 'programId', description: 'Program ID' })
  async setPrimaryProgram(
    @Param('leadId') leadId: string,
    @Param('programId') programId: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<void> {
    const userId = req.user?.id || null;
    return this.leadProgramInterestService.setPrimaryProgram(
      +leadId,
      +programId,
      userId,
    );
  }

  @Delete('program-interests/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequirePermission({ resource: Resource.LEAD, action: Action.DELETE })
  @ApiOperation({ summary: 'Delete a program interest by ID' })
  @ApiParam({ name: 'id', description: 'Program interest ID' })
  async removeProgramInterest(@Param('id') id: string): Promise<void> {
    return this.leadProgramInterestService.remove(+id);
  }
}
