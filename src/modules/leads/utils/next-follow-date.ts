import { LeadLevel } from '../entities/lead-level.entity';
import { DurationUnit } from '../enums/lead-level.enum';

export const getNextFollowUpDateByLevel = (
  level: LeadLevel,
  nextFollowUpDate: string,
): Date | null => {
  let validatedNextFollowUpDate: Date | null = null;
  if (!level.is_final) {
    // If next follow-up date is provided, validate it
    if (nextFollowUpDate && level.default_fud_changeable_by_spoc) {
      const parsedDate = new Date(nextFollowUpDate);

      // Check if it's a valid date
      if (isNaN(parsedDate.getTime())) {
        throw new Error('Invalid next follow-up date format');
      }

      // Check if the date is in the future
      const currentDate = new Date();
      if (parsedDate < currentDate) {
        throw new Error('Next follow-up date must be in the future');
      }

      // Apply max duration restriction if configured
      if (
        level.max_fud_duration_value &&
        level.max_fud_duration_unit !== DurationUnit.None
      ) {
        const maxAllowedDate = new Date();

        switch (level.max_fud_duration_unit) {
          case DurationUnit.Days:
            maxAllowedDate.setDate(
              maxAllowedDate.getDate() + level.max_fud_duration_value,
            );
            break;
          case DurationUnit.Hours:
            maxAllowedDate.setHours(
              maxAllowedDate.getHours() + level.max_fud_duration_value,
            );
            break;
          case DurationUnit.Months:
            maxAllowedDate.setMonth(
              maxAllowedDate.getMonth() + level.max_fud_duration_value,
            );
            break;
        }

        if (parsedDate > maxAllowedDate) {
          throw new Error(
            `Next follow-up date exceeds maximum allowed duration of ${level.max_fud_duration_value} ${level.max_fud_duration_unit.toLowerCase()}`,
          );
        }
      }

      validatedNextFollowUpDate = parsedDate;
    }
    // If no date provided but a default is configured, use that
    else if (
      level.default_fud_duration_value &&
      level.default_fud_duration_unit !== DurationUnit.None
    ) {
      validatedNextFollowUpDate = new Date();

      switch (level.default_fud_duration_unit) {
        case DurationUnit.Days:
          validatedNextFollowUpDate.setDate(
            validatedNextFollowUpDate.getDate() +
              level.default_fud_duration_value,
          );
          break;
        case DurationUnit.Hours:
          validatedNextFollowUpDate.setHours(
            validatedNextFollowUpDate.getHours() +
              level.default_fud_duration_value,
          );
          break;
        case DurationUnit.Months:
          validatedNextFollowUpDate.setMonth(
            validatedNextFollowUpDate.getMonth() +
              level.default_fud_duration_value,
          );
          break;
      }
    }
  }

  return validatedNextFollowUpDate;
};
