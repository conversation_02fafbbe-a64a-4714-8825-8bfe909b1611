import { LeadSessionIds } from '@modules/session/enums/lead-session-status.enum';

export class LeadStatusUtil {
  /**
   * Extract S1 and S2 counselling statuses from lead sessions
   * EXACT SAME LOGIC as original
   */
  static extractCounsellingStatuses(leadSessions: any[]): {
    s1CounsellingStatus: string | null;
    s2CounsellingStatus: string | null;
  } {
    let s1CounsellingStatus = null;
    let s2CounsellingStatus = null;

    if (leadSessions && leadSessions.length > 0) {
      // Filter S1 counselling sessions and get the latest one - EXACT SAME LOGIC
      const s1Sessions = leadSessions
        .filter(
          (session) =>
            session.session &&
            session.session_config_id === LeadSessionIds.S1_COUNSELING,
        )
        .sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );

      // Filter S2 counselling sessions and get the latest one - EXACT SAME LOGIC
      const s2Sessions = leadSessions
        .filter(
          (session) =>
            session.session &&
            session.session_config_id === LeadSessionIds.S2_COUNSELING,
        )
        .sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );

      // Get the status from the latest sessions - EXACT SAME LOGIC
      if (s1Sessions.length > 0) {
        s1CounsellingStatus = s1Sessions[0].complete_status;
      }

      if (s2Sessions.length > 0) {
        s2CounsellingStatus = s2Sessions[0].complete_status;
      }
    }

    return { s1CounsellingStatus, s2CounsellingStatus };
  }

  /**
   * Extract latest interview status from program interests
   * EXACT SAME LOGIC as original
   */
  static extractInterviewStatus(programInterests: any[]): string | null {
    let interviewStatus = null;
    for (const interest of programInterests) {
      if (interest.interviews && interest.interviews.length > 0) {
        // Sort interviews by created_at in descending order and get the latest - EXACT SAME LOGIC
        const latestInterview = [...interest.interviews].sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        )[0];

        interviewStatus = latestInterview.interview_status;
        break; // Take the first interview status found - EXACT SAME LOGIC
      }
    }
    return interviewStatus;
  }

  /**
   * Extract past and upcoming webinar statuses from program interests
   * EXACT SAME LOGIC as original
   */
  static extractWebinarStatuses(programInterests: any[]): {
    pastWebinarStatus: string | null;
    upcomingWebinarStatus: string | null;
  } {
    // Get past and upcoming webinar statuses from program interests - EXACT SAME LOGIC
    let pastWebinarStatus = null;
    let upcomingWebinarStatus = null;
    const currentDate = new Date();

    for (const interest of programInterests) {
      if (
        interest.webinar_registrations &&
        interest.webinar_registrations.length > 0
      ) {
        // Separate past and upcoming webinars - EXACT SAME LOGIC
        const pastWebinars = interest.webinar_registrations
          .filter(
            (wr) => wr.webinar && new Date(wr.webinar.date_time) < currentDate,
          )
          .sort(
            (a, b) =>
              new Date(b.webinar.date_time).getTime() -
              new Date(a.webinar.date_time).getTime(),
          );

        const upcomingWebinars = interest.webinar_registrations
          .filter(
            (wr) => wr.webinar && new Date(wr.webinar.date_time) >= currentDate,
          )
          .sort(
            (a, b) =>
              new Date(a.webinar.date_time).getTime() -
              new Date(b.webinar.date_time).getTime(),
          );

        // Get the most recent past webinar status - EXACT SAME LOGIC
        if (pastWebinars.length > 0 && !pastWebinarStatus) {
          pastWebinarStatus = pastWebinars[0].webinar_status;
        }

        // Get the earliest upcoming webinar status - EXACT SAME LOGIC
        if (upcomingWebinars.length > 0 && !upcomingWebinarStatus) {
          upcomingWebinarStatus = upcomingWebinars[0].webinar_status;
        }

        // Break if we have both statuses - EXACT SAME LOGIC
        if (pastWebinarStatus && upcomingWebinarStatus) {
          break;
        }
      }
    }

    return { pastWebinarStatus, upcomingWebinarStatus };
  }
}
