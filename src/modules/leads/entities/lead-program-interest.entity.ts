import { Program } from 'src/modules/programs/entities/program.entity';
import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  OneToMany,
  Unique,
} from 'typeorm';
import { Lead } from './lead.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { NetEnquiry } from 'src/modules/net-enquiries/entities/net-enquiry.entity';
import { WebinarRegistration } from 'src/modules/webinars/entities/webinar-registration.entity';
import { LeadSource } from '@modules/lead-allocations/entities';
import { LeadLevel } from './lead-level.entity';
import { LeadCallStatus } from '../enums/lead-status.enum';
import { Interview } from 'src/modules/interviews/entities/interview.entity';

@Entity()
@Unique(['lead_id', 'program_id'])
export class LeadProgramInterest extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Lead, (lead) => lead.program_interests, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @Column({ type: 'integer' })
  lead_id: number;

  @Index()
  @ManyToOne(() => Program, (program) => program.leadProgramInterests)
  @JoinColumn({ name: 'program_id' })
  program: Program;

  @OneToMany(() => NetEnquiry, (netEnquiry) => netEnquiry.lead_program_interest)
  net_enquiries: NetEnquiry[];

  @Column({ type: 'integer' })
  program_id: number;

  @ManyToOne(() => LeadSource)
  @JoinColumn({ name: 'lead_source_id' })
  lead_source: LeadSource;

  @Column({ type: 'integer', nullable: true })
  lead_source_id: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'boolean', default: false })
  is_primary: boolean;

  @Column({ type: 'timestamp with time zone', nullable: true })
  re_enquiry_date: Date;

  @OneToMany(
    () => WebinarRegistration,
    (webinarRegistration) => webinarRegistration.lead_program_interest,
  )
  webinar_registrations: WebinarRegistration[];

  @Index()
  @Column({
    type: 'timestamp with time zone',
    nullable: true,
  })
  last_call_date: Date;

  @Column({ type: 'boolean', nullable: false, default: false })
  is_DND: boolean;

  @Column({ type: 'enum', enum: LeadCallStatus, nullable: true })
  call_status: LeadCallStatus;

  @Index()
  @ManyToOne(() => LeadLevel)
  @JoinColumn({ name: 'lead_level_id' })
  lead_level: LeadLevel;

  @Column({ type: 'integer', nullable: true })
  lead_level_id: number;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @OneToMany(() => Interview, (interview) => interview.lead_program_interest)
  interviews: Interview[];
}
