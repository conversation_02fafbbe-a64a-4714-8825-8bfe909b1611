import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>To<PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>umn,
  Index,
  OneToOne,
} from 'typeorm';
import { Lead } from './lead.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { SpocType } from '../enums/spoc.enum';
import { User } from '@modules/users/entities/user.entity';

@Entity()
export class LeadSpoc extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Lead, (lead) => lead.spocs)
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @Column({ type: 'integer', nullable: false })
  lead_id: number;

  @Index()
  @ManyToOne(() => User)
  @JoinColumn({ name: 'spoc_id' })
  spoc: User;

  @Column({ type: 'integer', nullable: false })
  spoc_id: number;

  @Column({
    type: 'enum',
    enum: SpocType,
    nullable: false,
  })
  spoc_type: SpocType;

  @Column({ type: 'boolean', default: false })
  is_initial_owner: boolean;

  @OneToOne(() => Lead, (lead) => lead.owner_spoc)
  owner_relation: Lead;
}
