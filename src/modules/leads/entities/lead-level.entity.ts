import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import {
  AllowedTransitionMethod,
  DurationUnit,
  EnrollmentStage,
  LeadTemperature,
  ReEnquiryAction,
} from '../enums/lead-level.enum';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Program } from '@modules/programs/entities/program.entity';

export interface RequiredPreviousLevelCondition {
  requiredPreviousLevelIds?: number[];
}

@Entity()
@Index(['client', 'name', 'program_id'], {
  unique: true,
})
export class LeadLevel extends ClientAwareEntity {
  @Column({
    type: 'integer',
  })
  program_id: number;

  @ManyToOne(() => Program)
  @JoinColumn({ name: 'program_id' })
  program: Program;

  @Column({
    length: 50,
  })
  name: string; // e.g., 'D2', 'D3#'

  @Column({
    type: 'text',
    nullable: true,
  })
  definition: string | null;

  @Column({
    type: 'text',
    nullable: true,
  })
  description: string | null;

  @Column({
    default: false,
  })
  is_default_for_ne: boolean;

  @Column({
    type: 'int',
    default: 0,
  })
  display_order: number;

  @Column({
    type: 'json',
    nullable: true,
  })
  allowed_downgrade_level_ids_json: number[] | null;

  @Column({
    type: 'json',
    nullable: true,
  })
  allowed_transition_methods: AllowedTransitionMethod[] | null;

  @Column({
    type: 'enum',
    enum: ReEnquiryAction,
    default: ReEnquiryAction.NoAction,
  })
  re_enquiry_action: ReEnquiryAction;

  @Column({
    type: 'int',
    nullable: true,
  })
  re_enquiry_target_level_id: number | null; // Column to store the self-referencing FK

  @ManyToOne(() => LeadLevel, {
    nullable: true,
  })
  @JoinColumn({
    name: 're_enquiry_target_level_id',
  })
  re_enquiry_target_level: LeadLevel | null; // Relationship property to another LeadLevel

  @Column({
    default: false,
  })
  has_re_tag: boolean;

  @Column({
    type: 'json',
    nullable: true,
  })
  required_previous_level_ids_json: RequiredPreviousLevelCondition | null;

  @Column({
    type: 'int',
    nullable: true,
  })
  default_fud_duration_value: number | null;

  @Column({
    type: 'enum',
    enum: DurationUnit,
    default: DurationUnit.None,
  })
  default_fud_duration_unit: DurationUnit;

  @Column({
    default: true,
  })
  default_fud_changeable_by_spoc: boolean;

  @Column({
    default: false,
  })
  is_fud_mandatory_by_spoc: boolean;

  @Column({
    type: 'int',
    nullable: true,
  })
  max_fud_duration_value: number | null;

  @Column({
    type: 'enum',
    enum: DurationUnit,
    default: DurationUnit.None,
  })
  max_fud_duration_unit: DurationUnit;

  @Column({
    default: false,
  })
  is_final: boolean;

  @Column({
    default: false,
  })
  should_move_to_x_acc: boolean;

  @Column({
    default: false,
  })
  is_positive: boolean;

  @Column({
    type: 'enum',
    enum: LeadTemperature,
    default: LeadTemperature.Cold,
    nullable: true,
  })
  temperature: LeadTemperature;

  @Column({
    type: 'enum',
    enum: EnrollmentStage,
    default: EnrollmentStage.PreEnroll,
  })
  enrollment_stage: EnrollmentStage;
}
