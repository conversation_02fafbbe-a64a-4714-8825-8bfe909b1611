import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import { ClientAwareEntity } from '../../../common/entities/client-aware.entity';
import { LeadProgramInterest } from './lead-program-interest.entity';
import { LeadLevel } from './lead-level.entity';
import { User } from '../../users/entities/user.entity';
import { AllowedTransitionMethodEnum } from '../enums/lead-level.enum';

@Entity('lead_level_history')
export class LeadLevelHistory extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => LeadProgramInterest, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lead_program_interest_id' })
  lead_program_interest: LeadProgramInterest;

  @Column({ type: 'integer' })
  lead_program_interest_id: number;

  @ManyToOne(() => LeadLevel)
  @JoinColumn({ name: 'from_level_id' })
  from_level: LeadLevel;

  @Column({ type: 'integer', nullable: true })
  from_level_id: number;

  @ManyToOne(() => LeadLevel)
  @JoinColumn({ name: 'to_level_id' })
  to_level: LeadLevel;

  @Column({ type: 'integer' })
  to_level_id: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'changed_by_user_id' })
  changed_by_user: User;

  @Column({ type: 'integer', nullable: true })
  changed_by_user_id: number;

  @Column({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  change_timestamp: Date;

  @Column({ type: 'text', nullable: true })
  comments: string;

  @Column({
    type: 'enum',
    enum: AllowedTransitionMethodEnum,
    default: AllowedTransitionMethodEnum.Manual,
  })
  transition_method: AllowedTransitionMethodEnum;

  @Column({ type: 'integer', nullable: true })
  disposition_history_id: number;
}
