import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Lead } from './entities/lead.entity';
import { LeadProgramInterest } from './entities/lead-program-interest.entity';
import { LeadProgramInterestService } from './services/lead-program-interest.service';
import { ProgramModule } from '../programs/program.module';
import { WebinarModule } from '../webinars/webinar.module';
import { LeadService } from './services/lead.service';
import { ContactModule } from '../contacts/contact.module';
import { LeadLevel } from './entities/lead-level.entity';
import { LeadSpoc } from './entities/lead-spoc.entity';
import { LeadSpocService } from './services/lead-spoc.service';
import { LeadLevelService } from './services/lead-level.service';
import { CallLogModule } from '@modules/call-logs/call-log.module';
import { LeadAllocationModule } from '@modules/lead-allocations/lead-allocation.module';
import { Program } from '@modules/programs/entities/program.entity';
import { LeadContactService } from './services/lead-contact.service';
import { LeadDetailService } from './services/lead-detail.service';
import { LeadLevelHistory } from './entities/lead-level-history.entity';
import { LeadLevelHistoryService } from './services/lead-level-history.service';

// Import all controllers
import { LeadController } from './controllers/lead.controller';
import { LeadProgramInterestController } from './controllers/lead-program-interest.controller';
import { LeadSpocController } from './controllers/lead-spoc.controller';
import { LeadLevelController } from './controllers/lead-level.controller';
import { LeadWebinarController } from './controllers/lead-webinar.controller';
import { LeadContactController } from './controllers/lead-contact.controller';
import { CallLog } from '@modules/call-logs/entities/call-log.entity';
import { LeadLevelHistoryController } from './controllers/lead-level-history.controller';
import { DispositionHistory } from '@modules/dispositions/entities/disposition-history.entity';
import { LeadTransformationService } from './services/lead-transformation.service';
import { NetEnquiry } from '@modules/net-enquiries/entities/net-enquiry.entity';
import { CampaignModule } from '@modules/campaigns/campaign.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Lead,
      LeadProgramInterest,
      LeadLevel,
      LeadSpoc,
      Program,
      CallLog,
      LeadLevelHistory,
      DispositionHistory,
      NetEnquiry,
    ]),
    ProgramModule,
    CampaignModule,
    forwardRef(() => WebinarModule),
    ContactModule,
    LeadAllocationModule,
    forwardRef(() => CallLogModule),
  ],
  controllers: [
    LeadController,
    LeadProgramInterestController,
    LeadSpocController,
    LeadLevelController,
    LeadContactController,
    LeadWebinarController,
    LeadLevelHistoryController,
  ],
  providers: [
    LeadService,
    LeadProgramInterestService,
    LeadSpocService,
    LeadLevelService,
    LeadContactService,
    LeadDetailService,
    LeadLevelHistoryService,
    LeadTransformationService,
  ],
  exports: [
    LeadService,
    LeadProgramInterestService,
    LeadSpocService,
    LeadLevelService,
    LeadContactService,
    LeadDetailService,
    LeadLevelHistoryService,
  ],
})
export class LeadModule {}
