// Define Enums corresponding to your database ENUMs
export enum ReEnquiryAction {
  NoAction = 'NoAction',
  AutoTransitionToSpecific = 'AutoTransitionToSpecific',
  TriggerWorkflow = 'TriggerWorkflow',
}

export enum DurationUnit {
  Hours = 'Hours',
  Days = 'Days',
  Months = 'Months',
  None = 'None',
}

export enum LeadTemperature {
  Hot = 'Hot',
  Warm = 'Warm',
  Cold = 'Cold',
}

export enum EnrollmentStage {
  PreEnroll = 'PreEnroll',
  Enrolled = 'Enrolled',
  PostEnroll = 'PostEnroll',
}
// Type for allowed_transition_methods SET - TypeORM doesn't have a SET type,
// using string array + JSON column or transformer is common. Let's use JSON array of strings.
export type AllowedTransitionMethod =
  | 'Manual'
  | 'AutoDisposition'
  | 'ReEnquiry'
  | 'System';

export enum AllowedTransitionMethodEnum {
  System = 'System',
  Manual = 'Manual',
  AutoDisposition = 'AutoDisposition',
  ReEnquiry = 'ReEnquiry',
}

export enum LeadType {
  NE = 'ne',
  RE = 're',
}
