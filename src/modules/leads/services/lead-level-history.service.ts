import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeadLevelHistory } from '../entities/lead-level-history.entity';
import { PaginatedResponse } from '../../../common/interfaces/pagination.interface';

@Injectable()
export class LeadLevelHistoryService {
  constructor(
    @InjectRepository(LeadLevelHistory)
    private readonly leadLevelHistoryRepository: Repository<LeadLevelHistory>,
  ) {}

  /**
   * Get level history for a specific lead program interest
   */
  async getHistoryByProgramInterest(
    leadProgramInterestId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<LeadLevelHistory>> {
    const skip = (page - 1) * limit;

    const [data, total] = await this.leadLevelHistoryRepository.findAndCount({
      where: { lead_program_interest_id: leadProgramInterestId },
      relations: [
        'from_level',
        'to_level',
        'changed_by_user',
        'lead_program_interest',
        'lead_program_interest.program',
      ],
      order: { change_timestamp: 'DESC' },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        size: limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Get level history for all program interests of a lead
   */
  async getHistoryByLead(
    leadId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<LeadLevelHistory>> {
    const skip = (page - 1) * limit;

    const [data, total] = await this.leadLevelHistoryRepository
      .createQueryBuilder('history')
      .leftJoinAndSelect('history.from_level', 'fromLevel')
      .leftJoinAndSelect('history.to_level', 'toLevel')
      .leftJoinAndSelect('history.changed_by_user', 'changedByUser')
      .leftJoinAndSelect('history.lead_program_interest', 'programInterest')
      .leftJoinAndSelect('programInterest.program', 'program')
      .leftJoinAndSelect('programInterest.lead', 'lead')
      .where('lead.id = :leadId', { leadId })
      .orderBy('history.change_timestamp', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        page,
        size: limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Get level history by disposition history ID
   */
  async getHistoryByDisposition(
    dispositionHistoryId: number,
  ): Promise<LeadLevelHistory[]> {
    return this.leadLevelHistoryRepository.find({
      where: { disposition_history_id: dispositionHistoryId },
      relations: [
        'from_level',
        'to_level',
        'changed_by_user',
        'lead_program_interest',
        'lead_program_interest.program',
      ],
      order: { change_timestamp: 'DESC' },
    });
  }
}
