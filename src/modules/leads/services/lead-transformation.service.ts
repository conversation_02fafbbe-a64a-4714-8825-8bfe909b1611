import { Injectable } from '@nestjs/common';
import { LeadStatusUtil } from '../utils/lead-status.util';

@Injectable()
export class LeadTransformationService {
  /**
   * Transform lead data for API response
   * EXACT SAME LOGIC as original, just organized
   */
  transformLeadForResponse(
    lead: any,
    lastConnectedCallDates: Map<number, Date | null>,
  ): any {
    const { contact, program_interests, owner_spoc, lead_sessions } = lead;

    // Extract unique sources from program_interests - EXACT SAME LOGIC
    const sourcesMap = new Map();
    program_interests.forEach((interest) => {
      if (interest.lead_source && !sourcesMap.has(interest.lead_source.id)) {
        sourcesMap.set(interest.lead_source.id, {
          id: interest.lead_source.id,
          name: interest.lead_source.name,
        });
      }
    });

    // Convert map to array for the response - EXACT SAME LOGIC
    const sources = Array.from(sourcesMap.values());

    // Find the latest and first enquiry dates across all program interests

    const first_enquiry = { campaign: null, source: null, date: null };
    const latest_enquiry = { campaign: null, source: null, date: null };

    // Collect all enquiries with their metadata in one pass
    const allEnquiries = program_interests
      .filter((interest) => interest.net_enquiries?.length > 0)
      .flatMap((interest) =>
        interest.net_enquiries.map((enquiry) => ({
          created_at: enquiry.created_at,
          campaign: enquiry.campaign,
          interest_lead_source: interest.lead_source,
        })),
      );

    if (allEnquiries.length > 0) {
      // Sort once and extract both first and last
      const sortedEnquiries = allEnquiries.sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
      );

      // First enquiry (earliest)
      const firstEnquiry = sortedEnquiries[0];
      first_enquiry.source = firstEnquiry.interest_lead_source?.name || null;
      first_enquiry.campaign = firstEnquiry.campaign?.campaign_name || null;
      first_enquiry.date = firstEnquiry.created_at;

      // Latest enquiry (most recent)
      const latestEnquiry = sortedEnquiries[sortedEnquiries.length - 1];
      latest_enquiry.source = latestEnquiry.interest_lead_source?.name || null;
      latest_enquiry.campaign = latestEnquiry.campaign?.campaign_name || null;
      latest_enquiry.date = latestEnquiry.created_at;
    }

    // Extract all statuses using utility functions (EXACT SAME LOGIC inside utilities)
    const { s1CounsellingStatus, s2CounsellingStatus } =
      LeadStatusUtil.extractCounsellingStatuses(lead_sessions);

    const interviewStatus =
      LeadStatusUtil.extractInterviewStatus(program_interests);

    const { pastWebinarStatus, upcomingWebinarStatus } =
      LeadStatusUtil.extractWebinarStatuses(program_interests);

    // Transform the owner_spoc to have a more descriptive structure - EXACT SAME LOGIC
    const ownerDetails = owner_spoc
      ? {
          ...owner_spoc,
          // Rename the nested "spoc" to "user_details" for clarity
          spoc_details: owner_spoc.spoc,
          // Remove the original spoc property to avoid duplication
          spoc: undefined,
        }
      : null;

    const contactWithMaskedInfo = contact
      ? {
          ...contact,
          masked_primary_phone:
            contact.primary_phone?.masked_phone_number || null,
          masked_primary_email: contact.primary_email?.masked_email || null,
          // Remove the emails and phones arrays
          emails: undefined,
          phones: undefined,
          primary_email: undefined,
          primary_phone: undefined,
        }
      : null;

    // Get the last connected call date for this lead - EXACT SAME LOGIC
    const lastConnectedCallDate = lastConnectedCallDates.get(lead.id) || null;

    // Remove unwanted nested objects from program_interests - EXACT SAME LOGIC
    const cleanProgramInterests = program_interests.map((interest) => ({
      ...interest,
      interviews: undefined,
      webinar_registrations: undefined,
    }));

    return {
      lead: {
        ...lead,
        contact: contactWithMaskedInfo,
        lead_sessions: undefined,
        program_interests: cleanProgramInterests,
        first_enquiry: first_enquiry,
        latest_enquiry: latest_enquiry,
        last_connected_call_date: lastConnectedCallDate,
        sources,
        s1_counselling_status: s1CounsellingStatus,
        s2_counselling_status: s2CounsellingStatus,
        interview_status: interviewStatus,
        past_webinar_status: pastWebinarStatus,
        upcoming_webinar_status: upcomingWebinarStatus,
      },
      lead_uuid: lead.lead_uuid,
      spoc: ownerDetails,
    };
  }
}
