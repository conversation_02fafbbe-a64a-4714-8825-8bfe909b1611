import { ContactService } from '@modules/contacts/services/contact.service';
import { Injectable } from '@nestjs/common';
import { Lead } from '../entities/lead.entity';
import { LeadService } from './lead.service';

@Injectable()
export class LeadContactService {
  constructor(
    private readonly contactService: ContactService,
    private readonly leadService: LeadService,
  ) {}

  async updateContactMetaDetails(
    leadId: number,
    userId: number,
    metaDetails: Record<string, any>,
  ): Promise<{ data: Lead; success: boolean }> {
    try {
      const lead = await this.leadService.findOne(leadId);

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      const contactId = lead.contact_id;
      if (!contactId) {
        throw new Error('Contact ID is required to update meta details');
      }

      await this.contactService.updateMetadata(metaDetails, contactId);

      if (!metaDetails || typeof metaDetails !== 'object') {
        throw new Error('Meta details must be a valid object');
      }

      return {
        data: lead,
        success: true,
      };
      // Update the contact's metadata
    } catch (error) {
      console.error('Error updating contact meta details:', error);
      throw new Error('Failed to update contact meta details');
    }
  }

  async handleContactNameUpdate(
    leadId: number,
    userId: number,
    firstName: string,
    lastName?: string,
  ): Promise<Lead> {
    try {
      const lead = await this.leadService.findOne(leadId);

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      const contactId = lead.contact_id;
      if (!contactId) {
        throw new Error('Contact ID is required to update name');
      }

      // Update the contact's name
      await this.contactService.updateContactName({
        firstName,
        lastName,
        contactId,
        userId,
      });

      // Refetch the lead to get the updated contact information
      return await this.leadService.findOne(leadId);
    } catch (error) {
      console.error('Error updating contact name:', error);
      throw new Error('Failed to update contact name');
    }
  }

  async viewEmail(emailId: number, userId: number, clientId: number) {
    const email = await this.contactService.getContactFromEmailId(emailId);
    const lead = await this.leadService.findByClientAndContact(
      clientId,
      email?.contact?.id,
    );
    return this.contactService.viewEmail(email, userId, lead?.id);
  }

  async viewPhone(phoneId: number, userId: number, clientId: number) {
    const phone = await this.contactService.getContactFromPhoneId(phoneId);
    const lead = await this.leadService.findByClientAndContact(
      clientId,
      phone?.contact?.id,
    );
    return this.contactService.viewPhone(phone, userId, lead?.id);
  }
}
