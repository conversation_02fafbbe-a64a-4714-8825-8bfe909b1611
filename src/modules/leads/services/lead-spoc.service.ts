import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { LeadSpoc } from '../entities/lead-spoc.entity';

@Injectable()
export class LeadSpocService {
  constructor(
    @InjectRepository(LeadSpoc)
    private readonly leadSpocRepository: Repository<LeadSpoc>,
  ) {}

  async create(leadSpocData: Partial<LeadSpoc>): Promise<LeadSpoc> {
    const leadSpoc = this.leadSpocRepository.create(leadSpocData);
    return this.leadSpocRepository.save(leadSpoc);
  }

  async findAll(): Promise<LeadSpoc[]> {
    return this.leadSpocRepository.find();
  }

  async findByLeadId(leadId: number): Promise<LeadSpoc[]> {
    return this.leadSpocRepository.find({
      where: { lead_id: leadId },
    });
  }

  async findByUserIds(userIds: number[]): Promise<LeadSpoc[]> {
    return this.leadSpocRepository.find({
      where: { spoc_id: In(userIds) },
    });
  }

  async findOne(id: number): Promise<LeadSpoc> {
    return this.leadSpocRepository.findOne({
      where: { id },
    });
  }

  async update(
    id: number,
    updateLeadSpocData: Partial<LeadSpoc>,
  ): Promise<LeadSpoc> {
    await this.leadSpocRepository.update(id, updateLeadSpocData);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.leadSpocRepository.delete(id);
  }

  async removeByLeadId(leadId: number): Promise<void> {
    await this.leadSpocRepository.delete({ lead_id: leadId });
  }
}
