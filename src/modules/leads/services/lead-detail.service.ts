import { Injectable, Logger } from '@nestjs/common';
import { Lead } from '../entities/lead.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
import { EducationBoards } from '@modules/contacts/enums/education-board.enum';
import { CallLog } from '@modules/call-logs/entities/call-log.entity';
import { CallStatus } from '@modules/call-logs/enums/call-log.enum';

@Injectable()
export class LeadDetailService {
  private readonly logger = new Logger(LeadDetailService.name);

  constructor(
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(CallLog)
    private readonly callLogRepository: Repository<CallLog>,
  ) {}

  async findLeadDetailByUUID(leadUUID: UUID) {
    try {
      const lead = await this.leadRepository.findOne({
        where: { lead_uuid: leadUUID },
        relations: {
          contact: {
            emails: true,
            phones: true,
            educations: true,
            workExperiences: true,
            miles_office: true,
          },
          program_interests: {
            lead_level: true,
            net_enquiries: {
              lead_source: true,
              campaign: true,
            },
            webinar_registrations: true,
            program: true,
          },
          spocs: true,
          owner_spoc: {
            spoc: {
              manager: true,
            },
          },
        },
      });

      if (!lead || !lead.contact) {
        throw new Error(`Lead not found for UUID: ${leadUUID}`);
      }
      // Find the most recent call log for this lead
      const lastCallLog = await this.callLogRepository.findOne({
        where: { lead: { id: lead.id }, call_status: CallStatus.ATTENDED },
        order: { created_at: 'DESC' },
      });

      // Prepare masked contact information
      const maskedContactInfo = {
        emails: [],
        phones: [],
      };

      // Mask and include only email information
      if (lead.contact.emails && lead.contact.emails.length > 0) {
        maskedContactInfo.emails = lead.contact.emails.map((email) => ({
          id: email.id,
          email: email.masked_email,
          label: email.label,
        }));
      }

      // Mask and include only phone information
      if (lead.contact.phones && lead.contact.phones.length > 0) {
        maskedContactInfo.phones = lead.contact.phones.map((phone) => ({
          id: phone.id,
          phone_number: phone.masked_phone_number,
          country_code: phone.country_code,
          label: phone.label,
        }));
      }

      // Collect all net enquiries from program interests
      const allNetEnquiries = [];
      if (lead.program_interests && lead.program_interests.length > 0) {
        lead.program_interests.forEach((programInterest) => {
          if (
            programInterest.net_enquiries &&
            programInterest.net_enquiries.length > 0
          ) {
            const filteredNetEnquiries = programInterest.net_enquiries.map(
              (netEnquiry) => ({
                lead_source: netEnquiry.lead_source?.name,
                enquired_on: netEnquiry.created_at,
                campaign_name: netEnquiry.campaign?.campaign_name,
                source_name: netEnquiry.lead_source?.name,
                program_id: programInterest.program.id,
                program_name: programInterest.program.name,
              }),
            );
            allNetEnquiries.push(...filteredNetEnquiries);
          }
        });
      }

      return {
        lead: {
          ...lead,
          contact: {
            ...lead.contact,
            emails: maskedContactInfo.emails,
            phones: maskedContactInfo.phones,
          },
        },
        last_connected_call_date: lastCallLog?.created_at || null,
        net_enquiries: allNetEnquiries,
        otherDetails: {
          educationBoards: Object.values(EducationBoards),
        },
      };
    } catch (error) {
      this.logger.error(
        `Error finding lead detail by UUID ${leadUUID}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lead not found for UUID: ${leadUUID}`);
    }
  }
}
