import { Injectable, Logger } from '@nestjs/common';
import { Lead } from '@modules/leads/entities/lead.entity';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { User } from '@modules/users/entities/user.entity';
import { LeadService } from '@modules/leads/services/lead.service';
import { UserService } from '@modules/users/services/user.service';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { CallLog } from '@modules/call-logs/entities/call-log.entity';

export interface LeadAssignmentResult {
  callLog: CallLog;
  assignmentStatus: 'assigned' | 'multi_vertical' | 'untracked';
  reason?: string;
}

export interface LeadAssignmentMetadata {
  reason: string;
  message: string;
  user_business_units: number[];
  available_leads?: Array<{ id: number; client_id: number }>;
  matching_leads?: Array<{ id: number; client_id: number }>;
  all_available_leads?: Array<{ id: number; client_id: number }>;
}

@Injectable()
export class LeadAssignmentService {
  private readonly logger = new Logger(LeadAssignmentService.name);

  constructor(
    private readonly leadService: LeadService,
    private readonly userService: UserService,
    private readonly callLogService: CallLogService,
  ) {}

  /**
   * Main method to assign leads to call logs with intelligent business logic
   */
  async assignLeadToCallLog(
    callLog: CallLog,
    phone: Phone,
    user: User,
  ): Promise<LeadAssignmentResult> {
    this.logger.log(`Starting lead assignment for call log ID: ${callLog.id}`);

    // Get contact's leads and user's business units
    const [contactLeads, userClientIds] = await Promise.all([
      this.leadService.findByContactId(phone.contact.id),
      this.userService.getUserClientIds(user.id),
    ]);

    this.logger.debug(
      `Found ${contactLeads.length} leads for contact, user has ${userClientIds.length} business units`,
    );

    // Process lead assignment based on business logic
    return await this.processLeadAssignment(
      callLog,
      contactLeads,
      userClientIds,
    );
  }

  /**
   * Process lead assignment based on the number of leads and business unit rules
   */
  private async processLeadAssignment(
    callLog: CallLog,
    contactLeads: Lead[],
    userClientIds: number[],
  ): Promise<LeadAssignmentResult> {
    if (contactLeads.length === 0) {
      return await this.handleNoLeads(callLog);
    }

    if (contactLeads.length === 1) {
      return await this.handleSingleLead(
        callLog,
        contactLeads[0],
        userClientIds,
      );
    }

    return await this.handleMultipleLeads(callLog, contactLeads, userClientIds);
  }

  /**
   * Handle case when no leads exist for the contact
   */
  private async handleNoLeads(callLog: CallLog): Promise<LeadAssignmentResult> {
    this.logger.log('No leads found for contact - creating untracked call');

    const updatedCallLog = await this.callLogService.updateCallLog(callLog);

    return {
      callLog: updatedCallLog,
      assignmentStatus: 'untracked',
      reason: 'NO_LEADS_FOUND',
    };
  }

  /**
   * Handle case when exactly one lead exists for the contact
   */
  private async handleSingleLead(
    callLog: CallLog,
    lead: Lead,
    userClientIds: number[],
  ): Promise<LeadAssignmentResult> {
    this.logger.log(`Processing single lead ID: ${lead.id} for assignment`);

    if (userClientIds.includes(lead.client_id)) {
      // Lead belongs to user's business unit - assign it
      this.logger.log(
        `Assigning lead ID: ${lead.id} to call log (same business unit)`,
      );

      callLog.lead = lead;
      const updatedCallLog = await this.callLogService.updateCallLog(callLog);

      return {
        callLog: updatedCallLog,
        assignmentStatus: 'assigned',
        reason: 'SINGLE_LEAD_SAME_BUSINESS_UNIT',
      };
    } else {
      // Lead belongs to different business unit - mark as multi-vertical
      this.logger.log(
        `Single lead ID: ${lead.id} is from different business unit - marking as multi-vertical`,
      );

      callLog.is_multi_vertical_phone = true;
      callLog.metadata = this.createSingleLeadCrossBusinessUnitMetadata(
        lead,
        userClientIds,
      );

      const updatedCallLog = await this.callLogService.updateCallLog(callLog);

      return {
        callLog: updatedCallLog,
        assignmentStatus: 'multi_vertical',
        reason: 'SINGLE_LEAD_CROSS_BUSINESS_UNIT',
      };
    }
  }

  /**
   * Handle case when multiple leads exist for the contact
   */
  private async handleMultipleLeads(
    callLog: CallLog,
    leads: Lead[],
    userClientIds: number[],
  ): Promise<LeadAssignmentResult> {
    this.logger.log(`Processing ${leads.length} leads for assignment`);

    // Filter leads that belong to user's business units
    const userLeads = leads.filter((lead) =>
      userClientIds.includes(lead.client_id),
    );

    if (userLeads.length === 0) {
      return await this.handleMultipleLeadsNoMatch(
        callLog,
        leads,
        userClientIds,
      );
    }

    // Case 3.1: User has single business unit
    if (userClientIds.length === 1) {
      this.logger.log(
        'User has single business unit - checking for leads in that unit',
      );

      if (userLeads.length === 1) {
        // Single lead in user's single business unit - assign it
        this.logger.log(
          `Assigning lead ID: ${userLeads[0].id} from user's single business unit to call log`,
        );

        callLog.lead = userLeads[0];
        const updatedCallLog = await this.callLogService.updateCallLog(callLog);

        return {
          callLog: updatedCallLog,
          assignmentStatus: 'assigned',
          reason: 'SINGLE_BUSINESS_UNIT_SINGLE_LEAD',
        };
      } else if (userLeads.length > 1) {
        // Multiple leads in user's single business unit - mark as multi-vertical
        this.logger.log(
          `Multiple leads found in user's single business unit - marking as multi-vertical`,
        );

        callLog.is_multi_vertical_phone = true;
        callLog.metadata = this.createSingleBusinessUnitMultipleLeadsMetadata(
          userLeads,
          leads,
          userClientIds[0],
        );

        const updatedCallLog = await this.callLogService.updateCallLog(callLog);

        return {
          callLog: updatedCallLog,
          assignmentStatus: 'multi_vertical',
          reason: 'SINGLE_BUSINESS_UNIT_MULTIPLE_LEADS',
        };
      }
    }

    // Case 3.2: User has multiple business units
    this.logger.log(
      'User has multiple business units - marking as multi-vertical',
    );

    callLog.is_multi_vertical_phone = true;
    callLog.metadata = this.createMultipleBusinessUnitsMetadata(
      userLeads,
      leads,
      userClientIds,
    );

    const updatedCallLog = await this.callLogService.updateCallLog(callLog);

    return {
      callLog: updatedCallLog,
      assignmentStatus: 'multi_vertical',
      reason: 'USER_HAS_MULTIPLE_BUSINESS_UNITS',
    };
  }

  /**
   * Handle multiple leads with no matches in user's business units
   */
  private async handleMultipleLeadsNoMatch(
    callLog: CallLog,
    allLeads: Lead[],
    userClientIds: number[],
  ): Promise<LeadAssignmentResult> {
    this.logger.log(
      "Multiple leads found but none from user's business units - marking as multi-vertical",
    );

    callLog.is_multi_vertical_phone = true;
    callLog.metadata = this.createNoMatchingLeadsMetadata(
      allLeads,
      userClientIds,
    );

    const updatedCallLog = await this.callLogService.updateCallLog(callLog);

    return {
      callLog: updatedCallLog,
      assignmentStatus: 'multi_vertical',
      reason: 'MULTIPLE_LEADS_NO_BUSINESS_UNIT_MATCH',
    };
  }

  /**
   * Create metadata for single lead cross business unit scenario
   */
  private createSingleLeadCrossBusinessUnitMetadata(
    lead: Lead,
    userClientIds: number[],
  ): LeadAssignmentMetadata {
    return {
      reason: 'CROSS_BUSINESS_UNIT_LEAD',
      message: 'Lead belongs to a different business unit than the user',
      user_business_units: userClientIds,
      available_leads: [{ id: lead.id, client_id: lead.client_id }],
    };
  }

  /**
   * Create metadata for no matching leads scenario
   */
  private createNoMatchingLeadsMetadata(
    allLeads: Lead[],
    userClientIds: number[],
  ): LeadAssignmentMetadata {
    return {
      reason: 'NO_LEADS_IN_USER_BUSINESS_UNITS',
      message: "Contact has leads but none belong to user's business units",
      user_business_units: userClientIds,
      available_leads: allLeads.map((lead) => ({
        id: lead.id,
        client_id: lead.client_id,
      })),
    };
  }

  /**
   * Create metadata for single business unit with multiple leads scenario
   */
  private createSingleBusinessUnitMultipleLeadsMetadata(
    matchingLeads: Lead[],
    allLeads: Lead[],
    userBusinessUnit: number,
  ): LeadAssignmentMetadata {
    return {
      reason: 'SINGLE_BUSINESS_UNIT_MULTIPLE_LEADS',
      message:
        'User has single business unit but contact has multiple leads in that unit - manual assignment required',
      user_business_units: [userBusinessUnit],
      matching_leads: matchingLeads.map((lead) => ({
        id: lead.id,
        client_id: lead.client_id,
      })),
      all_available_leads: allLeads.map((lead) => ({
        id: lead.id,
        client_id: lead.client_id,
      })),
    };
  }

  /**
   * Create metadata for user with multiple business units scenario
   */
  private createMultipleBusinessUnitsMetadata(
    matchingLeads: Lead[],
    allLeads: Lead[],
    userClientIds: number[],
  ): LeadAssignmentMetadata {
    return {
      reason: 'USER_HAS_MULTIPLE_BUSINESS_UNITS',
      message:
        'User has multiple business units and contact has multiple leads - manual assignment required',
      user_business_units: userClientIds,
      matching_leads: matchingLeads.map((lead) => ({
        id: lead.id,
        client_id: lead.client_id,
      })),
      all_available_leads: allLeads.map((lead) => ({
        id: lead.id,
        client_id: lead.client_id,
      })),
    };
  }
}
