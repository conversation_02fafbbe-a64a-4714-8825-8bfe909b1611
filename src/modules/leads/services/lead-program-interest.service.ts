import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { CreateLeadProgramInterestDto } from '../dto/create-lead-program-interest.dto';
import { UpdateLeadProgramInterestDto } from '../dto/update-lead-program-interest.dto';
import { LeadLevelService } from './lead-level.service';
import { AllowedTransitionMethodEnum } from '../enums/lead-level.enum';

@Injectable()
export class LeadProgramInterestService {
  constructor(
    @InjectRepository(LeadProgramInterest)
    private readonly leadProgramInterestRepository: Repository<LeadProgramInterest>,
    private readonly leadLevelService: LeadLevelService,
  ) {}

  async create(
    createLeadProgramInterestDto: CreateLeadProgramInterestDto,
    leadId: number,
    userId?: number,
  ): Promise<LeadProgramInterest> {
    const leadProgramInterest = this.leadProgramInterestRepository.create({
      ...createLeadProgramInterestDto,
      lead_id: leadId,
      created_by: userId,
    });
    console.log('Creating LeadProgramInterest:', leadProgramInterest);
    return this.leadProgramInterestRepository.save(leadProgramInterest);
  }

  async createMany(
    createLeadProgramInterestDtos: CreateLeadProgramInterestDto[],
    leadId: number,
    userId?: number,
  ): Promise<LeadProgramInterest[]> {
    const leadProgramInterests = createLeadProgramInterestDtos.map((dto) =>
      this.leadProgramInterestRepository.create({
        ...dto,
        lead_id: leadId,
        created_by: userId,
      }),
    );
    return this.leadProgramInterestRepository.save(leadProgramInterests);
  }

  async findAllByLeadId(leadId: number): Promise<LeadProgramInterest[]> {
    return this.leadProgramInterestRepository.find({
      where: { lead_id: leadId },
      relations: ['program', 'lead_level'],
    });
  }

  async findByLeadIdAndProgramId(
    leadId: number,
    programId: number,
  ): Promise<LeadProgramInterest | null> {
    return this.leadProgramInterestRepository.findOne({
      where: { lead_id: leadId, program_id: programId },
      relations: { program: true, lead: { contact: { primary_email: true } } },
    });
  }

  async findOne(id: number): Promise<LeadProgramInterest> {
    const leadProgramInterest =
      await this.leadProgramInterestRepository.findOne({
        where: { id },
        relations: [
          'program',
          'lead',
          'lead.contact',
          'lead.contact.primary_email',
        ],
      });

    if (!leadProgramInterest) {
      throw new NotFoundException(
        `LeadProgramInterest with ID ${id} not found`,
      );
    }

    return leadProgramInterest;
  }

  async update(
    id: number,
    updateLeadProgramInterestDto: UpdateLeadProgramInterestDto,
    userId?: number,
  ): Promise<LeadProgramInterest> {
    const leadProgramInterest = await this.findOne(id);

    const updatedLeadProgramInterest = {
      ...leadProgramInterest,
      ...updateLeadProgramInterestDto,
      updated_by: userId,
    };

    return this.leadProgramInterestRepository.save(updatedLeadProgramInterest);
  }

  async remove(id: number): Promise<void> {
    const result = await this.leadProgramInterestRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(
        `LeadProgramInterest with ID ${id} not found`,
      );
    }
  }

  async removeAllByLeadId(leadId: number): Promise<void> {
    await this.leadProgramInterestRepository.delete({ lead_id: leadId });
  }

  async setPrimaryProgram(
    leadId: number,
    programId: number,
    userId?: number,
  ): Promise<void> {
    // First, set all programs as non-primary
    await this.leadProgramInterestRepository.update(
      { lead_id: leadId },
      { is_primary: false, updated_by: userId },
    );

    // Then, set the specified program as primary
    const interest = await this.leadProgramInterestRepository.findOne({
      where: { lead_id: leadId, program_id: programId },
    });

    if (!interest) {
      throw new NotFoundException(
        `Program interest with Lead ID ${leadId} and Program ID ${programId} not found`,
      );
    }

    await this.leadProgramInterestRepository.update(interest.id, {
      is_primary: true,
      updated_by: userId,
    });
  }

  async updateLeadLevelsForLead({
    leadId,
    levelsAndPrograms,
  }: {
    leadId: number;
    levelsAndPrograms: { targetLevel: number; programId: number }[];
  }) {
    try {
      const leadProgramInterests = await this.findAllByLeadId(leadId);
      if (!leadProgramInterests || leadProgramInterests.length === 0) {
        throw new NotFoundException(
          `No program interests found for lead ID ${leadId}`,
        );
      }

      const failedResults = [];
      for (const item of levelsAndPrograms) {
        const { programId, targetLevel } = item;
        const interest = leadProgramInterests.find(
          (interest) => interest.program_id === programId,
        );
        if (!interest) {
          failedResults.push({
            programId,
            error: `Program interest not found for program ID ${programId}`,
          });
          continue;
        }
        try {
          await this.leadLevelService.updateLeadProgIntLevel({
            programInterestId: interest.id,
            currentLevelId: interest.lead_level_id,
            newLevelId: targetLevel,
            nextFollowUpDate: null,
            userId: interest.updated_by,
          });
        } catch (error) {
          failedResults.push({
            programId,
            error: error.message,
          });
        }
        //level history
      }
      return {
        failedUpdates: failedResults,
      };
    } catch (error) {
      throw new NotFoundException(
        `Error updating lead levels for lead ID ${leadId}: ${error.message}`,
      );
    }
  }

  /**
   * Create a new program interest during disposition workflow
   * @param createData - Data for creating the program interest
   * @param userId - ID of the user creating the program interest
   * @param dispositionId - ID of the disposition this is being created for
   * @returns Created LeadProgramInterest
   */
  async createProgramInterestDuringDisposition(
    createData: {
      lead_id: number;
      client_id: number;
      program_id: number;
      initial_level_id: number;
      lead_source_id?: number;
      comments?: string;
      metadata?: Record<string, any>;
      source?: number;
    },
    userId?: number,
    dispositionId?: number,
  ): Promise<LeadProgramInterest> {
    // Check if this program interest already exists
    const existingInterest = await this.findByLeadIdAndProgramId(
      createData.lead_id,
      createData.program_id,
    );

    if (existingInterest) {
      throw new BadRequestException(
        `Program interest for program ${createData.program_id} already exists for this lead`,
      );
    }

    // Create the program interest
    const programInterest = this.leadProgramInterestRepository.create({
      lead_id: createData.lead_id,
      client_id: createData.client_id,
      program_id: createData.program_id,
      lead_level_id: createData.initial_level_id,
      lead_source_id: createData.lead_source_id,
      notes: createData.comments,
      metadata: {
        ...createData.metadata,
        created_via: 'disposition',
        created_during_disposition_id: dispositionId,
      },
      created_by: userId,
    });

    const savedProgramInterest =
      await this.leadProgramInterestRepository.save(programInterest);

    // Create initial level history for the new program interest
    await this.leadLevelService.applyLevelChangesAndCreateHistory({
      validatedUpdates: [
        {
          programInterest: savedProgramInterest,
          newLevelId: createData.initial_level_id,
          comments:
            createData.comments || 'Initial level assignment for new program',
        },
      ],
      dispositionHistoryId: dispositionId,
      spocUserId: userId,
      transitionMethod: AllowedTransitionMethodEnum.System, // Since this is an initial assignment
    });

    //create lead level chanhe history
    await this.leadLevelService.createLevelChangeHistory({
      programInterestId: savedProgramInterest.id,
      fromLevelId: null,
      toLevelId: createData.initial_level_id,
      comments: createData.comments || 'Initial level assignment',
      userId: userId,
      transitionMethod: AllowedTransitionMethodEnum.System,
    });

    return savedProgramInterest;
  }
}
