import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Lead } from '../entities/lead.entity';
import { CreateLeadDto } from '../dto/create-lead.dto';
import { UpdateLeadDto } from '../dto/update-lead.dto';
import { LeadProgramInterestService } from './lead-program-interest.service';
import { Contact } from 'src/modules/contacts/entities/contact.entity';

@Injectable()
/* //!Deprecated - Use the LeadService instead.

  Logic for new lead according to this LOGIC : 
    Either email or phone is new, create a new lead and link the old one as parent
  
*/
export class DepLeadService {
  constructor(
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    private readonly leadProgramInterestService: LeadProgramInterestService,
  ) {}

  // async create(
  //   createLeadDto: CreateLeadDto,
  //   userId?: number,
  // ): Promise<{ isNew: boolean; lead: Lead }> {
  //   return this.createLeadWithRelationships(createLeadDto, userId);
  // }

  async createLeadWithRelationships(
    createLeadDto: {
      createLeadDto: CreateLeadDto;
      contact_info: Contact;
      client_id: number;
    },
    userId?: number,
  ): Promise<{ lead: Lead; isNew: boolean }> {
    const { contact_info, ...leadData } = createLeadDto;
    const contactId = contact_info.id;
    const clientId = createLeadDto.client_id;

    // First check if this contact already has a lead in this client
    const existingLead = await this.leadRepository.findOne({
      where: {
        contact_id: contactId,
        client_id: clientId,
      },
    });

    if (existingLead) {
      // Contact already has a lead in this client, return it
      return { isNew: false, lead: existingLead };
    }

    // Create a new lead for this contact in this client
    const lead = this.leadRepository.create({
      ...leadData,
      contact: contact_info,
      contact_id: contactId,
      created_by: userId,
    });

    const savedLead = await this.leadRepository.save(lead);

    // Now handle parent-child relationships if needed
    await this.handleContactRelationships(
      contact_info,
      savedLead,
      clientId,
      userId,
    );

    return {
      isNew: true,
      lead: savedLead,
    };
  }

  /**
   * Handles establishing parent-child relationships between leads based on their contacts
   */
  private async handleContactRelationships(
    contact,
    lead,
    clientId: number,
    userId?: number,
  ): Promise<void> {
    // If contact has a parent, check if there's a lead for it in this client
    if (contact.parentContactId) {
      // Check if parent contact has a lead in this client
      const parentLead = await this.leadRepository.findOne({
        where: {
          contact_id: contact.parentContactId,
          client_id: clientId,
        },
      });

      if (parentLead) {
        // Set this lead's parent to the found lead
        await this.leadRepository.update(lead.id, {
          parent_lead_id: parentLead.id,
          updated_by: userId,
        });
      }
    }

    // If contact has children, check if any have leads in this client
    if (contact.childContacts && contact.childContacts.length > 0) {
      const childContactIds = contact.childContacts.map((child) => child.id);

      // Find leads for child contacts in this client
      const childLeads = await this.leadRepository.find({
        where: {
          contact_id: In(childContactIds),
          client_id: clientId,
        },
      });

      // Set this lead as parent for each child lead
      if (childLeads && childLeads.length > 0) {
        for (const childLead of childLeads) {
          await this.leadRepository.update(childLead.id, {
            parent_lead_id: lead.id,
            updated_by: userId,
          });
        }
      }
    }
  }

  async findAll(): Promise<Lead[]> {
    return this.leadRepository.find({
      relations: ['contact', 'parentLead', 'childLeads'],
    });
  }

  async findOne(id: number): Promise<Lead> {
    const lead = await this.leadRepository.findOne({
      where: { id },
      relations: [
        'contact',
        'parentLead',
        'childLeads',
        'program_interests',
        'program_interests.program',
      ],
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }

    return lead;
  }

  async findByClientAndContact(
    clientId: number,
    contactId: number,
  ): Promise<Lead> {
    const lead = await this.leadRepository.findOne({
      where: {
        client_id: clientId,
        contact_id: contactId,
      },
      relations: ['contact', 'parentLead', 'childLeads'],
    });

    return lead; // May be null if not found
  }

  async update(
    id: number,
    updateLeadDto: UpdateLeadDto,
    userId?: number,
  ): Promise<Lead> {
    const { ...leadData } = updateLeadDto;

    const lead = await this.findOne(id);

    // Update the lead
    const updatedLead = {
      ...lead,
      ...leadData,
      updated_by: userId,
    };

    await this.leadRepository.save(updatedLead);

    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const result = await this.leadRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }
  }

  async markAsConverted(id: number, userId?: number): Promise<Lead> {
    const lead = await this.findOne(id);

    lead.updated_by = userId;

    return this.leadRepository.save(lead);
  }

  async setPrimaryProgram(
    leadId: number,
    programId: number,
    userId?: number,
  ): Promise<void> {
    await this.leadProgramInterestService.setPrimaryProgram(
      leadId,
      programId,
      userId,
    );
  }
}
