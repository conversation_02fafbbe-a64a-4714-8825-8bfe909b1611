import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity()
export class EmailTemplate extends ClientAwareEntity {
  @Column({ type: 'varchar', nullable: true })
  template_name: string;

  @Column({ type: 'text', nullable: true })
  text: string;

  @Column({ type: 'varchar', nullable: true })
  subject: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by', referencedColumnName: 'id' })
  createdBy: User;
}
