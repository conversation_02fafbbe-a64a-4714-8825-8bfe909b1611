import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON>in<PERSON><PERSON><PERSON>n, Index, Unique } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';
import { Role } from '../../roles/enities/role.entity';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';

@Entity()
@Unique(['user_client_id', 'role_id'])
export class UserClientRole extends BaseEntity {
  @Column()
  @Index()
  user_client_id: number;

  @Column()
  @Index()
  role_id: number;

  @ManyToOne(() => UserClient, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_client_id' })
  userClient: UserClient;

  @ManyToOne(() => Role, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;
}
