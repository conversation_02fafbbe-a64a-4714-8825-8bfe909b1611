import { Controller, Post, Body, UseGuards, Req } from '@nestjs/common';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { Resource, Action } from '../permissions/enums/permission.enum';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';

import { AssignUserRoleDto } from './dto/assign-user-role.dto';
import { UserClientRoleService } from './services/user-client-role.service';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';

import { ApiTags, ApiOperation, ApiBody } from '@nestjs/swagger';

@ApiTags('user-client-roles')
@Controller('user-client-roles')
@ApiResource(Resource.ROLE)
@UseGuards(PermissionGuard)
export class UserClientRoleController {
  constructor(private readonly userClientRoleService: UserClientRoleService) {}

  @Post('assign')
  @RequirePermission({ resource: Resource.ROLE, action: Action.CREATE })
  @ApiOperation({ summary: 'Assign a role to a user for a client' })
  @ApiBody({ type: AssignUserRoleDto })
  async assignRoles(
    @Body() dto: AssignUserRoleDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    const clientId = req.user?.currentClientId;
    return this.userClientRoleService.assignRole(
      dto.user_client_id,
      dto.role_id,
      userId,
      clientId,
    );
  }
}
