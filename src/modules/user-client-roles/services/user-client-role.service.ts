import { Injectable, Logger, NotFoundException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';
import { Role } from '@modules/roles/enities/role.entity';
import { AssignUserRoleResponse } from '../interfaces/assign-user-role.interface.dto';

@Injectable()
export class UserClientRoleService {
  private readonly logger = new Logger(UserClientRoleService.name);

  constructor(
    @InjectRepository(UserClientRole)
    private userClientRoleRepository: Repository<UserClientRole>,
    @InjectRepository(UserClient)
    private userClientRepository: Repository<UserClient>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async assignRole(
    userClientId: number,
    roleId: number,
    userId: number,
    clientId: number,
  ): Promise<AssignUserRoleResponse> {
    try {
      // Verify user-client exists
      const userClient = await this.userClientRepository.findOne({
        where: { id: userClientId, client_id: clientId },
      });

      if (!userClient) {
        throw new NotFoundException('User-client relationship not found');
      }

      // Verify role exists and belongs to client
      const role = await this.roleRepository.findOne({
        where: { id: roleId, client_id: clientId },
      });

      if (!role) {
        throw new NotFoundException(
          'Role not found or does not belong to this client',
        );
      }

      // Check existing assignment
      const existingAssignment = await this.userClientRoleRepository.findOne({
        where: {
          user_client_id: userClientId,
        },
      });

      if (existingAssignment?.role_id === roleId) {
        return {
          success: true,
          message: 'Role is already assigned to this user',
          data: {
            user_client_id: userClientId,
            role_id: roleId,
          },
        };
      }

      // Perform assignment in transaction
      await this.userClientRoleRepository.manager.transaction(
        async (manager) => {
          // Remove existing assignment if any
          if (existingAssignment) {
            await manager.delete(UserClientRole, {
              user_client_id: userClientId,
            });
          }

          // Create new assignment
          const assignment = {
            user_client_id: userClientId,
            role_id: roleId,
            created_by: userId,
          };

          await manager.insert(UserClientRole, assignment);
        },
      );

      await this.invalidateUserRoleCache(userClientId);

      return {
        success: true,
        message: 'Role assigned successfully',
        data: {
          user_client_id: userClientId,
          role_id: roleId,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to assign role: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async invalidateUserRoleCache(userClientId: number): Promise<void> {
    try {
      console.log(userClientId);
      const pattern = `permission:*`;
      const keys = await this.redis.keys(pattern);

      if (keys?.length) {
        const pipeline = this.redis.pipeline();
        keys.forEach((key) => pipeline.del(key));
        await pipeline.exec();
        this.logger.log(`Invalidated ${keys.length} permission cache keys`);
      }
    } catch (error) {
      this.logger.error(
        `Cache invalidation failed: ${error.message}`,
        error.stack,
      );
    }
  }
}
