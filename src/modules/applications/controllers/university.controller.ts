import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { UniversityService } from '../services/university.service';
import { CreateUniversityDto } from '../dto/create-university.dto';
import { UpdateUniversityDto } from '../dto/update-university.dto';
import { University } from '../entities/university.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';

@ApiTags('universities')
@ApiBearerAuth()
@Controller('universities')
export class UniversityController {
  constructor(private readonly universityService: UniversityService) {}

  @Post('add')
  @ApiOperation({ summary: 'Create a new university' })
  @ApiResponse({
    status: 201,
    description: 'University created successfully',
    type: University,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  create(
    @Body() createUniversityDto: CreateUniversityDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;
    const userId = req.user?.id;

    return this.universityService.create(createUniversityDto, clientId, userId);
  }

  @Get('get-all')
  @ApiOperation({
    summary: 'Get all universities with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Universities retrieved successfully',
  })
  findAll(
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<University>> {
    const clientId = req.user?.currentClientId;
    return this.universityService.findAll(clientId);
  }

  @Get('get-one/:id')
  @ApiOperation({ summary: 'Get university by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'University ID' })
  @ApiResponse({
    status: 200,
    description: 'University retrieved successfully',
    type: University,
  })
  @ApiResponse({ status: 404, description: 'University not found' })
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;

    return this.universityService.findOne(id, clientId);
  }

  @Post('update')
  @ApiOperation({ summary: 'Update university by ID' })
  @ApiResponse({
    status: 200,
    description: 'University updated successfully',
    type: University,
  })
  @ApiResponse({ status: 404, description: 'University not found' })
  update(
    @Body() updateUniversityDto: UpdateUniversityDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;
    const userId = req.user?.id;

    return this.universityService.update(updateUniversityDto, clientId, userId);
  }

  @Get('remove/:id')
  @ApiOperation({ summary: 'Delete university by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'University ID' })
  @ApiResponse({ status: 200, description: 'University deleted successfully' })
  @ApiResponse({ status: 404, description: 'University not found' })
  remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;
    const userId = req.user?.id;
    return this.universityService.remove(id, userId, clientId);
  }
}
