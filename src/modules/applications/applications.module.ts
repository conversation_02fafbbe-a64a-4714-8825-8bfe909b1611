import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { University } from './entities/university.entity';
import { DocType } from './entities/doc-type.entity';
import { UploadedFiles } from './entities/uploaded-files.entity';

// Services
import { UniversityService } from './services/university.service';
import { DocTypeService } from './services/doc-type.service';

// Controllers
import { UniversityController } from './controllers/university.controller';
import { DocTypeController } from './controllers/doc-type.controller';
import { AttachmentsModule } from '@modules/attachments/attachments.module';
import { UniversityRequiredDocs } from './entities/university-required-docs.entity';
import { LeadModule } from '@modules/leads/lead.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      University,
      DocType,
      UploadedFiles,
      AttachmentsModule,
      UniversityRequiredDocs,
    ]),
    LeadModule,
  ],
  controllers: [UniversityController, DocTypeController],
  providers: [UniversityService, DocTypeService],
  exports: [UniversityService, DocTypeService],
})
export class ApplicationsModule {}
