import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, JoinTable } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity('universities')
export class University extends ClientAwareEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  university_name: string;

  @Column({ type: 'json', nullable: true })
  interviewers: Record<string, any>;

  @Column({ type: 'boolean', nullable: true })
  schedule_meet: boolean;
}
