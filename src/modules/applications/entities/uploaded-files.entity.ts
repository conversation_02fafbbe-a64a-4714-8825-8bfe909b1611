import { <PERSON><PERSON><PERSON>, Col<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';

// Type imports to avoid circular dependency
import type { DocType } from './doc-type.entity';
import type { Contact } from 'src/modules/contacts/entities/contact.entity';
import type { Attachment } from 'src/modules/attachments/entities/attachment.entity';
import { Lead } from '@modules/leads/entities/lead.entity';

@Entity('uploaded_files')
export class UploadedFiles extends BaseEntity {
  @Index()
  @ManyToOne('DocType', { nullable: false })
  @JoinColumn({ name: 'doc_type_id' })
  doc_type: DocType;

  @Column({ type: 'integer', nullable: false })
  doc_type_id: number;

  @Index()
  @ManyToOne('Contact', { nullable: false })
  @JoinColumn({ name: 'contact_id' })
  contact: Contact;

  @Column({ type: 'integer', nullable: false })
  contact_id: number;

  @Index()
  @ManyToOne('Attachment', { nullable: false })
  @JoinColumn({ name: 'attachment_id' })
  attachment: Attachment;

  @Column({ type: 'integer', nullable: false })
  attachment_id: number;
}
