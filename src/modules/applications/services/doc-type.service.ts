import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { DocType } from '../entities/doc-type.entity';
import { CreateDocTypeDto } from '../dto/create-doc-type.dto';
import { UpdateDocTypeDto } from '../dto/update-doc-type.dto';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { CreateUploadedFilesDto } from '../dto/create-uploaded-files.dto';
import { UploadedFiles } from '../entities/uploaded-files.entity';
import { University } from '../entities/university.entity';
import { AttachmentsService } from '@modules/attachments/attachments.service';
import { FilePrivacy } from '@modules/attachments/enums/file-type.enum';
import { UniversityRequiredDocs } from '../entities/university-required-docs.entity';

@Injectable()
export class DocTypeService {
  constructor(
    @InjectRepository(DocType)
    private readonly docTypeRepository: Repository<DocType>,
    @InjectRepository(UniversityRequiredDocs)
    private readonly uniReqDocsRepository: Repository<UniversityRequiredDocs>,
    @InjectRepository(University)
    private readonly universityRepository: Repository<University>,
    @InjectRepository(UploadedFiles)
    private readonly uploadedFielRepository: Repository<UploadedFiles>,
    private readonly attachmentsService: AttachmentsService,
  ) {}

  async create(
    createDocTypeDto: CreateDocTypeDto,
    userId: number,
  ): Promise<DocType> {
    const docType = this.docTypeRepository.create({
      created_by: userId,
      document_title: createDocTypeDto.document_title,
    });
    return await this.docTypeRepository.save(docType);
  }

  async findAll(): Promise<PaginatedResponse<any>> {
    const docTypes = await this.uniReqDocsRepository.find({
      where: {
        status: true,
        is_active: true,
      },
      relations: {
        doc_type: true,
        university: true,
      },
    });
    console.log(docTypes);
    const data = [];
    for (const docType of docTypes) {
      if (!data.some((d) => d.document_id === docType.doc_type.id)) {
        data.push({
          document_id: docType.doc_type.id,
          document_title: docType.doc_type.document_title,
          universities: [
            {
              id: docType.university.id,
              name: docType.university.university_name,
            },
          ],
        });
      } else {
        const index = data.findIndex(
          (d) => d.document_id === docType.doc_type.id,
        );
        data[index].universities.push({
          id: docType.university.id,
          name: docType.university.university_name,
        });
      }
    }
    return {
      data,
      meta: null,
    };
  }

  async finLeadDocs(leadId: number): Promise<PaginatedResponse<any>> {
    const docs = await this.uploadedFielRepository.find({
      where: { lead_id: leadId },
      relations: {
        doc_type: true,
        attachment: true,
      },
    });

    const data = docs.map((doc) => ({
      uploaded_doc_id: doc.id,
      doc_type_id: doc.doc_type_id,
      doc_type_title: doc.doc_type.document_title,
      attachment_id: doc.attachment_id,
      attachment_name: doc.attachment.file_name,
    }));

    return {
      data,
      meta: null,
    };
  }

  async createUploadDocument(
    file: Express.Multer.File,
    createUploadedFilesDto: CreateUploadedFilesDto,
    clientId: number,
    userId: number,
  ) {
    try {
      let response;
      const attachment = await this.attachmentsService.uploadFile(
        file,
        null,
        FilePrivacy.PRIVATE,
      );
      const existingDocument = await this.uploadedFielRepository.findOne({
        where: {
          doc_type_id: createUploadedFilesDto.doc_type_id,
          lead_id: createUploadedFilesDto.lead_id,
          contact_id: createUploadedFilesDto.contact_id,
          status: true,
          is_active: true,
        },
      });
      if (existingDocument) {
        existingDocument.attachment_id = attachment.id;
        existingDocument.updated_by = userId;
        response = await this.uploadedFielRepository.save(existingDocument);
      } else {
        const uploadedFiles = this.uploadedFielRepository.create({
          doc_type_id: createUploadedFilesDto.doc_type_id,
          attachment_id: attachment.id,
          lead_id: createUploadedFilesDto.lead_id,
          contact_id: createUploadedFilesDto.contact_id,
          created_by: userId,
          updated_by: userId,
        });
        response = await this.uploadedFielRepository.save(uploadedFiles);
      }

      return {
        success: true,
        message: 'Documents upload successfully',
        data: response,
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        message: 'Document upload failed',
        data: null,
      };
    }
  }

  async getAllUploadedDocumets(
    leadId: number,
    clientId: number,
  ): Promise<PaginatedResponse<any>> {
    try {
      const uploadedFiles = await this.uploadedFielRepository.find({
        where: {
          lead_id: leadId,
          status: true,
          is_active: true,
        },
        relations: {
          doc_type: true,
          attachment: true,
        },
      });
      const data = uploadedFiles.map((uploadedFile) => ({
        uploaded_doc_id: uploadedFile.id,
        doc_type_id: uploadedFile.doc_type_id,
        doc_type_title: uploadedFile.doc_type.document_title,
        attachment_id: uploadedFile.attachment_id,
        attachment_name: uploadedFile.attachment.file_name,
      }));

      return {
        data,
        meta: null,
      };
    } catch (error) {
      return {
        data: [],
        meta: null,
      };
    }
  }

  async removeUploadedDocument(uploadedFileId: number, userId: number) {
    try {
      const uploadedFile = await this.uploadedFielRepository.findOne({
        where: { id: uploadedFileId },
      });
      if (!uploadedFile) {
        throw new NotFoundException(
          `Uploaded file with ID ${uploadedFileId} not found`,
        );
      }
      uploadedFile.updated_by = userId;
      uploadedFile.is_active = false;
      uploadedFile.status = false;
      await this.uploadedFielRepository.save(uploadedFile);
      return {
        success: true,
        message: 'Uploaded file deleted successfully',
        data: null,
      };
    } catch (error) {
      return {
        success: false,
        message: 'Uploaded file delete failed',
        data: null,
      };
    }
  }

  async findOne(id: number): Promise<DocType> {
    const docType = await this.docTypeRepository.findOne({
      where: { id },
    });

    if (!docType) {
      throw new NotFoundException(`Document type with ID ${id} not found`);
    }

    return docType;
  }

  async update(
    updateDocTypeDto: UpdateDocTypeDto,
    userId: number,
  ): Promise<DocType> {
    const docType = await this.findOne(updateDocTypeDto.id);

    Object.assign(docType, updateDocTypeDto);
    docType.updated_by = userId;
    return await this.docTypeRepository.save(docType);
  }

  async remove(id: number, userId: number): Promise<DocType> {
    const docType = await this.findOne(id);
    docType.updated_by = userId;
    docType.status = false;
    docType.is_active = false;
    return this.docTypeRepository.save(docType);
  }
}
