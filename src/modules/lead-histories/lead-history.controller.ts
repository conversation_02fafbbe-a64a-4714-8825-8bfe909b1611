import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LeadHistoryService } from './services/lead-history.service';
import { FindAllLeadHistoryDto } from './dtos/get-lead-history.dto';
import { getLeadHistoryCountsDto } from './dtos/get-history-counts.dto';

@ApiTags('lead-histories')
@Controller('lead-histories')
export class LeadHistoryController {
  constructor(private readonly leadHistoryService: LeadHistoryService) {}

  @Post()
  async getAllHistories(@Body() dto: FindAllLeadHistoryDto) {
    return this.leadHistoryService.findAll(dto);
  }

  @Post('get-counts')
  async getHistoryCounts(@Body() dto: getLeadHistoryCountsDto) {
    return this.leadHistoryService.getInteractionCounts(dto);
  }

  @Post('create-action')
  async createAction(@Body() body: { action: string }) {
    return this.leadHistoryService.createAction(body);
  }

  @Post('create-sub-action')
  async createSubAction(
    @Body() { action, subActions }: { action: string; subActions: string[] },
  ) {
    return this.leadHistoryService.createSubAction(action, subActions);
  }

  @Post('create-lead-history')
  async createLeadHistory(@Body() payload: any) {
    return this.leadHistoryService.createLeadHistory(payload);
  }

  @Post('create-details')
  async createDetails(
    @Body() { subActionName, fields }: { subActionName: string; fields: any },
  ) {
    return this.leadHistoryService.createDetails(subActionName, fields);
  }
}
