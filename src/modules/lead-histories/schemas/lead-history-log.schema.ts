import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ collection: 'lead_history_logs', timestamps: true })
export class LeadHistoryLog extends Document {
  @Prop({ type: Object, required: true })
  payload: object;

  @Prop({ type: String, required: false })
  error: string;

  @Prop({ type: String, required: true })
  status: string;
}

export const LeadHistoryLogSchema =
  SchemaFactory.createForClass(LeadHistoryLog);
