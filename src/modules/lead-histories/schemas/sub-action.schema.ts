import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ collection: 'subactions' }) // Optional: name the collection
export class SubAction extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Action', required: true })
  actionId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  subAction: string;
}

export const SubActionSchema = SchemaFactory.createForClass(SubAction);
