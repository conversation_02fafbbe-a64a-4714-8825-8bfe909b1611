import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
export class LevelChange extends Document {
  @Prop({ type: String, required: false })
  initial: string;

  @Prop({ type: String, required: false })
  next: string;

  @Prop({ type: String, required: false })
  type: string;

  @Prop({ type: String, required: false })
  program?: string;

  @Prop({ type: String, required: false })
  comment?: string;
}

@Schema({ collection: 'lead_history', timestamps: true })
export class LeadHistory extends Document {
  @Prop({ type: Number, required: false })
  leadId: number;

  @Prop({ type: Number, required: true })
  contactId: number;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Action', required: true })
  actionId: MongooseSchema.Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'SubAction',
    required: true,
  })
  subActionId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Object, required: true })
  details: object;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Details', required: true }) // Dynamically references correct details collection
  detailsId: MongooseSchema.Types.ObjectId;

  @Prop({ type: Number, required: false })
  performedBy: number;

  @Prop({ type: Object, required: true, default: { first_name: 'System' } })
  performedByUser: object;

  @Prop({ type: [LevelChange], required: false })
  levels: LevelChange[];

  @Prop({ type: Boolean, required: true, default: false })
  isGlobalAction: boolean;

  @Prop({ type: Object, required: false })
  metadata: object;

  @Prop({ type: Date, required: true })
  date: Date;
}

export const LeadHistorySchema = SchemaFactory.createForClass(LeadHistory);
