import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
export class ValidationSchema extends Document {
  @Prop({ type: String, required: false })
  type: string;

  @Prop({ type: Boolean, required: false })
  additionalProperties: boolean;

  @Prop({ type: [String], required: false })
  required: string[];

  @Prop({ type: Object, required: false })
  properties?: Record<string, any>;
}

@Schema({ collection: 'details' })
export class Details extends Document {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'SubAction',
    required: true,
  })
  subActionId: MongooseSchema.Types.ObjectId;

  @Prop({
    type: Object,
    required: true,
  })
  details: ValidationSchema;

  @Prop({
    type: Boolean,
    required: true,
  })
  isCurrent: boolean;
}

export const DetailsSchema = SchemaFactory.createForClass(Details);
