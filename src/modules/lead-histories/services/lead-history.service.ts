import { Injectable, NotFoundException } from '@nestjs/common';
import { LeadHistory, LevelChange } from '../schemas/lead-history.schema';
import { Model } from 'mongoose';
import { Action } from '../schemas/action.schema';
import { Details } from '../schemas/details.schema';
import { InjectModel } from '@nestjs/mongoose';
import { OnModuleInit } from '@nestjs/common';
import { SubAction } from '../schemas/sub-action.schema';
import { LeadHistoryLog } from '../schemas/lead-history-log.schema';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import { FindAllLeadHistoryDto } from '../dtos/get-lead-history.dto';
import {
  ActionType,
  AppointmentSubActionType,
  EngagementSubActionType,
} from '../enums/lead-history.enum';
import { getLeadHistoryCountsDto } from '../dtos/get-history-counts.dto';
import { LeadProgramInterestService } from '@modules/leads/services/lead-program-interest.service';
import { LeadLevelHistoryService } from '@modules/leads/services/lead-level-history.service';

const ajv = new Ajv({
  allErrors: true,
  removeAdditional: 'all',
  useDefaults: true,
});
addFormats(ajv);
@Injectable()
export class LeadHistoryService implements OnModuleInit {
  constructor(
    @InjectModel('LeadHistory') private leadHistoryModel: Model<LeadHistory>,
    @InjectModel('Action') private actionModel: Model<Action>,
    @InjectModel('SubAction') private subActionModel: Model<SubAction>,
    @InjectModel('Details') private detailsModel: Model<Details>,
    @InjectModel('LeadHistoryLog')
    private leadHistoryLogModel: Model<LeadHistoryLog>,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly leadLevelHistoryService: LeadLevelHistoryService,
  ) {}

  /**
   * Initialize default actions on module initialization, creating action documents if they don't exist
   */
  async onModuleInit() {
    const defaultActions = [
      'request',
      'engagement',
      'contact_updation',
      'lead_management',
      'appointment',
    ];

    for (const action of defaultActions) {
      await this.actionModel.updateOne(
        { action },
        { $setOnInsert: { action } },
        { upsert: true },
      );
    }
  }

  /**
   * Create lead history based on action and subaction
   */
  async createLeadHistory(payload: {
    action: string;
    subAction: string;
    leadId: number;
    contactId: number;
    performedByUser: any;
    details: any;
    levels?: any[];
    isGlobalAction?: boolean;
    performedBy?: number;
    date?: Date;
    metadata?: any;
  }) {
    try {
      // 1. Find the action document
      const actionDoc = await this.actionModel.findOne({
        action: payload.action,
      });
      if (!actionDoc) {
        throw new NotFoundException(`Action ${payload.action} not found`);
      }

      // 2. Find the subAction document
      const subActionDoc = await this.subActionModel.findOne({
        subAction: payload.subAction,
      });
      if (!subActionDoc) {
        throw new NotFoundException(`SubAction ${payload.subAction} not found`);
      }

      // 3. Find the details schema based on subActionId
      const detailsSchemaDoc = await this.detailsModel.findOne({
        subActionId: subActionDoc._id,
        isCurrent: true,
      });
      if (!detailsSchemaDoc || !detailsSchemaDoc.details) {
        throw new NotFoundException(
          `Details schema for subAction ${payload.subAction} not found`,
        );
      }

      // 4. Validate payload.details against JSON schema using AJV
      const validate = ajv.compile(detailsSchemaDoc.details);
      const isValid = validate(payload.details);

      if (!isValid) {
        const errors = validate.errors
          ?.map((err) => `${err?.instancePath} ${err?.message}`)
          .join(', ');
        throw new Error(`Validation failed for details: ${errors}`);
      }

      // 5. Save only the validated fields in a clean object
      const allowedKeys = Object.keys(
        detailsSchemaDoc?.details?.properties || {},
      );
      const validatedDetails: any = {};
      for (const key of allowedKeys) {
        if (key in payload.details) {
          validatedDetails[key] = payload.details[key];
        }
      }

      if (!payload?.levels) {
        const programs = await this.leadProgramInterestService.findAllByLeadId(
          payload?.leadId,
        );
        payload.levels = programs?.map((program) => ({
          initial: program?.lead_level?.name,
          next: program?.lead_level?.name,
          type: program?.lead_level?.temperature,
          program: program?.program?.name,
          comment: null,
        }));
      }

      const leadHistory = await this.leadHistoryModel.create({
        leadId: payload?.leadId,
        contactId: payload?.contactId,
        actionId: actionDoc._id,
        subActionId: subActionDoc._id,
        details: validatedDetails,
        detailsId: detailsSchemaDoc._id,
        performedBy: payload?.performedBy,
        performedByUser: payload?.performedByUser ?? { first_name: 'System' },
        levels: payload?.levels,
        isGlobalAction: payload.isGlobalAction || false,
        date: payload.date ?? new Date(Date.now()),
        metadata: payload?.metadata,
      });

      // Log successful creation
      await this.leadHistoryLogModel.create({
        payload,
        status: 'success',
      });

      return leadHistory;
    } catch (error) {
      // Log the error with the payload
      await this.leadHistoryLogModel.create({
        payload,
        error: error?.message || 'Unknown error',
        status: 'error',
      });

      console.error('Error creating lead history:', error);
    }
  }

  /**
   * Create action in action collection
   */
  async createAction(body: { action: string }) {
    return await this.actionModel.create(body);
  }

  /**
   * Create subaction in subaction collection
   */
  async createSubAction(action: string, subActions: string[]) {
    const actionDoc = await this.actionModel.findOne({ action }).exec();
    if (!actionDoc) throw new NotFoundException(`Action "${action}" not found`);

    // Get existing subactions for this action
    const existingSubActions = await this.subActionModel
      .find({ actionId: actionDoc.id })
      .select('subAction')
      .exec();

    // Get unique subactions that don't already exist
    const existingNames = new Set(
      existingSubActions?.map((sa) => sa?.subAction),
    );
    const newSubActions = subActions?.filter(
      (subAction) => !existingNames.has(subAction),
    );

    if (newSubActions.length === 0) {
      return [];
    }

    // Create only new subactions
    const subActionDocs = await Promise.all(
      newSubActions?.map((subAction) =>
        this.subActionModel.create({
          actionId: actionDoc.id,
          subAction: subAction,
        }),
      ),
    );

    return subActionDocs;
  }

  /**
   * Create details for a subaction with specified fields
   */
  async createDetails(subActionName: string, fields: any) {
    // Find the subaction
    const subAction = await this.subActionModel
      .findOne({ subAction: subActionName })
      .exec();
    if (!subAction) {
      throw new NotFoundException(`Subaction "${subActionName}" not found`);
    }

    // Set all existing details for this subaction to isCurrent: false
    await this.detailsModel.updateMany(
      { subActionId: subAction.id, isCurrent: true },
      { $set: { isCurrent: false } },
    );

    // Create new details document
    const details = await this.detailsModel.create({
      subActionId: subAction.id,
      details: fields,
      isCurrent: true,
    });

    return details;
  }

  /**
   * Get all lead histories
   */
  async findAll(
    findAllLeadHistoryDto: FindAllLeadHistoryDto,
  ): Promise<{ totalCount: number; data: any }> {
    try {
      const {
        leadId,
        contactId,
        action,
        subAction,
        limit,
        offset,
        startDate,
        endDate,
      } = findAllLeadHistoryDto;

      const filter: any = {};

      // Filtering by contactId or leadId(one is required always)
      if (contactId) {
        filter.contactId = contactId;
      } else if (leadId) {
        filter.leadId = leadId;
      }

      // Filtering by action
      if (action) {
        const actionDoc = await this.actionModel.findOne({ action }).exec();
        if (!actionDoc)
          throw new NotFoundException(`Action "${action}" not found`);
        filter.actionId = actionDoc.id;
      }

      // Filtering by subAction
      if (subAction) {
        const subActionDoc = await this.subActionModel
          .findOne({ subAction })
          .exec();
        if (!subActionDoc)
          throw new NotFoundException(`SubAction "${subAction}" not found`);
        filter.subActionId = subActionDoc.id;
      }

      if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) {
          filter.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          filter.createdAt.$lte = new Date(endDate);
        }
      }
      const totalCount = await this.leadHistoryModel.countDocuments(filter);
      const data = await this.leadHistoryModel
        .find(filter)
        .skip(limit * offset)
        .limit(limit)
        .populate('actionId', 'action')
        .populate('subActionId', 'subAction')
        .sort({ date: -1 })
        .lean()
        .exec();

      // Transform the data to remove unwanted fields and keep only action and subAction
      const transformedData = data.map((item) => {
        const populatedAction = item.actionId as unknown as { action: string };
        const populatedSubAction = item.subActionId as unknown as {
          subAction: string;
        };

        return {
          action: populatedAction?.action,
          subAction: populatedSubAction?.subAction,
          ...item,
          actionId: undefined,
          subActionId: undefined,
          isGlobalAction: undefined,
          detailsId: undefined,
          metadata: undefined,
        };
      });

      return { totalCount, data: transformedData };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update call-related data in lead history
   */
  async updateCallHistory(
    callId: number,
    updateData: {
      disposition?: any;
      levelChanges?: LevelChange[];
    },
  ): Promise<void> {
    try {
      // Find the lead history document with action 'calls' and matching callId
      const leadHistory = await this.leadHistoryModel.findOne({
        'details.callId': callId,
        subActionId: await this.getSubActionIdByName('calls'),
      });

      if (!leadHistory) {
        return null;
      }

      // Update disposition if provided
      if (updateData?.disposition) {
        leadHistory.details = {
          ...leadHistory.details,
          disposition: updateData.disposition,
        };

        // If levelChanges not explicitly provided, derive from disposition
        if (!updateData.levelChanges) {
          // Get level history and current programs in parallel
          const [leadLevelHistory, currentPrograms] = await Promise.all([
            this.leadLevelHistoryService.getHistoryByDisposition(
              updateData.disposition.id,
            ),
            this.leadProgramInterestService.findAllByLeadId(leadHistory.leadId),
          ]);

          // Create a map for O(1) lookups instead of nested loops
          const programLevels = new Map(
            leadLevelHistory.map((item) => [
              item?.lead_program_interest?.program?.name,
              {
                next: item?.to_level?.name,
                initial: item?.from_level?.name,
                type: item?.lead_program_interest?.lead_level?.temperature,
                program: item?.lead_program_interest?.program?.name,
                comment: item?.comments,
              },
            ]),
          );

          // Merge with current programs, prioritizing disposition history
          leadHistory.levels = currentPrograms.map((program) => {
            const programName = program?.program?.name;
            return (
              programLevels.get(programName) || {
                next: program?.lead_level?.name,
                initial: program?.lead_level?.name,
                type: program?.lead_level?.temperature,
                program: programName,
                comment: program?.comments,
              }
            );
          }) as any[];
        }
      }

      // Update level changes if explicitly provided
      if (updateData?.levelChanges) {
        leadHistory.levels = updateData.levelChanges;
      }

      // Save and return the updated document
      await leadHistory.save();
    } catch (error) {
      console.error('Error updating call history:', error);
      return null;
    }
  }

  /**
   * Helper method to get action ID by name
   */
  private async getSubActionIdByName(subActionName: string): Promise<any> {
    const action = await this.subActionModel.findOne({
      subAction: subActionName,
    });
    if (!action) {
      console.log(`Action "${subActionName}" not found`);
    }
    return action._id;
  }

  /**
   * Get counts for specific interaction types
   */
  async getInteractionCounts(dto: getLeadHistoryCountsDto) {
    try {
      const filter: any = {};

      // Filter by leadId or contactId if provided
      if (dto.leadId) {
        filter.leadId = dto.leadId;
      } else if (dto.contactId) {
        filter.contactId = dto.contactId;
      }
      // Get action IDs for engagement and appointment actions
      const engagementAction = await this.actionModel
        .findOne({
          action: ActionType.ENGAGEMENT,
        })
        .exec();

      const appointmentAction = await this.actionModel
        .findOne({
          action: ActionType.APPOINTMENT,
        })
        .exec();

      if (!engagementAction || !appointmentAction) {
        throw new Error('Required action types not found in database');
      }

      // Get all subactions for the engagement and appointment actions
      const engagementSubactions = await this.subActionModel
        .find({
          actionId: engagementAction._id,
        })
        .exec();

      const appointmentSubactions = await this.subActionModel
        .find({
          actionId: appointmentAction._id,
        })
        .exec();

      // Create a map of subaction names to their IDs
      const subactionMap = {};

      // Map engagement subactions
      engagementSubactions.forEach((subaction) => {
        subactionMap[subaction.subAction] = subaction._id;
      });

      // Map appointment subactions
      appointmentSubactions.forEach((subaction) => {
        subactionMap[subaction.subAction] = subaction._id;
      });

      // Define the counts we want to retrieve
      const countsToRetrieve = {
        calls: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.CALLS],
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        emails: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.MESSAGES],
          'details.messageCategory': 'email',
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        whatsapp: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.MESSAGES],
          'details.messageCategory': 'whatsapp',
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        enquiries: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.ENQUIRY],
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        webinars_registered: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.WEBINAR],
          'details.visitStatus': 'registered',
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        webinars_attended: {
          actionId: engagementAction._id,
          subActionId: subactionMap[EngagementSubActionType.WEBINAR],
          'details.visitStatus': 'attended',
          date: {
            $gte: new Date(dto.start_date || new Date(0)),
            $lte: new Date(dto.end_date || new Date('9999-12-31')),
          },
        },
        visits: {
          // actionId: appointmentAction._id,
          subActionId:
            subactionMap[AppointmentSubActionType.OFFICE_VISIT_SCHEDULED],
          'details.date': {
            $gte: new Date(dto.start_date || new Date(0)).toISOString(),
            $lte: new Date(
              dto.end_date || new Date('9999-12-31'),
            ).toISOString(),
          },
        },
        meets: {
          // actionId: appointmentAction._id,
          subActionId:
            subactionMap[AppointmentSubActionType.GOOGLE_MEET_SCHEDULED],
          'details.date': {
            $gte: new Date(dto.start_date || new Date(0)).toISOString(),
            $lte: new Date(
              dto.end_date || new Date('9999-12-31'),
            ).toISOString(),
          },
        },
      };
      // Execute count queries for each type
      const countsObj = {};
      for (const [key, conditions] of Object.entries(countsToRetrieve)) {
        const count = await this.leadHistoryModel.countDocuments({
          ...filter,
          ...conditions,
        });
        countsObj[key] = count;
      }

      // Transform to the requested array format
      const result = [
        {
          actionType: 'Calls',
          count: (countsObj as Record<string, number>).calls || 0,
        },
        {
          actionType: 'Emails',
          count: (countsObj as Record<string, number>).emails || 0,
        },
        {
          actionType: 'WhatsApp',
          count: (countsObj as Record<string, number>).whatsapp || 0,
        },
        {
          actionType: 'Enquiries',
          count: (countsObj as Record<string, number>).enquiries || 0,
        },
        {
          actionType: 'Visits',
          count: (countsObj as Record<string, number>).visits || 0,
        },
        {
          actionType: 'Meets',
          count: (countsObj as Record<string, number>).meets || 0,
        },
        {
          actionType: 'Webinars Registered',
          count: (countsObj as Record<string, number>).webinars_registered || 0,
        },
        {
          actionType: 'Webinars Attended',
          count: (countsObj as Record<string, number>).webinars_attended || 0,
        },
      ];

      return result;
    } catch (error) {
      console.error('Error getting interaction counts:', error);
      throw error;
    }
  }

  /**
   * Get counts for specific interaction types
   */
  async getEmailPhoneViewedCounts(
    subActionName: EngagementSubActionType,
    userId: number,
    itemId: number,
  ): Promise<number> {
    try {
      const filter: any = {
        subActionId: await this.getSubActionIdByName(subActionName),
      };

      // Set the correct field based on subaction type
      if (subActionName === EngagementSubActionType.PHONE_NUMBER_VIEWED) {
        filter['details.phoneId'] = itemId;
        filter['details.userId'] = userId;
      } else if (subActionName === EngagementSubActionType.EMAIL_VIEWED) {
        filter['details.emailId'] = itemId;
        filter['details.userId'] = userId;
      }

      const count = await this.leadHistoryModel.countDocuments(filter);
      return count;
    } catch (error) {
      console.error('Error getting interaction counts:', error);
      return 0;
    }
  }
}
