import { forwardRef, Global, Module } from '@nestjs/common';
import { LeadHistoryController } from './lead-history.controller';
import { LeadHistoryService } from './services/lead-history.service';
import { LeadHistorySchema } from './schemas/lead-history.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { DetailsSchema } from './schemas/details.schema';
import { ActionSchema } from './schemas/action.schema';
import { SubActionSchema } from './schemas/sub-action.schema';
import { LeadHistoryLogSchema } from './schemas/lead-history-log.schema';
import { LeadModule } from '@modules/leads/lead.module';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'LeadHistory', schema: LeadHistorySchema },
      { name: 'Details', schema: DetailsSchema },
      { name: 'Action', schema: ActionSchema },
      { name: 'SubAction', schema: SubActionSchema },
      { name: 'LeadHistoryLog', schema: LeadHistoryLogSchema },
    ]),
    forwardRef(() => LeadModule),
  ],
  controllers: [LeadHistoryController],
  providers: [LeadHistoryService],
  exports: [LeadHistoryService],
})
export class LeadHistoryModule {}
