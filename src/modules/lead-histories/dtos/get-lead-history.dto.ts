import {
  IsOptional,
  IsString,
  IsInt,
  Min,
  IsDateString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FindAllLeadHistoryDto {
  @ApiProperty()
  @IsOptional()
  @IsInt()
  readonly leadId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  readonly contactId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  readonly action?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly subAction?: string;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  readonly limit?: number = 10;

  @ApiProperty({ required: false, default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  readonly offset?: number = 0;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
