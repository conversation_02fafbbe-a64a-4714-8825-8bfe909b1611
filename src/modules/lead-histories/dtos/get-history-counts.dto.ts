import { IsOptional, IsInt, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class getLeadHistoryCountsDto {
  @ApiProperty()
  @IsOptional()
  @IsInt()
  readonly leadId: number;

  @ApiProperty()
  @IsOptional()
  @IsInt()
  readonly contactId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  end_date?: string;
}
