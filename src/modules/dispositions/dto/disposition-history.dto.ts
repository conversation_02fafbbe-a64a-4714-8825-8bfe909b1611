import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ClientAwareDto } from '../../../common/dto/client-aware.dto';
import { Type } from 'class-transformer';
import { ApprovalStatus } from '../enums/disposition-history.enum';

export class CreateDispositionHistoryDto extends ClientAwareDto {
  @ApiProperty({ description: 'Lead ID' })
  @IsInt()
  @IsNotEmpty()
  lead_id: number;

  @ApiPropertyOptional({ description: 'SPOC User ID', nullable: true })
  @IsOptional()
  @IsInt()
  spoc_user_id?: number | null;

  @ApiPropertyOptional({
    description: 'Final disposition node ID',
    nullable: true,
  })
  @IsOptional()
  @IsInt()
  final_disposition_node_id?: number | null;

  @ApiPropertyOptional({
    description: 'Comments about this disposition',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  comments?: string | null;

  @ApiProperty({ description: 'Level before ID' })
  @IsInt()
  @IsNotEmpty()
  level_before_id: number;

  @ApiPropertyOptional({ description: 'Level after ID', nullable: true })
  @IsOptional()
  @IsInt()
  level_after_id?: number | null;

  @ApiPropertyOptional({ description: 'Next follow-up date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  next_follow_up_date?: Date | null;

  @ApiPropertyOptional({
    description: 'GM approval status',
    enum: ApprovalStatus,
    default: ApprovalStatus.NOT_REQUIRED,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approval_status?: ApprovalStatus;

  @ApiPropertyOptional({ description: 'Approver ID', nullable: true })
  @IsOptional()
  @IsInt()
  approver_id?: number | null;

  @ApiPropertyOptional({ description: 'Approver comments', nullable: true })
  @IsOptional()
  @IsString()
  approver_comments?: string | null;

  @ApiPropertyOptional({ description: 'Additional data', nullable: true })
  @IsOptional()
  additional_data?: any | null;

  @ApiPropertyOptional({
    description: 'Level disposition outcome ID',
    nullable: true,
  })
  @IsOptional()
  @IsInt()
  level_disposition_outcome_id?: number | null;
}

export class UpdateDispositionHistoryDto {
  @ApiPropertyOptional({ description: 'SPOC User ID' })
  @IsOptional()
  @IsInt()
  spoc_user_id?: number;

  @ApiPropertyOptional({ description: 'Comments about this disposition' })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({ description: 'Level after ID' })
  @IsOptional()
  @IsInt()
  level_after_id?: number;

  @ApiPropertyOptional({ description: 'Next follow-up date' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  next_follow_up_date?: Date;

  @ApiPropertyOptional({
    description: 'GM approval status',
    enum: ApprovalStatus,
  })
  @IsOptional()
  @IsEnum(ApprovalStatus)
  approval_status?: ApprovalStatus;

  @ApiPropertyOptional({ description: 'Approver ID' })
  @IsOptional()
  @IsInt()
  approver_id?: number;

  @ApiPropertyOptional({ description: 'Approval timestamp' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  approval_timestamp?: Date;

  @ApiPropertyOptional({ description: 'Approver comments' })
  @IsOptional()
  @IsString()
  approver_comments?: string;

  @ApiPropertyOptional({ description: 'Additional data' })
  @IsOptional()
  additional_data?: any;
}

export class DispositionHistoryDto extends ClientAwareDto {
  @ApiProperty({ description: 'Unique identifier' })
  id: number;

  @ApiProperty({ description: 'Lead ID' })
  lead_id: number;

  @ApiPropertyOptional({ description: 'SPOC User ID', nullable: true })
  spoc_user_id: number | null;

  @ApiProperty({ description: 'Event timestamp' })
  event_timestamp: Date;

  @ApiPropertyOptional({
    description: 'Final disposition node ID',
    nullable: true,
  })
  final_disposition_node_id: number | null;

  @ApiPropertyOptional({
    description: 'Comments about this disposition',
    nullable: true,
  })
  comments: string | null;

  @ApiProperty({ description: 'Level before ID' })
  level_before_id: number;

  @ApiPropertyOptional({ description: 'Level after ID', nullable: true })
  level_after_id: number | null;

  @ApiPropertyOptional({ description: 'Next follow-up date', nullable: true })
  next_follow_up_date: Date | null;

  @ApiProperty({
    description: 'GM approval status',
    enum: ApprovalStatus,
  })
  approval_status: ApprovalStatus;

  @ApiPropertyOptional({ description: 'Approver ID', nullable: true })
  approver_id: number | null;

  @ApiPropertyOptional({ description: 'Approval timestamp', nullable: true })
  approval_timestamp: Date | null;

  @ApiPropertyOptional({ description: 'Approver comments', nullable: true })
  approver_comments: string | null;

  @ApiPropertyOptional({ description: 'Additional data', nullable: true })
  additional_data: any | null;

  @ApiPropertyOptional({
    description: 'Level disposition outcome ID',
    nullable: true,
  })
  level_disposition_outcome_id: number | null;

  @ApiPropertyOptional({ description: 'Related disposition node' })
  final_disposition_node?: any;

  @ApiPropertyOptional({ description: 'Related SPOC user' })
  spoc_user?: any;

  @ApiPropertyOptional({ description: 'Related approver user' })
  approver_user?: any;

  @ApiPropertyOptional({ description: 'Related lead' })
  lead?: any;

  @ApiPropertyOptional({ description: 'Related level before' })
  level_before?: any;

  @ApiPropertyOptional({ description: 'Related level after' })
  level_after?: any;

  @ApiPropertyOptional({ description: 'Created at timestamp' })
  created_at?: Date;

  @ApiPropertyOptional({ description: 'Updated at timestamp' })
  updated_at?: Date;
}
