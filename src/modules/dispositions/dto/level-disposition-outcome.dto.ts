import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ClientAwareDto } from '../../../common/dto/client-aware.dto';
import { OutcomeAutoFollowUpLogic } from '../enums/disposition.enum';

export class CreateLevelDispositionOutcomeDto extends ClientAwareDto {
  @ApiPropertyOptional({ description: 'From level ID', nullable: true })
  @IsOptional()
  @IsInt()
  from_level_id?: number | null;

  @ApiProperty({ description: 'Final disposition node ID' })
  @IsInt()
  @IsNotEmpty()
  final_disposition_node_id: number;

  @ApiPropertyOptional({ description: 'To level ID', nullable: true })
  @IsOptional()
  @IsInt()
  to_level_id?: number | null;

  @ApiPropertyOptional({
    description: 'Whether this rule causes an automatic level transition',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_auto_transition?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this transition requires GM approval',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  requires_approval_for_transition?: boolean;

  @ApiPropertyOptional({
    description: 'Auto follow-up logic for this outcome',
    enum: OutcomeAutoFollowUpLogic,
    default: OutcomeAutoFollowUpLogic.FromLevelDefault,
  })
  @IsOptional()
  @IsEnum(OutcomeAutoFollowUpLogic)
  auto_follow_up_logic?: OutcomeAutoFollowUpLogic;

  @ApiPropertyOptional({
    description: 'Specific field for auto follow-up',
    nullable: true,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  auto_follow_up_specific_field?: string | null;

  @ApiPropertyOptional({ description: 'JSON array of required data fields' })
  @IsOptional()
  required_data_fields_json?: any | null;

  @ApiPropertyOptional({ description: 'Notes about this rule', nullable: true })
  @IsOptional()
  @IsString()
  rule_notes?: string | null;
}

export class UpdateLevelDispositionOutcomeDto {
  @ApiPropertyOptional({ description: 'To level ID' })
  @IsOptional()
  @IsInt()
  to_level_id?: number | null;

  @ApiPropertyOptional({
    description: 'Whether this rule causes an automatic level transition',
  })
  @IsOptional()
  @IsBoolean()
  is_auto_transition?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this transition requires GM approval',
  })
  @IsOptional()
  @IsBoolean()
  requires_gm_approval_for_transition?: boolean;

  @ApiPropertyOptional({
    description: 'Auto follow-up logic for this outcome',
    enum: OutcomeAutoFollowUpLogic,
  })
  @IsOptional()
  @IsEnum(OutcomeAutoFollowUpLogic)
  auto_follow_up_logic?: OutcomeAutoFollowUpLogic;

  @ApiPropertyOptional({
    description: 'Specific field for auto follow-up',
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  auto_follow_up_specific_field?: string | null;

  @ApiPropertyOptional({ description: 'JSON array of required data fields' })
  @IsOptional()
  required_data_fields_json?: any | null;

  @ApiPropertyOptional({ description: 'Notes about this rule' })
  @IsOptional()
  @IsString()
  rule_notes?: string | null;
}

export class LevelDispositionOutcomeDto extends ClientAwareDto {
  @ApiProperty({ description: 'Unique identifier' })
  id: number;

  @ApiProperty({ description: 'From level ID', nullable: true })
  from_level_id: number | null;

  @ApiProperty({ description: 'Final disposition node ID' })
  final_disposition_node_id: number;

  @ApiProperty({ description: 'To level ID', nullable: true })
  to_level_id: number | null;

  @ApiProperty({
    description: 'Whether this rule causes an automatic level transition',
  })
  is_auto_transition: boolean;

  @ApiProperty({ description: 'Whether this transition requires GM approval' })
  requires_gm_approval_for_transition: boolean;

  @ApiProperty({
    description: 'Auto follow-up logic for this outcome',
    enum: OutcomeAutoFollowUpLogic,
  })
  auto_follow_up_logic: OutcomeAutoFollowUpLogic;

  @ApiProperty({
    description: 'Specific field for auto follow-up',
    nullable: true,
  })
  auto_follow_up_specific_field: string | null;

  @ApiProperty({
    description: 'JSON array of required data fields',
    nullable: true,
  })
  required_data_fields_json: any | null;

  @ApiProperty({ description: 'Notes about this rule', nullable: true })
  rule_notes: string | null;
}
