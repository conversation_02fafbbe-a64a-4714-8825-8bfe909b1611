import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { Level1Group } from '../enums/disposition.enum';
import { ClientAwareDto } from '../../../common/dto/client-aware.dto';

export class CreateDispositionNodeDto extends ClientAwareDto {
  @ApiPropertyOptional({ description: 'Parent node ID', nullable: true })
  @IsOptional()
  @IsInt()
  parent_id?: number | null;

  @ApiProperty({ description: 'Display text shown in UI', maxLength: 100 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  display_text: string;

  @ApiPropertyOptional({ description: 'Display order', default: 0 })
  @IsOptional()
  @IsInt()
  display_order?: number;

  @ApiProperty({ description: 'Level order in the hierarchy' })
  @IsInt()
  @IsNotEmpty()
  level_order: number;

  @ApiPropertyOptional({
    description: 'Whether this node is a selectable outcome',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_selectable_outcome?: boolean;

  @ApiPropertyOptional({
    description: 'Mandatory L1 grouping for top-level nodes',
    enum: Level1Group,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(Level1Group)
  level1_group?: Level1Group | null;

  @ApiPropertyOptional({ description: 'Notes about this node', nullable: true })
  @IsOptional()
  @IsString()
  notes?: string | null;
}

export class UpdateDispositionNodeDto {
  @ApiPropertyOptional({
    description: 'Display text shown in UI',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  display_text?: string;

  @ApiPropertyOptional({ description: 'Display order' })
  @IsOptional()
  @IsInt()
  display_order?: number;

  @ApiPropertyOptional({
    description: 'Whether this node is a selectable outcome',
  })
  @IsOptional()
  @IsBoolean()
  is_selectable_outcome?: boolean;

  @ApiPropertyOptional({
    description: 'Mandatory L1 grouping for top-level nodes',
    enum: Level1Group,
  })
  @IsOptional()
  @IsEnum(Level1Group)
  level1_group?: Level1Group;

  @ApiPropertyOptional({ description: 'Notes about this node' })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class DispositionNodeDto extends ClientAwareDto {
  @ApiProperty({ description: 'Unique identifier' })
  id: number;

  @ApiProperty({ description: 'Parent node ID', nullable: true })
  parent_id: number | null;

  @ApiProperty({ description: 'Display text shown in UI' })
  display_text: string;

  @ApiProperty({ description: 'Display order' })
  display_order: number;

  @ApiProperty({ description: 'Level order in the hierarchy' })
  level_order: number;

  @ApiProperty({ description: 'Whether this node is a selectable outcome' })
  is_selectable_outcome: boolean;

  @ApiProperty({
    description: 'Mandatory L1 grouping for top-level nodes',
    enum: Level1Group,
    nullable: true,
  })
  level1_group: Level1Group | null;

  @ApiProperty({ description: 'Notes about this node', nullable: true })
  notes: string | null;

  @ApiPropertyOptional({
    description: 'Children nodes',
    type: [DispositionNodeDto],
  })
  children?: DispositionNodeDto[];
}
