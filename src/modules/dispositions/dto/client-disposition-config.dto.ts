import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { AutoFollowUpLogic } from '../enums/disposition.enum';
import { ClientAwareDto } from '../../../common/dto/client-aware.dto';
import { Type } from 'class-transformer';
import { CreateProgramInterestDuringDispositionDto } from './create-program-interest-during-disposition.dto';

export class CreateClientDispositionConfigDto extends ClientAwareDto {
  @ApiProperty({ description: 'Final disposition node ID' })
  @IsInt()
  @IsNotEmpty()
  final_disposition_node_id: number;

  @ApiPropertyOptional({
    description: 'Whether comments are required by default',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  default_required_comments?: boolean;

  @ApiPropertyOptional({
    description: 'Whether next follow-up date is required by default',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  default_required_next_follow_up_date?: boolean;

  @ApiPropertyOptional({
    description: 'Default auto follow-up logic',
    enum: AutoFollowUpLogic,
    default: AutoFollowUpLogic.None,
  })
  @IsOptional()
  @IsEnum(AutoFollowUpLogic)
  default_auto_follow_up_logic?: AutoFollowUpLogic;
}

export class UpdateClientDispositionConfigDto {
  @ApiPropertyOptional({
    description: 'Whether comments are required by default',
  })
  @IsOptional()
  @IsBoolean()
  default_required_comments?: boolean;

  @ApiPropertyOptional({
    description: 'Whether next follow-up date is required by default',
  })
  @IsOptional()
  @IsBoolean()
  default_required_next_follow_up_date?: boolean;

  @ApiPropertyOptional({
    description: 'Default auto follow-up logic',
    enum: AutoFollowUpLogic,
  })
  @IsOptional()
  @IsEnum(AutoFollowUpLogic)
  default_auto_follow_up_logic?: AutoFollowUpLogic;
}

export class ClientDispositionConfigDto extends ClientAwareDto {
  @ApiProperty({ description: 'Unique identifier' })
  id: number;

  @ApiProperty({ description: 'Final disposition node ID' })
  final_disposition_node_id: number;

  @ApiProperty({ description: 'Whether comments are required by default' })
  default_required_comments: boolean;

  @ApiProperty({
    description: 'Whether next follow-up date is required by default',
  })
  default_required_next_follow_up_date: boolean;

  @ApiProperty({
    description: 'Default auto follow-up logic',
    enum: AutoFollowUpLogic,
  })
  default_auto_follow_up_logic: AutoFollowUpLogic;
}

export class ProgramUpdateDto {
  @ApiProperty({ description: 'Program interest ID' })
  @IsInt()
  @IsNotEmpty()
  prog_int_id: number;

  @ApiPropertyOptional({ description: 'New level ID (optional)' })
  @IsOptional()
  @IsInt()
  new_level_id?: number;

  @ApiPropertyOptional({
    description: 'Comments for this program level change',
  })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class ApplyDispositionDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsInt()
  lead_id: number;

  @ApiProperty({ description: 'ID of the final disposition node' })
  @IsInt()
  final_disposition_node_id: number;

  @ApiPropertyOptional({ description: 'Comments for the disposition' })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({ description: 'Next follow-up date' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  next_follow_up_date?: Date;

  @ApiProperty({ description: 'Call log Id' })
  @IsInt()
  @IsOptional()
  call_log_id?: number;

  @ApiProperty({
    description: 'Program updates with level changes and comments',
    type: [ProgramUpdateDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProgramUpdateDto)
  program_updates: ProgramUpdateDto[];
}

export class HandleGmApprovalDto {
  @ApiProperty({ description: 'Whether to approve the disposition' })
  @IsBoolean()
  approve: boolean;

  @ApiPropertyOptional({
    description: 'Comments regarding approval or rejection',
  })
  @IsOptional()
  @IsString()
  comments?: string;
}

export class CreateDispositionDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsInt()
  lead_id: number;

  @ApiProperty({ description: 'ID of the final disposition node' })
  @IsInt()
  final_disposition_node_id: number;

  @ApiPropertyOptional({ description: 'Comments for the disposition' })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiProperty({ description: 'Call log Id' })
  @IsInt()
  @IsOptional()
  call_log_id?: number;
}

export class UpdateDispositionDto {
  @ApiPropertyOptional({ description: 'Comments for the disposition' })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({ description: 'Next follow-up date' })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  next_follow_up_date?: Date;

  @ApiProperty({ description: 'Call log Id' })
  @IsInt()
  @IsOptional()
  call_log_id?: number;

  @ApiProperty({
    description: 'Program updates with level changes and comments',
    type: [ProgramUpdateDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProgramUpdateDto)
  program_updates?: ProgramUpdateDto[];

  @ApiPropertyOptional({
    description: 'New program interests to create during disposition',
    type: [CreateProgramInterestDuringDispositionDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateProgramInterestDuringDispositionDto)
  new_program_interests?: CreateProgramInterestDuringDispositionDto[];
}
