import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsObject,
} from 'class-validator';

export class CreateProgramInterestDuringDispositionDto {
  @ApiProperty({ description: 'ID of the program' })
  @IsNotEmpty()
  @IsNumber()
  program_id: number;

  @ApiProperty({ description: 'Initial level ID for the new program interest' })
  @IsNotEmpty()
  @IsNumber()
  initial_level_id: number;

  @ApiPropertyOptional({
    description: 'Lead source ID (optional, defaults to 1)',
  })
  @IsOptional()
  @IsNumber()
  lead_source_id?: number;

  @ApiPropertyOptional({ description: 'Comments for the new program interest' })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata for the program interest',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
