import {
  Injectable,
  Logger,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { DispositionHistory } from '../entities/disposition-history.entity';
import { ApprovalStatus } from '../enums/disposition-history.enum';
import {
  calculateFollowUpDate,
  validateInputsAgainstRulesAndConfig,
} from '../utils/disposition.utils';
import { processAndValidateProgramUpdates } from '../utils/program-interest.utils';
import { DispositionDataService } from './disposition-data.service';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DispositionNode } from '../entities/disposition-node.entity';
import { Repository } from 'typeorm';
import { LeadLevelService } from '../../leads/services/lead-level.service';
import { LeadProgramInterestService } from '../../leads/services/lead-program-interest.service';
import { LeadProgramInterest } from '../../leads/entities/lead-program-interest.entity';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import {
  ApplyDispositionInput,
  CreateDispositionInput,
  UpdateDispositionInput,
} from '../types/disposition';

@Injectable()
export class DispositionService {
  private readonly logger = new Logger(DispositionService.name);

  constructor(
    @InjectRepository(DispositionNode)
    private readonly dispostionNode: Repository<DispositionNode>,
    private readonly dataService: DispositionDataService,
    private readonly callLogService: CallLogService,
    private readonly leadLevelService: LeadLevelService,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly leadHistoryService: LeadHistoryService,
  ) {}

  /**
   * Create a disposition entry without processing level changes
   * If a disposition already exists for the same lead_id and call_log_id,
   * it will update the disposition node instead of creating a new one
   */
  async createDisposition(
    spoc_user_id: number,
    inputData: CreateDispositionInput,
  ): Promise<DispositionHistory> {
    try {
      // --- Step 1: Load and validate initial data ---
      const { lead, chosenFinalNode } = await this.loadInitialData(
        inputData.lead_id,
        inputData.final_disposition_node_id,
        spoc_user_id,
      );

      // --- Step 2: Validate call log ID if provided ---
      if (inputData.call_log_id) {
        await this.callLogService.validateCallLog(
          inputData.call_log_id,
          inputData.lead_id,
          spoc_user_id,
        );

        // --- Step 3: Check if disposition already exists for this lead and call log ---
        const existingDisposition =
          await this.dataService.findDispositionByLeadAndCallLog(
            inputData.lead_id,
            inputData.call_log_id,
          );

        if (existingDisposition) {
          // Validate that the existing disposition belongs to the same user
          if (existingDisposition.spoc_user_id !== spoc_user_id) {
            throw new BadRequestException(
              'Cannot update disposition created by another user',
            );
          }

          this.logger.log(
            `Found existing disposition ${existingDisposition.id} for lead ${inputData.lead_id} and call ${inputData.call_log_id}. Updating disposition node.`,
          );

          // Update the existing disposition with new node and comments
          existingDisposition.final_disposition_node_id =
            inputData.final_disposition_node_id;
          existingDisposition.comments =
            inputData.comments ?? existingDisposition.comments;
          existingDisposition.event_timestamp = new Date(); // Update timestamp

          const updatedDisposition =
            await this.dataService.saveDispositionHistory(existingDisposition);

          this.logger.log(
            `Successfully updated disposition ${updatedDisposition.id} with new node ${inputData.final_disposition_node_id}`,
          );
          this.updateLeadHistory(inputData.call_log_id, updatedDisposition);
          return updatedDisposition;
        }
      }

      // --- Step 4: Create new disposition if none exists ---
      const dispositionInput = {
        comments: inputData.comments,
        next_follow_up_date: undefined, // Will be set during applyDisposition
        call_log_id: inputData.call_log_id,
        program_updates: [], // Will be updated during applyDisposition
      };

      const savedHistory = await this.createDispositionHistory(
        lead,
        spoc_user_id,
        chosenFinalNode,
        dispositionInput,
        null, // next_follow_up_date will be calculated during applyDisposition
      );

      this.logger.log(
        `Created new disposition ${savedHistory.id} for lead ${inputData.lead_id}`,
      );

      return savedHistory;
    } catch (error) {
      this.logger.error(
        `Error creating disposition: ${error.message}`,
        error.stack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'An error occurred while creating disposition',
      );
    }
  }

  /**
   * Apply/update an existing disposition with program-specific level changes
   */
  async applyDisposition(
    disposition_id: number,
    spoc_user_id: number,
    inputData: UpdateDispositionInput,
  ): Promise<DispositionHistory> {
    try {
      // --- Step 1: Load and validate existing disposition ---
      const existingDisposition = await this.loadAndValidateExistingDisposition(
        disposition_id,
        spoc_user_id,
        inputData.call_log_id,
      );

      // --- Step 2: Load lead and initial data ---
      const { lead, client_id } = await this.loadInitialData(
        existingDisposition.lead_id,
        existingDisposition.final_disposition_node_id,
        spoc_user_id,
      );

      // --- Step 3: Load disposition configuration and outcome rules ---
      const { dispositionConfig, outcomeRule } =
        await this.loadDispositionRulesAndConfig(
          client_id,
          existingDisposition.final_disposition_node_id,
          lead.program_interests,
        );

      // --- Step 4: Validate inputs against rules and configuration ---
      await this.validateInputsAgainstRules(
        inputData,
        dispositionConfig,
        outcomeRule,
      );

      // --- Step 5: Create new program interests if provided ---
      const newProgramInterests = await this.createNewProgramInterests(
        inputData.new_program_interests,
        lead,
        spoc_user_id,
        existingDisposition.id,
      );

      // --- Step 6: Process and validate program updates (including new ones) ---
      const { validatedUpdates, highestIntendedLevel } =
        await this.processAllProgramUpdates(
          inputData.program_updates,
          lead.program_interests,
          newProgramInterests,
        );

      // --- Step 7: Calculate follow-up date based on highest intended level ---
      const finalNextFollowUpDate =
        await this.calculateFollowUpDateForHighestLevel(
          inputData.next_follow_up_date,
          highestIntendedLevel,
          existingDisposition.final_disposition_node_id,
          client_id,
          lead,
        );

      // --- Step 8: Update and save disposition history ---
      const savedHistory = await this.updateAndSaveDispositionHistory(
        existingDisposition,
        inputData,
        finalNextFollowUpDate,
        newProgramInterests,
      );

      // --- Step 9: Apply level changes and create history ---
      await this.applyLevelChangesAndCreateHistory(
        validatedUpdates,
        savedHistory.id,
        spoc_user_id,
      );

      try {
        if (inputData.call_log_id) {
          await this.callLogService.markOtherCallsAsDisposed(
            existingDisposition.lead_id,
            inputData.call_log_id,
          );
          //Updating call lead history with disposition information and level updates
          this.updateLeadHistory(
            inputData.call_log_id,
            savedHistory,
            //commented out before only program updating is coming in validatedUpdates
            // validatedUpdates.map((update) => ({
            //   next: update.intendedLevel?.name,
            //   initial: update.programInterest.lead_level?.name,
            //   type: update.programInterest.lead_level?.temperature,
            //   program: update.programInterest.program?.name,
            //   comment: update.comments,
            // })),
          );
          this.logger.log(
            `Marked other non-disposed calls as disposed for lead ${existingDisposition.lead_id}, disposition applied to call log ${inputData.call_log_id}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error marking other calls as disposed: ${error.message}`,
          error.stack,
        );
      }

      // --- Step 10: Update lead's next follow-up date ---
      await this.updateLeadNextFollowUpDate(
        existingDisposition.lead_id,
        finalNextFollowUpDate,
      );

      return savedHistory;
    } catch (error) {
      this.logger.error(
        `Error applying disposition: ${error.message}`,
        error.stack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(
        'An error occurred while applying disposition',
      );
    }
  }

  /**
   * Load and validate existing disposition
   */
  private async loadAndValidateExistingDisposition(
    disposition_id: number,
    spoc_user_id: number,
    call_log_id?: number,
  ): Promise<DispositionHistory> {
    const existingDisposition =
      await this.dataService.getDispositionHistoryById(disposition_id);

    if (existingDisposition.spoc_user_id !== spoc_user_id) {
      throw new BadRequestException(
        'You can only update your own dispositions',
      );
    }

    // Handle call log validation and assignment
    if (call_log_id) {
      if (existingDisposition.call_log_id !== call_log_id) {
        throw new BadRequestException(
          'Cannot change call log ID once it has been set',
        );
      }

      await this.callLogService.validateCallLog(
        call_log_id,
        existingDisposition.lead_id,
        spoc_user_id,
      );

      existingDisposition.call_log_id = call_log_id;

      await this.callLogService.updateDisposition({
        dispositionId: existingDisposition.id,
        callLogId: call_log_id,
      });
    }

    return existingDisposition;
  }

  /**
   * Load disposition configuration and outcome rules
   */
  private async loadDispositionRulesAndConfig(
    client_id: number,
    final_disposition_node_id: number,
    programInterests: LeadProgramInterest[],
  ): Promise<{ dispositionConfig: any; outcomeRule: any }> {
    // Load disposition config
    const dispositionConfig = await this.dataService.findDispositionConfig(
      client_id,
      final_disposition_node_id,
    );

    // Find the highest current level to determine which outcome rule to use
    let highestCurrentLevel = null;
    for (const programInterest of programInterests) {
      const currentLevel = programInterest.lead_level;
      if (
        !highestCurrentLevel ||
        currentLevel.display_order > highestCurrentLevel.display_order
      ) {
        highestCurrentLevel = currentLevel;
      }
    }

    // Load outcome rule based on highest current level
    const outcomeRule = highestCurrentLevel
      ? await this.dataService.findOutcomeRule(
          client_id,
          highestCurrentLevel.id,
          final_disposition_node_id,
        )
      : null;

    return { dispositionConfig, outcomeRule };
  }

  /**
   * Validate inputs against rules and configuration
   */
  private async validateInputsAgainstRules(
    inputData: UpdateDispositionInput,
    dispositionConfig: any,
    outcomeRule: any,
  ): Promise<void> {
    const validation = validateInputsAgainstRulesAndConfig(
      inputData,
      outcomeRule,
      dispositionConfig,
    );

    if (!validation.isValid) {
      throw new BadRequestException(validation.error);
    }
  }

  /**
   * Create new program interests if provided
   */
  private async createNewProgramInterests(
    new_program_interests: any[] | undefined,
    lead: any,
    spoc_user_id: number,
    disposition_id: number,
  ): Promise<LeadProgramInterest[]> {
    const newProgramInterests = [];

    if (new_program_interests && new_program_interests.length > 0) {
      for (const newProgramData of new_program_interests) {
        const newProgramInterest =
          await this.leadProgramInterestService.createProgramInterestDuringDisposition(
            {
              lead_id: lead.id,
              client_id: lead.client_id,
              program_id: newProgramData.program_id,
              initial_level_id: newProgramData.initial_level_id,
              lead_source_id: newProgramData.lead_source_id,
              comments: newProgramData.comments,
              metadata: {
                ...newProgramData.metadata,
                created_via: 'disposition',
                created_during_disposition_id: disposition_id,
              },
            },
            spoc_user_id,
            disposition_id,
          );

        newProgramInterests.push(newProgramInterest);

        this.logger.log(
          `Created new program interest ${newProgramInterest.id} during disposition ${disposition_id}`,
        );
      }
    }

    return newProgramInterests;
  }

  /**
   * Process all program updates including newly created program interests
   */
  private async processAllProgramUpdates(
    program_updates: any[] | undefined,
    existingProgramInterests: LeadProgramInterest[],
    newProgramInterests: LeadProgramInterest[],
  ): Promise<{
    validatedUpdates: Array<{
      programInterest: LeadProgramInterest;
      newLevelId?: number;
      comments?: string;
      intendedLevel: any;
    }>;
    highestIntendedLevel: any;
  }> {
    return await processAndValidateProgramUpdates(
      program_updates || [],
      existingProgramInterests,
      newProgramInterests,
      this.leadLevelService,
    );
  }

  /**
   * Update and save disposition history
   */
  private async updateAndSaveDispositionHistory(
    existingDisposition: DispositionHistory,
    inputData: UpdateDispositionInput,
    finalNextFollowUpDate: Date | null,
    newProgramInterests: LeadProgramInterest[],
  ): Promise<DispositionHistory> {
    existingDisposition.comments =
      inputData.comments ?? existingDisposition.comments;
    existingDisposition.next_follow_up_date = finalNextFollowUpDate;
    existingDisposition.additional_data = {
      program_updates: inputData.program_updates || [],
      new_program_interests: inputData.new_program_interests
        ? {
            requests: inputData.new_program_interests,
            created_program_interest_ids: newProgramInterests.map(
              (pi) => pi.id,
            ),
          }
        : undefined,
    };

    return await this.dataService.saveDispositionHistory(existingDisposition);
  }

  /**
   * Apply level changes and create history
   */
  private async applyLevelChangesAndCreateHistory(
    validatedUpdates: any[],
    dispositionHistoryId: number,
    spocUserId: number,
  ): Promise<void> {
    if (validatedUpdates.length > 0) {
      await this.leadLevelService.applyLevelChangesAndCreateHistory({
        validatedUpdates,
        dispositionHistoryId,
        spocUserId,
      });
    }
  }

  /**
   * Update lead's next follow-up date
   */
  private async updateLeadNextFollowUpDate(
    lead_id: number,
    finalNextFollowUpDate: Date | null,
  ): Promise<void> {
    if (finalNextFollowUpDate) {
      await this.dataService.updateLeadNextFollowUpDate(
        lead_id,
        finalNextFollowUpDate,
      );
    }
  }

  /**
   * Load and validate initial data
   */
  private async loadInitialData(
    lead_id: number,
    final_disposition_node_id: number,
    spoc_user_id: number,
  ) {
    const lead = await this.dataService.loadLeadWithRelations(lead_id);
    const client_id = lead.client_id;

    await this.dataService.loadUserById(spoc_user_id);

    const chosenFinalNode = await this.dataService.loadDispositionNode(
      final_disposition_node_id,
      client_id,
    );

    return { lead, chosenFinalNode, client_id };
  }

  /**
   * Calculate follow-up date based on the highest intended level
   */
  private async calculateFollowUpDateForHighestLevel(
    inputFollowUpDate: Date | undefined,
    highestLevel: any,
    final_disposition_node_id: number,
    client_id: number,
    lead: any,
  ): Promise<Date | null> {
    // If no highest level found (no program interests), return input or null
    if (!highestLevel) {
      return inputFollowUpDate || null;
    }

    const eventTimestamp = new Date();
    // Validate that input follow-up date is not in the past
    if (
      inputFollowUpDate &&
      inputFollowUpDate < new Date(eventTimestamp.toDateString())
    ) {
      throw new BadRequestException('Follow-up date cannot be in the past');
    }

    // Load rules and config for the highest level
    const outcomeRule = await this.dataService.findOutcomeRule(
      client_id,
      highestLevel.id,
      final_disposition_node_id,
    );

    const defaultConfig = await this.dataService.findDispositionConfig(
      client_id,
      final_disposition_node_id,
    );

    return calculateFollowUpDate(
      inputFollowUpDate,
      outcomeRule,
      defaultConfig,
      highestLevel,
      lead,
      eventTimestamp,
    );
  }

  /**
   * Create disposition history entry
   */
  private async createDispositionHistory(
    lead: any,
    spoc_user_id: number,
    chosenFinalNode: any,
    inputData: ApplyDispositionInput,
    finalNextFollowUpDate: Date | null,
  ): Promise<DispositionHistory> {
    const callLogId = inputData.call_log_id ?? null;
    const historyEntry = new DispositionHistory();
    historyEntry.client_id = lead.client_id;
    historyEntry.lead_id = lead.id;
    historyEntry.spoc_user_id = spoc_user_id;
    historyEntry.event_timestamp = new Date();
    historyEntry.final_disposition_node_id = chosenFinalNode.id;
    historyEntry.comments = inputData.comments ?? null;
    historyEntry.next_follow_up_date = finalNextFollowUpDate;
    historyEntry.approval_status = ApprovalStatus.NOT_REQUIRED;
    historyEntry.additional_data = {
      program_updates: inputData.program_updates,
    };
    historyEntry.call_log_id = callLogId;

    const savedHistory =
      await this.dataService.saveDispositionHistory(historyEntry);

    if (callLogId) {
      //? As discussed not to mark this call disposed while disposition creation
      // await this.callLogService.updateDisposition({
      //   dispositionId: savedHistory.id,
      //   callLogId: callLogId,
      // });

      // try {
      //   // Mark all other non-disposed calls for the same lead as disposed
      //   if (lead?.id) {
      //     await this.callLogService.markOtherCallsAsDisposed(
      //       lead.id,
      //       callLogId,
      //     );
      //   }
      // } catch (error) {
      //   this.logger.error(
      //     `Error marking other calls as disposed: ${error.message}`,
      //     error.stack,
      //   );
      // }
      //Getting lead level history corresponding to dispostion history
      const leadLevelHistory =
        await this.leadLevelService.getHistoryByDisposition(savedHistory.id);
      let levels = [];
      if (leadLevelHistory.length > 0) {
        levels = leadLevelHistory.map((item) => ({
          next: item?.to_level?.name,
          initial: item?.from_level?.name,
          type: item?.lead_program_interest?.lead_level?.temperature,
          program: item?.lead_program_interest?.program?.name,
          comment: item?.comments,
        }));
      }
      //Updating lead history with disposition information
      this.updateLeadHistory(
        callLogId,
        savedHistory,
        levels?.map((level) => ({
          next: level?.next,
          initial: level?.initial,
          type: level.type,
          program: level?.program,
          comment: level?.comment,
        })) as any,
      );
    }

    return savedHistory;
  }

  async getDispositionNodes(
    parentId?: number,
    client_id?: number,
  ): Promise<{ data: DispositionNode[]; message: string }> {
    try {
      const query = this.dispostionNode
        .createQueryBuilder('node')
        .where('node.is_active = :isActive', { isActive: true });

      if (parentId) {
        query.andWhere('node.parent_id = :parentId', { parentId });
      } else {
        query.andWhere('node.parent_id IS NULL');
      }

      // Add client filter if provided
      if (client_id) {
        query.andWhere('node.client_id = :client_id', { client_id });
      }

      const nodes = await query.getMany();

      // Fetch all configs in a single query if client_id is provided
      const configsMap = new Map();
      if (client_id && nodes.length > 0) {
        const selectableNodeIds = nodes
          .filter((node) => node.is_selectable_outcome)
          .map((node) => node.id);

        if (selectableNodeIds.length > 0) {
          const configs = await this.dataService.findDispositionConfigsByNodes(
            client_id,
            selectableNodeIds,
          );

          // Create a map for quick lookup
          configs.forEach((config) => {
            configsMap.set(config.final_disposition_node_id, config);
          });
        }
      }

      // Attach config to each node
      const nodesWithConfig = nodes.map((node) => ({
        ...node,
        config: configsMap.get(node.id) || null,
      }));

      return {
        data: nodesWithConfig,
        message: 'Disposition nodes retrieved successfully',
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving disposition nodes: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'An error occurred while retrieving disposition nodes',
      );
    }
  }

  async createDispositionNode(nodeData) {
    try {
      if (!nodeData.display_text) {
        throw new BadRequestException('Display text is required');
      }

      const newNode = this.dispostionNode.create(nodeData);
      return await this.dispostionNode.save(newNode);
    } catch (error) {
      this.logger.error(
        `Error creating disposition node: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'An error occurred while creating disposition node',
      );
    }
  }

  async handleGmApproval(
    dispositionHistoryId: number,
    approverId: number,
    approve: boolean,
    comments?: string,
  ): Promise<DispositionHistory> {
    try {
      const dispositionHistory =
        await this.dataService.getDispositionHistoryById(dispositionHistoryId);

      dispositionHistory.approval_status = approve
        ? ApprovalStatus.APPROVED
        : ApprovalStatus.REJECTED;
      dispositionHistory.approver_id = approverId;
      dispositionHistory.approval_timestamp = new Date();
      dispositionHistory.approver_comments = comments || null;

      return this.dataService.saveDispositionHistory(dispositionHistory);
    } catch (error) {
      this.logger.error(
        `Error handling GM approval: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'An error occurred while handling GM approval',
      );
    }
  }

  //Updating calls lead history with disposition information
  private async updateLeadHistory(
    callId: number,
    disposition: DispositionHistory,
    levelChanges?: any[],
  ): Promise<void> {
    try {
      this.leadHistoryService.updateCallHistory(callId, {
        disposition,
        levelChanges,
      });
    } catch (error) {
      this.logger.error(
        `Error updating lead history for call ${callId}: ${error.message}`,
        error.stack,
      );
    }
  }
}
