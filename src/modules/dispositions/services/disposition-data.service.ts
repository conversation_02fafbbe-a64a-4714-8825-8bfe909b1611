import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { DispositionHistory } from '../entities/disposition-history.entity';
import { DispositionNode } from '../entities/disposition-node.entity';
import { ClientDispositionConfig } from '../entities/client-disposition-config.entity';
import { LevelDispositionOutcome } from '../entities/level-disposition-outcome.entity';
import { Lead } from '../../leads/entities/lead.entity';
import { LeadLevel } from '../../leads/entities/lead-level.entity';
import { User } from '../../users/entities/user.entity';
import { safeParseJson } from '../utils/disposition.utils';
import { LeadProgramInterest } from '../../leads/entities/lead-program-interest.entity';

/**
 * Service responsible for database operations related to dispositions
 * Provides data access methods for the Disposition workflow
 */
@Injectable()
export class DispositionDataService {
  private readonly logger = new Logger(DispositionDataService.name);

  constructor(
    @InjectRepository(DispositionHistory)
    private dispositionHistoryRepository: Repository<DispositionHistory>,
    @InjectRepository(DispositionNode)
    private dispositionNodeRepository: Repository<DispositionNode>,
    @InjectRepository(ClientDispositionConfig)
    private clientDispositionConfigRepository: Repository<ClientDispositionConfig>,
    @InjectRepository(LevelDispositionOutcome)
    private levelDispositionOutcomeRepository: Repository<LevelDispositionOutcome>,
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
    @InjectRepository(LeadLevel)
    private leadLevelRepository: Repository<LeadLevel>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(LeadProgramInterest)
    private leadProgramInterestRepository: Repository<LeadProgramInterest>,
  ) {}

  // ==============================
  // LEAD RELATED METHODS
  // ==============================

  /**
   * Load a lead with necessary relations
   * @param lead_id - ID of the lead to load
   * @returns Lead with loaded relations
   * @throws NotFoundException if lead not found
   */
  async loadLeadWithRelations(lead_id: number): Promise<Lead> {
    try {
      const lead = await this.leadRepository.findOne({
        where: { id: lead_id },
        relations: [
          'program_interests',
          'program_interests.lead_level',
          'program_interests.program',
        ],
      });

      if (!lead) {
        throw new NotFoundException(`Lead with ID ${lead_id} not found.`);
      }

      return lead;
    } catch (error) {
      this.logger.error(`Error loading lead: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update lead's last call date
   * @param lead_id - ID of the lead to update
   * @param lastCallDate - Date to set as the last call date
   * @returns Updated lead entity
   * @throws NotFoundException if lead not found
   */
  async updateLeadLastCallDate(
    lead_id: number,
    lastCallDate: Date,
  ): Promise<Lead> {
    try {
      const lead = await this.leadRepository.findOne({
        where: { id: lead_id },
      });

      if (!lead) {
        throw new NotFoundException(`Lead with ID ${lead_id} not found.`);
      }

      lead.last_call_date = lastCallDate;
      return this.leadRepository.save(lead);
    } catch (error) {
      this.logger.error(
        `Error updating lead last call date: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateLeadNextFollowUpDate(
    lead_id: number,
    nextFollowUpDate: Date,
  ): Promise<Lead> {
    try {
      const lead = await this.leadRepository.findOne({
        where: { id: lead_id },
      });

      if (!lead) {
        throw new NotFoundException(`Lead with ID ${lead_id} not found.`);
      }

      lead.next_followup_date = nextFollowUpDate;
      return this.leadRepository.save(lead);
    } catch (error) {
      this.logger.error(
        `Error updating lead next follow-up date: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get lead level by ID
   */
  async getLeadLevelById(levelId: number): Promise<LeadLevel> {
    const level = await this.leadLevelRepository.findOne({
      where: { id: levelId },
    });

    if (!level) {
      throw new NotFoundException(`Lead level with ID ${levelId} not found`);
    }

    return level;
  }

  // ==============================
  // LEAD PROGRAM INTEREST METHODS
  // ==============================

  /**
   * Load a lead program interest with necessary relations
   * @param lead_program_interest_id - ID of the lead program interest
   * @returns Lead program interest with loaded relations
   * @throws NotFoundException if lead program interest not found
   */
  async loadLeadProgramInterestWithRelations(
    lead_program_interest_id: number,
  ): Promise<LeadProgramInterest> {
    try {
      const leadProgramInterest =
        await this.leadProgramInterestRepository.findOne({
          where: { id: lead_program_interest_id },
          relations: ['lead', 'lead_level', 'program'],
        });

      if (!leadProgramInterest) {
        throw new NotFoundException(
          `Lead program interest with ID ${lead_program_interest_id} not found.`,
        );
      }

      return leadProgramInterest;
    } catch (error) {
      this.logger.error(
        `Error loading lead program interest: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // ==============================
  // LEVEL RELATED METHODS
  // ==============================

  /**
   * Get allowed transition methods for a level
   * @param level - Lead level entity
   * @returns Array of allowed transition methods
   */
  async getAllowedTransitionMethods(level: LeadLevel): Promise<string[]> {
    try {
      if (!level.allowed_transition_methods) {
        return [];
      }

      return safeParseJson<string[]>(
        level.allowed_transition_methods as unknown as string,
        [],
      );
    } catch (error) {
      this.logger.error(
        `Error getting allowed transition methods: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Check if a level transition is valid based on previous level requirements
   * @param lead_id - ID of the lead
   * @param intendedToLevelId - Target level ID
   * @param currentLevelId - Current level ID
   * @returns Boolean indicating if transition is valid
   */

  // ==============================
  // USER RELATED METHODS
  // ==============================

  /**
   * Load a user by ID
   * @param user_id - ID of the user to load
   * @returns User entity
   * @throws NotFoundException if user not found
   */
  async loadUserById(user_id: number): Promise<User> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: user_id },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${user_id} not found.`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error loading user: ${error.message}`, error.stack);
      throw error;
    }
  }

  // ==============================
  // DISPOSITION NODE METHODS
  // ==============================

  /**
   * Load disposition node with validation
   * @param node_id - ID of the disposition node
   * @param client_id - ID of the client
   * @returns Disposition node entity
   * @throws BadRequestException if node is invalid or not selectable
   */
  async loadDispositionNode(
    node_id: number,
    client_id: number,
  ): Promise<DispositionNode> {
    try {
      const node = await this.dispositionNodeRepository.findOne({
        where: {
          id: node_id,
          client_id: client_id,
          is_selectable_outcome: true,
        },
      });

      if (!node) {
        throw new BadRequestException(
          `Invalid or non-selectable disposition node with ID ${node_id} for client ${client_id}.`,
        );
      }

      return node;
    } catch (error) {
      this.logger.error(
        `Error loading disposition node: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // ==============================
  // DISPOSITION CONFIG METHODS
  // ==============================

  /**
   * Find disposition configuration for a client and node
   * @param client_id - ID of the client
   * @param final_disposition_node_id - ID of the disposition node
   * @returns Client disposition config or null if not found
   */
  async findDispositionConfig(
    client_id: number,
    final_disposition_node_id: number,
  ): Promise<ClientDispositionConfig | null> {
    try {
      return this.clientDispositionConfigRepository.findOne({
        where: {
          client_id: client_id,
          final_disposition_node_id: final_disposition_node_id,
        },
      });
    } catch (error) {
      this.logger.error(
        `Error finding disposition config: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Find disposition configurations for multiple nodes in a single query
   * @param client_id - ID of the client
   * @param node_ids - Array of disposition node IDs
   * @returns Array of client disposition configs
   */
  async findDispositionConfigsByNodes(
    client_id: number,
    node_ids: number[],
  ): Promise<ClientDispositionConfig[]> {
    try {
      if (!node_ids || node_ids.length === 0) {
        return [];
      }

      return this.clientDispositionConfigRepository.find({
        where: {
          client_id: client_id,
          final_disposition_node_id: In(node_ids),
        },
      });
    } catch (error) {
      this.logger.error(
        `Error finding disposition configs by nodes: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  // ==============================
  // DISPOSITION OUTCOME METHODS
  // ==============================

  /**
   * Find appropriate outcome rule for the given disposition
   * @param client_id - ID of the client
   * @param current_level_id - ID of the current level
   * @param final_disposition_node_id - ID of the disposition node
   * @returns Level disposition outcome or null if not found
   */
  async findOutcomeRule(
    client_id: number,
    current_level_id: number,
    final_disposition_node_id: number,
  ): Promise<LevelDispositionOutcome | null> {
    try {
      // First look for a rule specific to the current level
      let outcomeRule = await this.levelDispositionOutcomeRepository.findOne({
        where: {
          client_id: client_id,
          from_level_id: current_level_id,
          final_disposition_node_id: final_disposition_node_id,
        },
      });

      // If no level-specific rule, check for a universal rule (from_level_id IS NULL)
      if (!outcomeRule) {
        outcomeRule = await this.levelDispositionOutcomeRepository.findOne({
          where: {
            client_id: client_id,
            from_level_id: null,
            final_disposition_node_id: final_disposition_node_id,
          },
        });
      }

      return outcomeRule;
    } catch (error) {
      this.logger.error(
        `Error finding outcome rule: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Get level disposition outcome by ID
   * @param id - ID of the level disposition outcome
   * @returns Level disposition outcome or null if not found
   */
  async getLevelDispositionOutcomeById(
    id: number,
  ): Promise<LevelDispositionOutcome | null> {
    try {
      if (!id) return null;

      return this.levelDispositionOutcomeRepository.findOne({
        where: { id },
      });
    } catch (error) {
      this.logger.error(
        `Error getting level disposition outcome: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  // ==============================
  // DISPOSITION HISTORY METHODS
  // ==============================

  /**
   * Find existing disposition by lead_id and call_log_id
   * @param lead_id - ID of the lead
   * @param call_log_id - ID of the call log
   * @returns Disposition history entity or null if not found
   */
  async findDispositionByLeadAndCallLog(
    lead_id: number,
    call_log_id: number,
  ): Promise<DispositionHistory | null> {
    try {
      return await this.dispositionHistoryRepository.findOne({
        where: {
          lead_id: lead_id,
          call_log_id: call_log_id,
        },
      });
    } catch (error) {
      this.logger.error(
        `Error finding disposition by lead and call log: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Save disposition history entry
   * @param dispositionHistory - Disposition history entity to save
   * @returns Saved disposition history entity
   */
  async saveDispositionHistory(
    dispositionHistory: DispositionHistory,
  ): Promise<DispositionHistory> {
    try {
      return this.dispositionHistoryRepository.save(dispositionHistory);
    } catch (error) {
      this.logger.error(
        `Error saving disposition history: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get disposition history by ID
   * @param id - ID of the disposition history
   * @returns Disposition history entity
   * @throws NotFoundException if disposition history not found
   */
  async getDispositionHistoryById(id: number): Promise<DispositionHistory> {
    try {
      const disposition = await this.dispositionHistoryRepository.findOne({
        where: { id },
      });

      if (!disposition) {
        throw new NotFoundException(
          `Disposition history with ID ${id} not found.`,
        );
      }

      return disposition;
    } catch (error) {
      this.logger.error(
        `Error getting disposition history: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
