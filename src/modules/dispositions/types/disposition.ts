export interface ApplyDispositionInput {
  comments?: string;
  next_follow_up_date?: Date;
  call_log_id?: number;
  program_updates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>;
}

export interface CreateDispositionInput {
  lead_id: number;
  final_disposition_node_id: number;
  comments?: string;
  call_log_id?: number;
}

export interface UpdateDispositionInput {
  comments?: string;
  next_follow_up_date?: Date;
  call_log_id?: number;
  program_updates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>;
  new_program_interests?: Array<{
    program_id: number;
    initial_level_id: number;
    lead_source_id?: number;
    comments?: string;
    metadata?: Record<string, any>;
  }>;
}
