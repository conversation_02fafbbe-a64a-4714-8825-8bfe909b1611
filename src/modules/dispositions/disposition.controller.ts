import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import {
  <PERSON>,
  Post,
  Body,
  Param,
  UseGuards,
  Req,
  BadRequestException,
  ParseIntPipe,
  Get,
  Query,
  Put,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { DispositionService } from './services/disposition.service';
import { DispositionHistory } from './entities/disposition-history.entity';
import { Action, Resource } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import {
  HandleGmApprovalDto,
  CreateDispositionDto,
  UpdateDispositionDto,
} from './dto/client-disposition-config.dto';
import { DispositionNode } from './entities/disposition-node.entity';

@ApiTags('dispositions')
@Controller('dispositions')
@UseGuards(PermissionGuard)
export class DispositionController {
  constructor(private readonly dispositionService: DispositionService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a disposition entry' })
  @ApiResponse({
    status: 201,
    description: 'Disposition created successfully',
    type: DispositionHistory,
  })
  async createDisposition(
    @Body() createDto: CreateDispositionDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<DispositionHistory> {
    if (!req.user?.id) {
      throw new BadRequestException('User not authenticated properly');
    }

    return this.dispositionService.createDisposition(req.user.id, {
      lead_id: createDto.lead_id,
      final_disposition_node_id: createDto.final_disposition_node_id,
      comments: createDto.comments,
      call_log_id: createDto.call_log_id,
    });
  }

  @Put(':id/apply')
  @ApiOperation({ summary: 'Apply/update disposition with level changes' })
  @ApiResponse({
    status: 200,
    description: 'Disposition applied successfully',
    type: DispositionHistory,
  })
  async updateDisposition(
    @Param('id') id: number,
    @Body() updateDto: UpdateDispositionDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<DispositionHistory> {
    if (!req.user?.id) {
      throw new BadRequestException('User not authenticated properly');
    }

    return this.dispositionService.applyDisposition(id, req.user?.id, {
      comments: updateDto.comments,
      next_follow_up_date: updateDto.next_follow_up_date,
      call_log_id: updateDto.call_log_id,
      program_updates: updateDto.program_updates,
      new_program_interests: updateDto.new_program_interests,
    });
  }

  //! Not being used in the client, but kept for backward compatibility
  // @Post('apply-legacy')
  // @ApiOperation({
  //   summary: 'Apply disposition (legacy - creates and applies in one step)',
  // })
  // @ApiBody({ type: ApplyDispositionDto })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Disposition applied successfully',
  //   type: DispositionHistory,
  // })
  // @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  // async applyDisposition(
  //   @Body() applyDto: ApplyDispositionDto,
  //   @Req() req: AuthenticatedRequest,
  // ): Promise<DispositionHistory> {
  //   if (!req.user?.id) {
  //     throw new BadRequestException('User not authenticated properly');
  //   }

  //   // First create the disposition
  //   const createdDisposition = await this.dispositionService.createDisposition(
  //     req.user.id,
  //     {
  //       lead_id: applyDto.lead_id,
  //       final_disposition_node_id: applyDto.final_disposition_node_id,
  //       comments: applyDto.comments,
  //       call_log_id: applyDto.call_log_id,
  //     },
  //   );

  //   // Then apply the level changes
  //   return this.dispositionService.applyDisposition(
  //     createdDisposition.id,
  //     req.user.id,
  //     {
  //       comments: applyDto.comments,
  //       next_follow_up_date: applyDto.next_follow_up_date,
  //       call_log_id: applyDto.call_log_id,
  //       program_updates: applyDto.program_updates,
  //     },
  //   );
  // }

  @Get()
  @ApiOperation({ summary: 'Get disposition nodes by parent ID' })
  @ApiQuery({
    name: 'parentId',
    required: false,
    description: 'Parent disposition node ID',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Disposition nodes retrieved successfully',
    type: [DispositionNode],
  })
  async getDispositionNodes(
    @Req() req: AuthenticatedRequest,
    @Query('parentId') parentId?: number,
  ): Promise<{
    data: DispositionNode[];
  }> {
    const clientId = req.user?.currentClientId || 1;
    return this.dispositionService.getDispositionNodes(parentId, clientId);
  }

  @Post('approval/:id')
  @ApiOperation({ summary: 'Handle GM approval for a pending disposition' })
  @ApiParam({ name: 'id', description: 'Disposition history ID', type: Number })
  @ApiBody({ type: HandleGmApprovalDto })
  @ApiResponse({
    status: 200,
    description: 'GM approval handled successfully',
    type: DispositionHistory,
  })
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  async handleGmApproval(
    @Param('id', ParseIntPipe) id: number,
    @Body() approvalDto: HandleGmApprovalDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<DispositionHistory> {
    if (!req.user?.id) {
      throw new BadRequestException('User not authenticated properly');
    }

    return this.dispositionService.handleGmApproval(
      id,
      req.user.id, // Using the authenticated user as the approver
      approvalDto.approve,
      approvalDto.comments,
    );
  }
}
