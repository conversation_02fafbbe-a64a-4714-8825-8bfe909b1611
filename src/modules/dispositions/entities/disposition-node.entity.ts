import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  OneToMany,
  Unique,
} from 'typeorm';
import { ClientAwareEntity } from '../../../common/entities/client-aware.entity';
import { Level1Group } from '../enums/disposition.enum';

@Entity()
@Unique('unique_client_parent_text', ['client_id', 'parent_id', 'display_text'])
export class DispositionNode extends ClientAwareEntity {
  @Column({ name: 'parent_id', nullable: true })
  parent_id: number | null;

  @ManyToOne(() => DispositionNode, (node) => node.children)
  @JoinColumn({ name: 'parent_id' })
  parent: DispositionNode;

  @Column({ name: 'display_text', length: 100 })
  display_text: string;

  @Column({ name: 'display_order', default: 0 })
  display_order: number;

  @Column({ name: 'level_order' })
  level_order: number;

  @Column({ name: 'is_selectable_outcome', default: false })
  is_selectable_outcome: boolean;

  @Column({
    name: 'level1_group',
    type: 'enum',
    enum: Level1Group,
    nullable: true,
  })
  level1_group: Level1Group | null;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string | null;

  @OneToMany(() => DispositionNode, (node) => node.parent)
  children: DispositionNode[];
}
