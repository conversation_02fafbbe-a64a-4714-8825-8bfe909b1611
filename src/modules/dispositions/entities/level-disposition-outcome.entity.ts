import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { ClientAwareEntity } from '../../../common/entities/client-aware.entity';
import { DispositionNode } from './disposition-node.entity';
import { OutcomeAutoFollowUpLogic } from '../enums/disposition.enum';

@Entity()
export class LevelDispositionOutcome extends ClientAwareEntity {
  @Column({ name: 'from_level_id', nullable: true })
  from_level_id: number | null;

  @Column({ name: 'final_disposition_node_id' })
  final_disposition_node_id: number;

  @Column({ name: 'to_level_id', nullable: true })
  to_level_id: number | null;

  //!Not giving this mode now
  // @Column({ name: 'is_auto_transition', default: false })
  // is_auto_transition: boolean;

  @Column({ name: 'requires_approval_for_transition', default: false })
  requires_approval_for_transition: boolean;

  @Column({
    name: 'auto_follow_up_logic',
    type: 'enum',
    enum: OutcomeAutoFollowUpLogic,
    default: OutcomeAutoFollowUpLogic.FromLevelDefault,
  })
  auto_follow_up_logic: OutcomeAutoFollowUpLogic;

  // If auto_follow_up_logic is 'SpecificField', the name of the lead field to use
  @Column({ name: 'auto_follow_up_specific_field', nullable: true, length: 50 })
  auto_follow_up_specific_field: string | null;

  // JSON array of field names (or structure) required from the SPOC for this outcome (e.g., parent details)
  @Column({ name: 'required_data_fields_json', type: 'json', nullable: true })
  required_data_fields_json: any | null;

  @Column({ name: 'rule_notes', type: 'text', nullable: true })
  rule_notes: string | null;

  @Column({ name: 'additional_rules_json', type: 'json', nullable: true })
  additional_rules_json: any | null;

  @ManyToOne(() => DispositionNode)
  @JoinColumn({ name: 'final_disposition_node_id' })
  final_disposition_node: DispositionNode;
}
