import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { ClientAwareEntity } from '../../../common/entities/client-aware.entity';
import { DispositionNode } from './disposition-node.entity';
import { User } from '../../users/entities/user.entity';
import { ApprovalStatus } from '../enums/disposition-history.enum';
import { Lead } from '@modules/leads/entities/lead.entity';
import { CallLog } from '@modules/call-logs/entities/call-log.entity';

/**
 * Entity to track disposition history for leads
 * Stores the disposition history including which disposition was selected,
 * when it was set, by whom, and associated metadata like comments.
 */
@Entity()
export class DispositionHistory extends ClientAwareEntity {
  @Column({ name: 'lead_id' })
  lead_id: number;

  @Column({ name: 'spoc_user_id', nullable: true })
  spoc_user_id: number | null;

  @Column({ name: 'call_log_id', nullable: true })
  call_log_id: number | null;

  @Column({ name: 'event_timestamp', default: () => 'CURRENT_TIMESTAMP' })
  event_timestamp: Date;

  @Column({ name: 'final_disposition_node_id', nullable: true })
  final_disposition_node_id: number | null;

  @Column({ name: 'comments', type: 'text', nullable: true })
  comments: string | null;

  @Column({ name: 'next_follow_up_date', nullable: true })
  next_follow_up_date: Date | null;

  @Column({
    name: 'gm_approval_status',
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.NOT_REQUIRED,
  })
  approval_status: ApprovalStatus;

  @Column({ name: 'approver_id', nullable: true })
  approver_id: number | null;

  @Column({ name: 'approval_timestamp', nullable: true })
  approval_timestamp: Date | null;

  @Column({ name: 'approver_comments', type: 'text', nullable: true })
  approver_comments: string | null;

  @Column({ name: 'additional_data', type: 'json', nullable: true })
  additional_data: any | null;

  @Column({ name: 'level_disposition_outcome_id', nullable: true })
  level_disposition_outcome_id: number | null;

  // Relations
  @ManyToOne(() => DispositionNode)
  @JoinColumn({ name: 'final_disposition_node_id' })
  final_disposition_node: DispositionNode;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'spoc_user_id' })
  spoc_user: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'approver_id' })
  approver_user: User;

  @ManyToOne(() => Lead)
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @OneToOne(() => CallLog, (callLog) => callLog.disposition_history, {
    nullable: true,
  })
  @JoinColumn({ name: 'call_log_id' })
  call_log: CallLog;
}
