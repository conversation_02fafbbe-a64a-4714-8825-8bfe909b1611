import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, Unique } from 'typeorm';
import { ClientAwareEntity } from '../../../common/entities/client-aware.entity';
import { DispositionNode } from './disposition-node.entity';
import { AutoFollowUpLogic } from '../enums/disposition.enum';

@Entity()
@Unique('unique_client_final_config', [
  'client_id',
  'final_disposition_node_id',
])
export class ClientDispositionConfig extends ClientAwareEntity {
  @Column({ name: 'final_disposition_node_id' })
  final_disposition_node_id: number;

  @Column({ name: 'default_required_comments', default: false })
  default_required_comments: boolean;

  @Column({ name: 'default_required_next_follow_up_date', default: false })
  default_required_next_follow_up_date: boolean;

  @Column({
    name: 'default_auto_follow_up_logic',
    type: 'enum',
    enum: AutoFollowUpLogic,
    default: AutoFollowUpLogic.FromLevel<PERSON><PERSON><PERSON>,
  })
  default_auto_follow_up_logic: AutoFollowUpLogic;

  @Column({ name: 'can_spoc_update_fud', default: false })
  can_spoc_update_fud: boolean;

  @ManyToOne(() => DispositionNode)
  @JoinColumn({ name: 'final_disposition_node_id' })
  final_disposition_node: DispositionNode;
}
