import { DurationUnit } from '@modules/leads/enums/lead-level.enum';
import {
  AutoFollowUpLogic,
  OutcomeAutoFollowUpLogic,
} from '../enums/disposition.enum';
import { UpdateDispositionInput } from '../types/disposition';

/**
 * Validate disposition inputs against requirements
 */
export function validateDispositionInputs(
  inputData: UpdateDispositionInput,
  requiredComments: boolean,
  requiredNextFollowUpDate: boolean,
): { isValid: boolean; error?: string } {
  if (requiredComments) {
    const validNewProgInterests =
      inputData.new_program_interests &&
      inputData.new_program_interests.length > 0;

    const validProgramUpdates =
      inputData.program_updates && inputData.program_updates.length > 0;

    // At least one of the program interest arrays must exist
    if (!validNewProgInterests && !validProgramUpdates) {
      return {
        isValid: false,
        error:
          'Either new program interests or program updates are required for this disposition.',
      };
    }

    // If new program interests exist, validate comments
    if (validNewProgInterests) {
      const newHasComments = inputData.new_program_interests.every(
        (item: any) => item.comments && item.comments.trim() !== '',
      );
      if (!newHasComments) {
        return {
          isValid: false,
          error:
            'Comments are required for all new program interests in this disposition.',
        };
      }
    }

    // If program updates exist, validate comments
    if (validProgramUpdates) {
      const hasComments = inputData.program_updates.every(
        (item: any) => item.comments && item.comments.trim() !== '',
      );
      if (!hasComments) {
        return {
          isValid: false,
          error:
            'Comments are required for all program updates in this disposition.',
        };
      }
    }
  }

  if (requiredNextFollowUpDate && !inputData.next_follow_up_date) {
    return {
      isValid: false,
      error: 'Next Follow-up Date is required for this disposition.',
    };
  }

  return { isValid: true };
}

/**
 * Validate disposition inputs against requirements from both outcome rule and disposition config
 */
export function validateInputsAgainstRulesAndConfig(
  inputData: UpdateDispositionInput,
  outcomeRule: any,
  dispositionConfig: any,
): { isValid: boolean; error?: string } {
  // Determine if comments are required
  const requiredComments =
    outcomeRule?.required_data_fields_json?.includes('comments') ||
    dispositionConfig?.default_required_comments ||
    false;

  // Determine if next follow-up date is required
  const requiredNextFollowUpDate =
    outcomeRule?.required_data_fields_json?.includes('next_follow_up_date') ||
    dispositionConfig?.default_required_next_follow_up_date ||
    false;

  // Get additional required fields from outcome rule
  // const additionalRequiredFields = outcomeRule?.required_data_fields_json || [];

  // Validate using the existing validation function
  return validateDispositionInputs(
    inputData,
    requiredComments,
    requiredNextFollowUpDate,
  );
}

/**
 * Map client configuration follow-up logic to outcome follow-up logic
 */
export function mapConfigFollowUpLogicToOutcome(
  configLogic: AutoFollowUpLogic,
): OutcomeAutoFollowUpLogic {
  switch (configLogic) {
    case AutoFollowUpLogic.Hours24:
      return OutcomeAutoFollowUpLogic.Hours24;
    case AutoFollowUpLogic.Months6:
      return OutcomeAutoFollowUpLogic.Months6;
    case AutoFollowUpLogic.ChangeableBySpocOnly:
      return OutcomeAutoFollowUpLogic.ChangeableBySpocOnly;
    case AutoFollowUpLogic.FromLevelDefault:
      return OutcomeAutoFollowUpLogic.FromLevelDefault;
    default:
      return OutcomeAutoFollowUpLogic.None;
  }
}

/**
 * Determine the effective follow-up logic based on rules and config
 */
export function determineEffectiveFollowUpLogic(
  outcomeRule: { auto_follow_up_logic: OutcomeAutoFollowUpLogic } | null,
  defaultConfig: { default_auto_follow_up_logic: AutoFollowUpLogic } | null,
): OutcomeAutoFollowUpLogic {
  // If no outcome rule exists, check default config
  if (!outcomeRule) {
    if (defaultConfig) {
      return mapConfigFollowUpLogicToOutcome(
        defaultConfig.default_auto_follow_up_logic,
      );
    }
    return OutcomeAutoFollowUpLogic.None;
  }

  // If outcome rule exists but uses config-based logic
  if (
    outcomeRule.auto_follow_up_logic !==
      OutcomeAutoFollowUpLogic.FromDispositionConfig &&
    outcomeRule.auto_follow_up_logic !==
      OutcomeAutoFollowUpLogic.FromLevelDefault
  ) {
    return outcomeRule.auto_follow_up_logic;
  }

  if (
    outcomeRule.auto_follow_up_logic ===
      OutcomeAutoFollowUpLogic.FromDispositionConfig &&
    defaultConfig
  ) {
    return mapConfigFollowUpLogicToOutcome(
      defaultConfig.default_auto_follow_up_logic,
    );
  }

  return OutcomeAutoFollowUpLogic.None;
}

/**
 * Calculate date from duration unit and value
 */
export function calculateDateFromDuration(
  baseDate: Date,
  durationUnit: string | null,
  durationValue: number | null,
): Date | null {
  if (durationUnit === null || durationValue === null) {
    return null;
  }

  const result = new Date(baseDate);

  switch (durationUnit) {
    case DurationUnit.Hours:
      result.setHours(result.getHours() + durationValue);
      break;
    case DurationUnit.Days:
      result.setDate(result.getDate() + durationValue);
      break;
    case DurationUnit.Months:
      result.setMonth(result.getMonth() + durationValue);
      break;
    default:
      return null;
  }

  return result;
}

/**
 * Calculate maximum follow-up date based on level constraints
 */
export function calculateMaxFudDate(
  baseDate: Date,
  durationUnit: string | null,
  durationValue: number | null,
): Date | null {
  return calculateDateFromDuration(baseDate, durationUnit, durationValue);
}

/**
 * Check if a follow-up date is valid against max constraints
 * and adjust if needed
 */
export function validateAndAdjustFollowUpDate(
  followUpDate: Date | null,
  maxConstraints: {
    eventTimestamp: Date;
    maxDurationUnit: string | null;
    maxDurationValue: number | null;
  },
): Date | null {
  if (!followUpDate) {
    return null;
  }

  const { eventTimestamp, maxDurationUnit, maxDurationValue } = maxConstraints;

  if (maxDurationUnit === null || maxDurationValue === null) {
    return followUpDate;
  }

  const maxDate = calculateMaxFudDate(
    eventTimestamp,
    maxDurationUnit,
    maxDurationValue,
  );

  if (maxDate && followUpDate > maxDate) {
    return maxDate;
  }

  return followUpDate;
}

/**
 * Calculate the follow-up date based on rules, configuration, and lead level
 */
export function calculateFollowUpDate(
  inputFollowUpDate: Date | undefined,
  outcomeRule: any,
  defaultConfig: any,
  currentLevel: any,
  lead: any,
  eventTimestamp: Date,
): Date | null {
  // Determine the effective follow-up date logic
  const effectiveFudLogic = determineEffectiveFollowUpLogic(
    outcomeRule,
    defaultConfig,
  );

  // Calculate default follow-up date based on effective logic
  const calculatedDefaultFud = calculateDefaultFud(
    effectiveFudLogic,
    outcomeRule,
    lead,
    currentLevel,
    eventTimestamp,
  );

  // Determine if SPOC can override FUD
  const spocCanOverrideFud =
    outcomeRule?.auto_follow_up_logic ===
      OutcomeAutoFollowUpLogic.ChangeableBySpocOnly ||
    defaultConfig?.can_spoc_update_fud ===
      currentLevel.default_fud_changeable_by_spoc;

  // Determine outcome dictates FUD input
  const outcomeDictatesFudInput =
    outcomeRule &&
    [
      OutcomeAutoFollowUpLogic.ChangeableBySpocOnly,
      OutcomeAutoFollowUpLogic.SpecificField,
    ].includes(outcomeRule.auto_follow_up_logic);

  // Determine final follow-up date
  let finalNextFollowUpDate: Date | null;
  if (spocCanOverrideFud && inputFollowUpDate) {
    finalNextFollowUpDate = inputFollowUpDate;
  } else if (outcomeDictatesFudInput) {
    finalNextFollowUpDate = inputFollowUpDate ?? calculatedDefaultFud;
  } else if (effectiveFudLogic !== OutcomeAutoFollowUpLogic.None) {
    finalNextFollowUpDate = calculatedDefaultFud;
  } else {
    finalNextFollowUpDate = inputFollowUpDate ?? null;
  }

  // Validate against max constraint
  return validateAndAdjustFollowUpDate(finalNextFollowUpDate, {
    eventTimestamp,
    maxDurationUnit: currentLevel.max_fud_duration_unit,
    maxDurationValue: currentLevel.max_fud_duration_value,
  });
}

/**
 * Calculate default follow-up date based on effective logic
 */
export function calculateDefaultFud(
  effectiveFudLogic: OutcomeAutoFollowUpLogic,
  outcomeRule: any,
  lead: any,
  currentLevel: any,
  eventTimestamp: Date,
): Date | null {
  let calculatedDefaultFud: Date | null = null;

  switch (effectiveFudLogic) {
    case OutcomeAutoFollowUpLogic.Hours24:
      calculatedDefaultFud = new Date(
        eventTimestamp.getTime() + 24 * 60 * 60 * 1000,
      );
      break;
    case OutcomeAutoFollowUpLogic.Months6:
      calculatedDefaultFud = new Date(eventTimestamp);
      calculatedDefaultFud.setMonth(calculatedDefaultFud.getMonth() + 6);
      break;
    case OutcomeAutoFollowUpLogic.SpecificField:
      if (
        outcomeRule?.auto_follow_up_specific_field &&
        lead[outcomeRule.auto_follow_up_specific_field] instanceof Date
      ) {
        calculatedDefaultFud = lead[outcomeRule.auto_follow_up_specific_field];
      }
      break;
  }

  // Handle Level Default FUD if applicable and not overridden
  if (
    !calculatedDefaultFud &&
    effectiveFudLogic === OutcomeAutoFollowUpLogic.FromLevelDefault
  ) {
    calculatedDefaultFud = calculateDateFromDuration(
      eventTimestamp,
      currentLevel.default_fud_duration_unit,
      currentLevel.default_fud_duration_value,
    );
  }

  return calculatedDefaultFud;
}

/**
 * Parse JSON safely with error handling
 */
export function safeParseJson<T>(
  jsonString: string | null | undefined,
  defaultValue: T,
): T {
  if (!jsonString) {
    return defaultValue;
  }

  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error(`Error parsing JSON: ${error}`);
    return defaultValue;
  }
}
