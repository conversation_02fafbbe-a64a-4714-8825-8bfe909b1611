import { BadRequestException, Logger } from '@nestjs/common';
import { LeadProgramInterest } from '../../leads/entities/lead-program-interest.entity';
import { LeadLevel } from '../../leads/entities/lead-level.entity';
import { AllowedTransitionMethodEnum } from '../../leads/enums/lead-level.enum';

export interface ValidatedProgramUpdate {
  programInterest: LeadProgramInterest;
  newLevelId?: number;
  comments?: string;
  intendedLevel: LeadLevel;
}

const logger = new Logger('ProgramInterestUtils');

/**
 * Validates program updates and determines the highest intended level
 */
export async function validateAndProcessProgramUpdates(
  programUpdates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>,
  leadProgramInterests: LeadProgramInterest[],
  leadLevelService: any,
): Promise<{
  validatedUpdates: ValidatedProgramUpdate[];
  highestIntendedLevel: LeadLevel | null;
}> {
  const validatedUpdates: ValidatedProgramUpdate[] = [];
  let highestIntendedLevel: LeadLevel | null = null;

  for (const update of programUpdates) {
    const programInterest = leadProgramInterests.find(
      (pi) => pi.id === update.prog_int_id,
    );

    if (!programInterest) {
      throw new BadRequestException(
        `Program interest with ID ${update.prog_int_id} not found`,
      );
    }

    // Start with current level as intended level
    let intendedLevel = programInterest.lead_level;

    // If level change is requested, validate it and set as intended level
    if (update.new_level_id) {
      const isValidTransition = await leadLevelService.isTransitionValid(
        update.new_level_id,
        programInterest.lead_level_id,
        AllowedTransitionMethodEnum.Manual,
      );

      if (!isValidTransition) {
        throw new BadRequestException(
          `Invalid level transition from ${programInterest.lead_level_id} to ${update.new_level_id} for program interest ${update.prog_int_id}`,
        );
      }

      // Load the new intended level
      intendedLevel = await leadLevelService.findById(update.new_level_id);
      if (!intendedLevel) {
        throw new BadRequestException(
          `Lead level with ID ${update.new_level_id} not found`,
        );
      }
    }

    // Track highest level by display_order
    if (
      !highestIntendedLevel ||
      intendedLevel.display_order > highestIntendedLevel.display_order
    ) {
      highestIntendedLevel = intendedLevel;
    }

    validatedUpdates.push({
      programInterest,
      newLevelId: update.new_level_id,
      comments: update.comments,
      intendedLevel,
    });
  }

  return { validatedUpdates, highestIntendedLevel };
}

/**
 * Create new program interests during disposition
 */
export async function createNewProgramInterests(
  newProgramData: Array<{
    program_id: number;
    initial_level_id: number;
    lead_source_id?: number;
    comments?: string;
    metadata?: Record<string, any>;
  }>,
  lead: any,
  existingDispositionId: number,
  spocUserId: number,
  leadProgramInterestService: any,
): Promise<LeadProgramInterest[]> {
  const newProgramInterests: LeadProgramInterest[] = [];

  if (!newProgramData || newProgramData.length === 0) {
    return newProgramInterests;
  }

  for (const newProgram of newProgramData) {
    try {
      const newProgramInterest =
        await leadProgramInterestService.createProgramInterestDuringDisposition(
          {
            lead_id: lead.id,
            client_id: lead.client_id,
            program_id: newProgram.program_id,
            initial_level_id: newProgram.initial_level_id,
            lead_source_id: newProgram.lead_source_id,
            comments: newProgram.comments,
            metadata: {
              ...newProgram.metadata,
              created_via: 'disposition',
              created_during_disposition_id: existingDispositionId,
            },
          },
          spocUserId,
          existingDispositionId,
        );

      newProgramInterests.push(newProgramInterest);

      logger.log(
        `Created new program interest ${newProgramInterest.id} during disposition ${existingDispositionId}`,
      );
    } catch (error) {
      logger.error(
        `Failed to create program interest for program ${newProgram.program_id}: ${error.message}`,
      );
      throw new BadRequestException(
        `Failed to create program interest for program ${newProgram.program_id}: ${error.message}`,
      );
    }
  }

  return newProgramInterests;
}

/**
 * Calculates the highest level from all program interests (existing + newly created)
 */
export function calculateHighestLevelFromAllInterests(
  existingInterests: LeadProgramInterest[],
  newlyCreatedInterests: LeadProgramInterest[],
  validatedUpdates: ValidatedProgramUpdate[],
): LeadLevel | null {
  let highestLevel: LeadLevel | null = null;

  // Check existing program interests (considering their intended levels from updates)
  for (const interest of existingInterests) {
    const update = validatedUpdates.find(
      (u) => u.programInterest.id === interest.id,
    );
    const levelToConsider = update ? update.intendedLevel : interest.lead_level;

    if (
      !highestLevel ||
      levelToConsider.display_order > highestLevel.display_order
    ) {
      highestLevel = levelToConsider;
    }
  }

  // Check newly created program interests
  for (const newInterest of newlyCreatedInterests) {
    if (
      !highestLevel ||
      newInterest.lead_level.display_order > highestLevel.display_order
    ) {
      highestLevel = newInterest.lead_level;
    }
  }

  return highestLevel;
}

/**
 * Calculate the highest level from all program interests (existing + new)
 */
export function calculateHighestLevel(
  existingProgramInterests: LeadProgramInterest[],
  validatedUpdates: ValidatedProgramUpdate[],
  newProgramInterests: LeadProgramInterest[],
): LeadLevel | null {
  let highestLevel: LeadLevel | null = null;

  // Check existing program interests (considering their intended levels after updates)
  for (const programInterest of existingProgramInterests) {
    // Find if this program interest has an update
    const update = validatedUpdates.find(
      (u) => u.programInterest.id === programInterest.id,
    );

    const levelToConsider = update
      ? update.intendedLevel
      : programInterest.lead_level;

    if (
      !highestLevel ||
      levelToConsider.display_order > highestLevel.display_order
    ) {
      highestLevel = levelToConsider;
    }
  }

  // Check newly created program interests
  for (const newProgramInterest of newProgramInterests) {
    if (newProgramInterest.lead_level) {
      if (
        !highestLevel ||
        newProgramInterest.lead_level.display_order > highestLevel.display_order
      ) {
        highestLevel = newProgramInterest.lead_level;
      }
    }
  }

  return highestLevel;
}

/**
 * Get all program interests including existing and newly created ones
 */
export function getAllProgramInterests(
  existingProgramInterests: LeadProgramInterest[],
  newProgramInterests: LeadProgramInterest[],
): LeadProgramInterest[] {
  return [...existingProgramInterests, ...newProgramInterests];
}

/**
 * Validate that required fields are present for program updates
 */
export function validateProgramUpdateFields(
  programUpdates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>,
  requiresComments: boolean,
): void {
  if (!programUpdates || programUpdates.length === 0) {
    return;
  }

  for (const update of programUpdates) {
    if (!update.prog_int_id) {
      throw new BadRequestException(
        'Program interest ID is required for updates',
      );
    }

    if (requiresComments && !update.comments?.trim()) {
      throw new BadRequestException(
        `Comments are required for program interest ${update.prog_int_id}`,
      );
    }
  }
}

/**
 * Check if any program updates involve level changes
 */
export function hasLevelChanges(
  programUpdates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>,
): boolean {
  return programUpdates?.some((update) => update.new_level_id) || false;
}

/**
 * Check if any program updates have comments
 */
export function hasComments(
  programUpdates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>,
): boolean {
  return programUpdates?.some((update) => update.comments?.trim()) || false;
}

/**
 * Process and validate all program updates for both existing and new program interests
 */
export async function processAndValidateProgramUpdates(
  programUpdates: Array<{
    prog_int_id: number;
    new_level_id?: number;
    comments?: string;
  }>,
  existingProgramInterests: LeadProgramInterest[],
  newProgramInterests: LeadProgramInterest[],
  leadLevelService: any,
): Promise<{
  validatedUpdates: ValidatedProgramUpdate[];
  highestIntendedLevel: LeadLevel | null;
}> {
  // Get all program interests (existing + newly created)
  const allProgramInterests = [
    ...existingProgramInterests,
    ...newProgramInterests,
  ];

  // Process the program updates
  const { validatedUpdates } = await validateAndProcessProgramUpdates(
    programUpdates,
    allProgramInterests,
    leadLevelService,
  );

  // Calculate the overall highest level considering all interests
  const overallHighestLevel = calculateHighestLevelFromAllInterests(
    existingProgramInterests,
    newProgramInterests,
    validatedUpdates,
  );

  return {
    validatedUpdates,
    highestIntendedLevel: overallHighestLevel,
  };
}
