/**
 * Enum representing the mandatory grouping for top-level disposition nodes
 */
export enum Level1Group {
  Connected = 'Connected',
  NotConnected = 'NotConnected',
  Other = 'Other',
}

/**
 * Enum representing auto follow-up logic for disposition configs
 */
export enum AutoFollowUpLogic {
  None = 'None',
  Hours24 = '24Hours',
  Months6 = '6Months',
  FromLevelDefault = 'FromLevelDefault',
  ChangeableBySpocOnly = 'ChangeableBySpocOnly',
}

/**
 * Enum representing auto follow-up logic for disposition outcomes
 */
export enum OutcomeAutoFollowUpLogic {
  None = 'None',
  Hours24 = '24Hours',
  Months6 = '6Months',
  SpecificField = 'SpecificField',
  ChangeableBySpocOnly = 'ChangeableBySpocOnly',
  FromDispositionConfig = 'FromDispositionConfig',
  FromLevelDefault = 'FromLevelDefault',
}
