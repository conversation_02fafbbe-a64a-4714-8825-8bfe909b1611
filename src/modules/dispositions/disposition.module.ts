import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LevelDispositionOutcome } from './entities/level-disposition-outcome.entity';
import { ClientDispositionConfig } from './entities/client-disposition-config.entity';
import { DispositionNode } from './entities/disposition-node.entity';
import { DispositionHistory } from './entities/disposition-history.entity';
import { DispositionService } from './services/disposition.service';
import { DispositionDataService } from './services/disposition-data.service';
import { UserModule } from '../users/user.module';
import { LeadModule } from '../leads/lead.module';
import { DispositionController } from './disposition.controller';
import { Lead } from '../leads/entities/lead.entity';
import { LeadLevel } from '../leads/entities/lead-level.entity';
import { User } from '../users/entities/user.entity';
import { LeadProgramInterest } from '../leads/entities/lead-program-interest.entity';
import { CallLogModule } from '@modules/call-logs/call-log.module';
import { LeadLevelHistory } from '../leads/entities/lead-level-history.entity';
import { Program } from '../programs/entities/program.entity';
import { LeadSource } from '@modules/lead-allocations/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DispositionNode,
      ClientDispositionConfig,
      LevelDispositionOutcome,
      DispositionHistory,
      Lead,
      LeadLevel,
      User,
      LeadProgramInterest,
      LeadLevelHistory,
      Program,
      LeadSource,
    ]),
    UserModule,
    CallLogModule,
    LeadModule,
  ],
  controllers: [DispositionController],
  providers: [DispositionService, DispositionDataService],
  exports: [DispositionService, DispositionDataService],
})
export class DispositionModule {}
