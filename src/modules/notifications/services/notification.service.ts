import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PusherService } from './pusher.service';
import { PusherData } from '../dto/pusher-data.dto';
import { CallEvents } from '../enums/call-events.enum';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    // private readonly leadHistoryService: LeadHistoryService,
    private readonly pusherService: PusherService,
  ) {}

  /**
   * Handles agent call attendance update events
   */
  @OnEvent(CallEvents.CALL_WAS_ANSWERED_BY_AGENT)
  async handleAgentCallAttendanceUpdate(data: PusherData) {
    this.logger.log(
      `Processing agent call attendance update for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid}`,
    );

    try {
      await this.sendCallNotification(data, 'agent call attendance');
    } catch (error) {
      this.logger.error(
        `Error handling agent call attendance notification for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid} - ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handles customer call attendance update events
   */
  @OnEvent(CallEvents.CALL_WAS_ANSWERED_BY_CUSTOMER)
  async handleCustomerCallAttendanceUpdate(data: PusherData) {
    this.logger.log(
      `Processing customer call attendance update for call ID: ${data.callLogId}, leadUuid: ${data.leadUuid}, spocUuid: ${data.spocUuid}`,
    );

    try {
      await this.sendCallNotification(data, 'customer call attendance');

      // Create lead history if lead exists
      if (data.leadId) {
        this.logger.debug(
          `Lead ID ${data.leadId} associated with call, lead history creation pending implementation`,
        );
        //todo: Uncomment when lead history service is implemented
        // await this.createCallLeadHistory(callLog);
      } else {
        this.logger.debug(
          `No lead ID associated with call ID: ${data.callLogId}, skipping lead history creation`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error handling customer call attendance notification for call ID: ${data.callLogId}, leadUuid: ${data.leadUuid} - ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handles incoming call notification events
   * Triggered when new call is received on the system
   */
  @OnEvent(CallEvents.CALL_WAS_RECEIVED)
  async handleCallReceived(data: PusherData) {
    this.logger.log(
      `Processing incoming call notification for call ID: ${data.callLogId}, candidate: ${data.candidateName}, phone: ${data.maskedPhoneNumber}, spocUuid: ${data.spocUuid}`,
    );

    try {
      await this.sendCallNotification(data, 'incoming call');
    } catch (error) {
      this.logger.error(
        `Error handling incoming call notification for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid} - ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handles call termination notification events
   * Triggered when call hanged up or ended
   */
  @OnEvent(CallEvents.CALL_WAS_TERMINATED)
  async handleCallTermination(data: PusherData) {
    this.logger.log(
      `Processing call termination notification for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid}, status: ${data.callAttendStatus}`,
    );

    try {
      await this.sendCallNotification(data, 'call termination');
    } catch (error) {
      this.logger.error(
        `Error handling call termination notification for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid} - ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handles call recording update events
   */
  @OnEvent(CallEvents.CALL_RECORDING_UPDATED)
  async handleCallRecordingUpdate(data: PusherData) {
    this.logger.log(
      `Processing call recording update for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid}`,
    );

    try {
      await this.sendCallNotification(data, 'call recording update');
    } catch (error) {
      this.logger.error(
        `Error handling call recording update notification for call ID: ${data.callLogId}, spocUuid: ${data.spocUuid} - ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Common method to send call notifications
   */
  private async sendCallNotification(
    data: PusherData,
    notificationType: string,
  ): Promise<void> {
    if (data.spocUuid) {
      this.logger.debug(
        `Sending ${notificationType} notification to user: ${data.spocUuid} for call: ${data.callLogId}`,
      );
      await this.pusherService.sendToUser(data.spocUuid, 'CALL', data);
      this.logger.log(
        `Successfully sent ${notificationType} notification to user: ${data.spocUuid} for call ID: ${data.callLogId}`,
      );
    } else {
      this.logger.warn(
        `No spocUuid provided for ${notificationType} notification, call ID: ${data.callLogId}`,
      );
    }
  }
}
