import { Injectable, Logger } from '@nestjs/common';
import { PusherService as NestjsPusher } from 'nestjs-pusher';

@Injectable()
export class PusherService {
  private readonly logger = new Logger(PusherService.name);

  constructor(private readonly pusherClient: NestjsPusher) {}

  /**
   * Send notification to specific user via their UUID
   * @param userUuid User's UUID to target notification
   * @param type Type of notification
   * @param data Data payload to send
   */
  async sendToUser(
    userUuid: string,
    type: string,
    data: any,
  ): Promise<boolean> {
    try {
      if (!userUuid) {
        this.logger.warn('Attempted to send notification with empty userUuid');
        return false;
      }

      const channelName = `socket_${userUuid}`;
      const eventName = 'WEB';
      const payload = {
        type,
        data,
        timestamp: new Date().toISOString(),
      };

      this.logger.debug(
        `Sending notification to user ${userUuid} with type: ${type}, data: ${JSON.stringify(data)}`,
      );

      const response = await this.pusherClient.trigger(
        channelName,
        eventName,
        payload,
      );

      this.logger.log(
        `Successfully sent notification to user ${userUuid}, response: ${JSON.stringify(response)}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send notification to user ${userUuid}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Send notification to multiple users at once
   * @param userUuids Array of user UUIDs
   * @param type Notification type
   * @param data Data payload
   */
  async sendToMultipleUsers(
    userUuids: string[],
    type: string,
    data: any,
  ): Promise<boolean> {
    try {
      if (!userUuids?.length) {
        this.logger.warn('Attempted to send notifications to empty users list');
        return true;
      }

      this.logger.debug(
        `Sending notifications to ${userUuids.length} users with type: ${type}`,
      );

      const channels = userUuids.map((uuid) => `socket_${uuid}`);
      const payload = {
        type,
        data,
        timestamp: new Date().toISOString(),
      };

      // Send to each channel individually, but in parallel
      await Promise.all(
        channels.map((channel) =>
          this.pusherClient.trigger(channel, 'WEB', payload),
        ),
      );

      this.logger.log(
        `Successfully sent notifications to ${userUuids.length} users`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to send notifications to multiple users: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
