import { Module } from '@nestjs/common';
// import { PusherModule } from 'nestjs-pusher';
import { NotificationService } from './services/notification.service';
import { PusherService } from './services/pusher.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { PusherModule } from 'nestjs-pusher';

@Module({
  imports: [EventEmitterModule.forRoot(), PusherModule],
  providers: [NotificationService, PusherService],
  exports: [NotificationService, PusherService],
})
export class NotificationModule {}
