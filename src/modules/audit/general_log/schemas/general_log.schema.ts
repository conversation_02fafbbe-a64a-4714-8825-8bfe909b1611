import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum MongoLogType {
  ERROR = 'error',
  EVENT = 'event',
  INFO = 'info',
}

@Schema()
export class GeneralLog extends Document {
  @Prop({ type: String, enum: MongoLogType, required: true })
  logType: string; // Type of log (e.g., 'api', 'error', 'event', 'info')

  @Prop({ type: Object })
  context?: object; // Additional context or metadata

  @Prop({ type: String })
  entityType?: string; // Type of the entity related to the log (e.g., 'user', 'order')

  @Prop({ type: String })
  entityId?: string; // ID of the entity related to the log

  @Prop({ type: Object, required: true })
  data: object; // Flexible field to store any log data

  @Prop({ type: String })
  message?: string; // Message to be logged

  @Prop({ type: String })
  source?: string; // Source of the log (e.g., 'server', 'client')

  @Prop({ type: Date, default: Date.now })
  timestamp?: Date; // Automatically set to current date
}

export const GeneralLogSchema = SchemaFactory.createForClass(GeneralLog);
