import { LogService } from '../mongo_log.service';
import { MongoLogType } from '../schemas/general_log.schema';

let logServiceInstance: LogService;

// Initialize the LogService instance
export function initializeLogger(logService: LogService) {
  logServiceInstance = logService;
}

// Utility function to log messages
export async function logToMongoDB(
  logType: MongoLogType,
  obj: {
    data?: object;
    entityId?: string;
    entityType?: string;
    message?: string;
    source?: string;
    context?: object;
  },
) {
  if (!logServiceInstance) {
    throw new Error(
      'LogService is not initialized. Call initializeLogger first.',
    );
  }
  try {
    await logServiceInstance.log(logType, obj);
  } catch (error) {
    console.error('Error logging to MongoDB:', error);
  }
}
