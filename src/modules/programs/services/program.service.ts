import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Program } from '../entities/program.entity';
import { CreateProgramDto } from '../dto/create-program.dto';
import { UpdateProgramDto } from '../dto/update-program.dto';

@Injectable()
export class ProgramService {
  constructor(
    @InjectRepository(Program)
    private readonly ProgramRepository: Repository<Program>,
    @InjectRepository(Program)
    private readonly programRepository: Repository<Program>,
  ) {}

  findOneCourse(id: number) {
    return this.ProgramRepository.findOne({ where: { id: id } });
  }

  async create(
    createProgramDto: CreateProgramDto,
    userId?: number,
  ): Promise<Program> {
    const program = this.programRepository.create({
      ...createProgramDto,
      created_by: userId,
    });
    return this.programRepository.save(program);
  }

  async findActive(
    page = 1,
    size = 10,
  ): Promise<{ data: Program[]; meta: any }> {
    const skip = (page - 1) * size;

    const [programs, total] = await this.programRepository.findAndCount({
      where: { is_active: true },
      skip,
      take: size,
    });

    const totalPages = Math.ceil(total / size);

    return {
      data: programs,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findAll(page = 1, size = 10): Promise<{ data: Program[]; meta: any }> {
    const skip = (page - 1) * size;

    const [programs, total] = await this.programRepository.findAndCount({
      skip,
      take: size,
    });

    const totalPages = Math.ceil(total / size);

    return {
      data: programs,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: number): Promise<Program> {
    const program = await this.programRepository.findOne({
      where: { id },
    });

    if (!program) {
      throw new NotFoundException(`Program with ID ${id} not found`);
    }

    return program;
  }

  async findByCode(code: string): Promise<Program> {
    const program = await this.programRepository.findOne({
      where: { code },
    });

    if (!program) {
      throw new NotFoundException(`Program with code ${code} not found`);
    }

    return program;
  }

  async update(
    id: number,
    updateProgramDto: UpdateProgramDto,
    userId?: number,
  ): Promise<Program> {
    const program = await this.findOne(id);

    const updatedProgram = {
      ...program,
      ...updateProgramDto,
      updated_by: userId,
    };

    return this.programRepository.save(updatedProgram);
  }

  async remove(id: number): Promise<void> {
    const result = await this.programRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Program with ID ${id} not found`);
    }
  }
}
