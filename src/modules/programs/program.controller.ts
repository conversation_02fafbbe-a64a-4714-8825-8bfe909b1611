import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  Req,
} from '@nestjs/common';
import { ProgramService } from './services/program.service';
import { CreateProgramDto } from './dto/create-program.dto';
import { UpdateProgramDto } from './dto/update-program.dto';
import { Program } from './entities/program.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';

@ApiTags('programs')
@Controller('programs')
export class ProgramController {
  constructor(private readonly programService: ProgramService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new program' })
  @ApiBody({ type: CreateProgramDto })
  async create(
    @Body() createProgramDto: CreateProgramDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<Program> {
    const userId = req.user?.id || null;
    return this.programService.create(createProgramDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get list of programs with optional filters' })
  @ApiQuery({ type: PaginationDto, required: false })
  async findAll(
    @Query() paginationDto?: PaginationDto,
  ): Promise<{ data: Program[]; meta: any }> {
    const { page = 1, size = 10, active } = paginationDto || {};

    if (active === 'true') {
      return this.programService.findActive(page, size);
    }
    return this.programService.findAll(page, size);
  }

  @Get('code/:code')
  @ApiOperation({ summary: 'Get program by unique code' })
  @ApiParam({ name: 'code', description: 'Program code string' })
  async findByCode(@Param('code') code: string): Promise<Program> {
    return this.programService.findByCode(code);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get program by ID' })
  @ApiParam({ name: 'id', description: 'Program ID' })
  async findOne(@Param('id') id: string): Promise<Program> {
    return this.programService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update program details by ID' })
  @ApiParam({ name: 'id', description: 'Program ID' })
  @ApiBody({ type: UpdateProgramDto })
  async update(
    @Param('id') id: string,
    @Body() updateProgramDto: UpdateProgramDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<Program> {
    const userId = req.user?.id || null;
    return this.programService.update(+id, updateProgramDto, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete program by ID' })
  @ApiParam({ name: 'id', description: 'Program ID' })
  async remove(@Param('id') id: string): Promise<void> {
    return this.programService.remove(+id);
  }
}
