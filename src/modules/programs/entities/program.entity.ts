import { Entity, Column, OneToMany } from 'typeorm';
import { NetEnquiry } from 'src/modules/net-enquiries/entities/net-enquiry.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { LeadProgramInterest } from 'src/modules/leads/entities/lead-program-interest.entity';

@Entity()
export class Program extends ClientAwareEntity {
  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  code: string;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @OneToMany(() => NetEnquiry, (netEnquiry) => netEnquiry.program)
  netEnquiries: NetEnquiry[];

  @OneToMany(
    () => LeadProgramInterest,
    (leadProgramInterest) => leadProgramInterest.program,
  )
  leadProgramInterests: LeadProgramInterest[];
}
