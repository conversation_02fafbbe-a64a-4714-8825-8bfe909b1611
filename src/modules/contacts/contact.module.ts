import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Contact } from './entities/contact.entity';
import { Email } from './entities/email.entity';
import { Phone } from './entities/phone.entity';
import { WorkExperience } from './entities/work-experience.entity';
import { Education } from './entities/education.entity';
import { ContactController } from './contact.controller';
import { ClientModule } from '../clients/client.module';
import { UserClientModule } from '@modules/user-clients/user-client.module';
import { RoleModule } from '@modules/roles/role.module';
import { ContactService } from './services/contact.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Contact,
      Email,
      Phone,
      WorkExperience,
      Education,
    ]),
    ClientModule,
    ClientModule,
    UserClientModule,
    RoleModule,
  ],
  controllers: [ContactController],
  providers: [ContactService],
  exports: [ContactService],
})
export class ContactModule {}
