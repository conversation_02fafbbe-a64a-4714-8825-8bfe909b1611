import {
  BadRequestException,
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Contact } from '../entities/contact.entity';
import { CreateContactDto } from '../dto/create-contact.dto';
import { Email } from '../entities/email.entity';
import { Phone } from '../entities/phone.entity';
import { Education } from '../entities/education.entity';
import { WorkExperience } from '../entities/work-experience.entity';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  ContactUpdationSubActionType,
  EngagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';
import { UserService } from '@modules/users/services/user.service';
import { PhoneType } from '../enums/contact.enum';
import {
  UpdatePrimaryDetailsDto,
  AddContactInfoDto,
} from '../dto/update-contact.dto';
import { EnrollmentStage } from '@modules/leads/enums/lead-level.enum';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(
    @InjectRepository(Contact)
    private contactRepository: Repository<Contact>,
    @InjectRepository(Email)
    private emailRepository: Repository<Email>,
    @InjectRepository(Phone)
    private phoneRepository: Repository<Phone>,
    @InjectRepository(Education)
    private educationRepository: Repository<Education>,
    @InjectRepository(WorkExperience)
    private workExperienceRepository: Repository<WorkExperience>,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  //todo :  make this file smaller by dividing it into smaller services
  /**
   * Handle a contact creation or update based on the new logic:
   * Case 1: Both email and phone are new - Create a new contact
   * Case 2: Email is new but phone exists - Add email to existing contact - Return reInquiry
   * Case 3: Phone is new but email exists - Add phone to existing contact - Return reInquiry
   * Case 4: Both email and phone exist but in different contacts - Return reInquiry flag for phone contact
   *
   * @param createContactDto The contact information to process
   * @param userId The ID of the user making the request
   * @returns The created or updated contact with all relations and a reInquiry flag
   */
  async handleContact(
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<{ contact: Contact; isNewContact: boolean; reInquiry: boolean }> {
    this.logger.log('Starting handleContact process');
    this.logger.log('Received createContactDto:', createContactDto);

    try {
      const dtoEmail = createContactDto.email?.email;
      const dtoPhoneNumber = createContactDto.phone?.phone_number;

      this.logger.log(
        `Extracted email: ${dtoEmail}, phone number: ${dtoPhoneNumber}`,
      );

      if (!dtoEmail && !dtoPhoneNumber) {
        this.logger.warn(
          'Neither email nor phone number provided in contact DTO',
        );
        throw new BadRequestException(
          'Either email or phone number must be provided',
        );
      }

      const standardizedPhone = dtoPhoneNumber
        ? dtoPhoneNumber.replace(/\D/g, '')
        : null;
      this.logger.log(`Standardized phone number: ${standardizedPhone}`);

      let existingEmail: Email = null;
      let emailContact: Contact = null;

      if (dtoEmail) {
        this.logger.log(`Looking for existing email: ${dtoEmail}`);
        existingEmail = await this.emailRepository.findOne({
          where: { email: dtoEmail },
          relations: ['contact'],
        });

        if (existingEmail && existingEmail.contact) {
          // Get the associated contact
          emailContact = await this.contactRepository.findOne({
            where: { id: existingEmail.contact?.id },
            relations: ['emails', 'phones', 'primary_email', 'primary_phone'],
          });
          this.logger.log(`Associated contact for email: ${emailContact?.id}`);
        } else {
          this.logger.log('No existing email record found');
        }
      }

      let existingPhone: Phone = null;
      let phoneContact: Contact = null;

      if (standardizedPhone) {
        this.logger.log(`Looking for existing phone: ${standardizedPhone}`);
        existingPhone = await this.phoneRepository.findOne({
          where: { phone_number: standardizedPhone },
          relations: ['contact'],
        });

        if (existingPhone && existingPhone.contact) {
          // Get the associated contact
          phoneContact = await this.contactRepository.findOne({
            where: { id: existingPhone.contact?.id },
            relations: ['emails', 'phones', 'primary_email', 'primary_phone'],
          });
          this.logger.log(`Associated contact for phone: ${phoneContact?.id}`);
        } else {
          this.logger.log('No existing phone record found');
        }
      }

      if (
        (existingEmail && !emailContact) ||
        (existingPhone && !phoneContact)
      ) {
        this.logger.log(
          'Email or phone exists without associated contact, creating new contact',
        );
        const contact = await this.createNewContactWithRelations(
          {
            ...createContactDto,
            email: existingEmail ? null : createContactDto.email,
            phone: existingPhone ? null : createContactDto.phone,
          },
          userId,
        );

        if (existingEmail && !emailContact) {
          this.logger.log(
            `Updating existing email record ${existingEmail.id} with new contact ${contact.id}`,
          );
          existingEmail.contact = contact;
          existingEmail.contact_id = contact.id;
          existingEmail.updated_by = userId;
          await this.emailRepository.save(existingEmail);
        }

        if (existingPhone && !phoneContact) {
          this.logger.log(
            `Updating existing phone record ${existingPhone.id} with new contact ${contact.id}`,
          );
          existingPhone.contact = contact;
          existingPhone.contact_id = contact.id;
          existingPhone.updated_by = userId;
          await this.phoneRepository.save(existingPhone);
        }

        //1. need to add create_contact history here
        this.createContactHistory(
          ContactUpdationSubActionType.CREATE,
          null,
          contact,
          userId,
          createContactDto,
        );
        return {
          contact,
          isNewContact: true,
          reInquiry: true,
        };
      }

      if (
        existingEmail &&
        existingPhone &&
        emailContact.id !== phoneContact.id
      ) {
        this.logger.log(
          'Email and phone belong to different contacts, updating phone contact with education/work experience',
        );
        const updatedContact = await this.addEducationAndWorkExperience(
          phoneContact,
          createContactDto,
          userId,
        );
        //2. need to add update_contact history here
        this.createContactHistory(
          ContactUpdationSubActionType.UPDATE,
          phoneContact,
          updatedContact,
          userId,
          createContactDto,
        );
        return {
          contact: await this.findOne(phoneContact.id),
          isNewContact: false,
          reInquiry: true,
        };
      }

      if (!existingEmail && !existingPhone) {
        this.logger.log('Neither email nor phone exists, creating new contact');
        const contact = await this.createNewContactWithRelations(
          createContactDto,
          userId,
        );
        //3. need to add create_contact history here
        this.createContactHistory(
          ContactUpdationSubActionType.CREATE,
          null,
          contact,
          userId,
          createContactDto,
        );
        return {
          contact,
          isNewContact: true,
          reInquiry: false,
        };
      }

      if (!existingEmail && existingPhone) {
        this.logger.log(
          'Email is new but phone exists, adding email to existing phone contact',
        );
        const updatedContact = await this.addEmailToExistingContact(
          phoneContact,
          createContactDto,
          userId,
        );
        //4. need add update_contact history here
        this.createContactHistory(
          ContactUpdationSubActionType.UPDATE,
          phoneContact,
          updatedContact,
          userId,
          createContactDto,
        );
        return {
          contact: updatedContact,
          isNewContact: false,
          reInquiry: true,
        };
      }

      if (existingEmail && !existingPhone) {
        this.logger.log(
          'Phone is new but email exists, adding phone to existing email contact',
        );
        const updatedContact = await this.addPhoneToExistingContact(
          emailContact,
          createContactDto,
          userId,
        );
        //5. need to add update_contact history here
        this.createContactHistory(
          ContactUpdationSubActionType.UPDATE,
          emailContact,
          updatedContact,
          userId,
          createContactDto,
        );
        return {
          contact: updatedContact,
          isNewContact: false,
          reInquiry: true,
        };
      }

      this.logger.log(
        'Both email and phone exist and belong to the same contact, adding education/work experience',
      );
      const updatedContact = await this.addEducationAndWorkExperience(
        emailContact,
        createContactDto,
        userId,
      );
      //6. need to add update_contact history here
      this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        emailContact,
        updatedContact,
        userId,
        createContactDto,
      );
      return {
        contact: await this.findOne(emailContact.id),
        isNewContact: false,
        reInquiry: false,
      };
    } catch (error) {
      this.logger.error(
        `Error in handleContact: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Error processing contact');
    }
  }

  /**
   * Adds education and work experience to an existing contact
   */
  private async addEducationAndWorkExperience(
    contact: Contact,
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    // Handle educations if provided
    if (createContactDto.educations && createContactDto.educations.length > 0) {
      for (const educationDto of createContactDto.educations) {
        const education = new Education();
        Object.assign(education, {
          ...educationDto,
          contact_id: contact.id,
          contact: contact,
          created_by: userId,
          updated_by: userId,
        });
        await this.educationRepository.save(education);
      }
    }

    // Handle work experiences if provided
    if (
      createContactDto.workExperiences &&
      createContactDto.workExperiences.length > 0
    ) {
      for (const workExperienceDto of createContactDto.workExperiences) {
        const workExperience = new WorkExperience();
        Object.assign(workExperience, {
          ...workExperienceDto,
          contact_id: contact.id,
          contact: contact,
          created_by: userId,
          updated_by: userId,
        });
        await this.workExperienceRepository.save(workExperience);
      }
    }

    // Update the contact's updated_by field
    contact.updated_by = userId;
    return await this.contactRepository.save(contact);
  }

  /**
   * Creates a new contact with all provided relations and sets the primary email and phone
   */
  private async createNewContactWithRelations(
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    this.logger.log('Starting creation of new contact with relations');
    this.logger.log('Received createContactDto:', createContactDto);

    // Create new contact entity
    const contact = new Contact();
    Object.assign(contact, {
      first_name: createContactDto.first_name,
      last_name: createContactDto.last_name,
      full_name:
        `${createContactDto.first_name} ${createContactDto.last_name}`.trim(),
      city: createContactDto.city,
      candidate_id: createContactDto.candidate_id,
      miles_office_id: createContactDto.miles_office_id,
      created_by: userId,
      updated_by: userId,
    });
    this.logger.log('Contact entity prepared:', contact);

    // Save the contact to get an ID
    const savedContact = await this.contactRepository.save(contact);
    const newContactId = savedContact.id;
    this.logger.log(`Contact saved successfully with ID: ${newContactId}`);

    // Create email if provided
    let email: Email = null;
    if (createContactDto.email && createContactDto.email.email) {
      this.logger.log('Creating email record for contact');
      email = new Email();
      Object.assign(email, {
        ...createContactDto.email,
        contact_id: newContactId,
        contact: savedContact,
        created_by: userId,
        updated_by: userId,
      });
      email = await this.emailRepository.save(email);
      this.logger.log(`Email saved successfully with ID: ${email.id}`);

      // Set as primary email on contact
      savedContact.primary_email = email;
      savedContact.primary_email_id = email.id;
    }

    // Create phone if provided
    let phone: Phone = null;
    if (createContactDto.phone && createContactDto.phone.phone_number) {
      this.logger.log('Creating phone record for contact');
      phone = new Phone();
      // Standardize phone number
      const standardizedPhone = createContactDto.phone.phone_number.replace(
        /\D/g,
        '',
      );
      Object.assign(phone, {
        ...createContactDto.phone,
        phone_number: standardizedPhone,
        contact_id: newContactId,
        contact: savedContact,
        created_by: userId,
        updated_by: userId,
      });
      phone = await this.phoneRepository.save(phone);
      this.logger.log(`Phone saved successfully with ID: ${phone.id}`);

      // Set as primary phone on contact
      savedContact.primary_phone = phone;
      savedContact.primary_phone_id = phone.id;
    }

    // Save contact again to update primary email and phone references
    await this.contactRepository.save(savedContact);
    this.logger.log('Contact updated with primary email and phone');

    // Create education records if any
    if (createContactDto.educations && createContactDto.educations.length > 0) {
      this.logger.log(
        `Creating ${createContactDto.educations.length} education record(s)`,
      );
      for (const educationDto of createContactDto.educations) {
        const education = new Education();
        Object.assign(education, {
          ...educationDto,
          contact_id: newContactId,
          contact: savedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.educationRepository.save(education);
        this.logger.log(
          `Education record saved for contact ID ${newContactId}`,
        );
      }
    } else {
      this.logger.log('No education records to create');
    }

    // Create work experience records if any
    if (
      createContactDto.workExperiences &&
      createContactDto.workExperiences.length > 0
    ) {
      this.logger.log(
        `Creating ${createContactDto.workExperiences.length} work experience record(s)`,
      );
      for (const workExperienceDto of createContactDto.workExperiences) {
        const workExperience = new WorkExperience();
        Object.assign(workExperience, {
          ...workExperienceDto,
          contact_id: newContactId,
          contact: savedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.workExperienceRepository.save(workExperience);
        this.logger.log(
          `Work experience record saved for contact ID ${newContactId}`,
        );
      }
    } else {
      this.logger.log('No work experience records to create');
    }

    this.logger.log(
      `Finished creating contact with ID ${newContactId}, fetching full contact details`,
    );
    // Return the fully loaded contact entity
    return this.findOne(newContactId);
  }

  /**
   * Adds a new email to an existing contact
   */
  private async addEmailToExistingContact(
    existingContact: Contact,
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    // Update basic contact info if provided

    //! Name and city need not to be updated
    // if (createContactDto.first_name) {
    //   existingContact.first_name = createContactDto.first_name;
    // }

    // if (createContactDto.last_name) {
    //   existingContact.last_name = createContactDto.last_name;
    // }

    // if (createContactDto.city) {
    //   existingContact.city = createContactDto.city;
    // }

    existingContact.updated_by = userId;
    await this.contactRepository.save(existingContact);

    // Create new email
    if (createContactDto.email && createContactDto.email.email) {
      const email = new Email();
      Object.assign(email, {
        ...createContactDto.email,
        contact_id: existingContact.id,
        contact: existingContact,
        created_by: userId,
        updated_by: userId,
      });
      await this.emailRepository.save(email);

      // Primary email remains unchanged
    }

    // Handle educations if provided
    if (createContactDto.educations && createContactDto.educations.length > 0) {
      for (const educationDto of createContactDto.educations) {
        const education = new Education();
        Object.assign(education, {
          ...educationDto,
          contact_id: existingContact.id,
          contact: existingContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.educationRepository.save(education);
      }
    }

    // Handle work experiences if provided
    if (
      createContactDto.workExperiences &&
      createContactDto.workExperiences.length > 0
    ) {
      for (const workExperienceDto of createContactDto.workExperiences) {
        const workExperience = new WorkExperience();
        Object.assign(workExperience, {
          ...workExperienceDto,
          contact_id: existingContact.id,
          contact: existingContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.workExperienceRepository.save(workExperience);
      }
    }

    return this.findOne(existingContact.id);
  }

  /**
   * Adds a new phone to an existing contact
   */
  private async addPhoneToExistingContact(
    existingContact: Contact,
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    // Update basic contact info if provided

    //! Name and city need not to be updated
    // if (createContactDto.first_name) {
    //   existingContact.first_name = createContactDto.first_name;
    // }

    // if (createContactDto.last_name) {
    //   existingContact.last_name = createContactDto.last_name;
    // }

    // if (createContactDto.city) {
    //   existingContact.city = createContactDto.city;
    // }

    existingContact.updated_by = userId;
    await this.contactRepository.save(existingContact);

    // Create new phone
    if (createContactDto.phone && createContactDto.phone.phone_number) {
      const phone = new Phone();
      // Standardize phone number
      const standardizedPhone = createContactDto.phone.phone_number.replace(
        /\D/g,
        '',
      );
      Object.assign(phone, {
        ...createContactDto.phone,
        phone_number: standardizedPhone,
        contact_id: existingContact.id,
        contact: existingContact,
        created_by: userId,
        updated_by: userId,
      });
      await this.phoneRepository.save(phone);

      // Primary phone remains unchanged
    }

    // Handle educations if provided
    if (createContactDto.educations && createContactDto.educations.length > 0) {
      for (const educationDto of createContactDto.educations) {
        const education = new Education();
        Object.assign(education, {
          ...educationDto,
          contact_id: existingContact.id,
          contact: existingContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.educationRepository.save(education);
      }
    }

    // Handle work experiences if provided
    if (
      createContactDto.workExperiences &&
      createContactDto.workExperiences.length > 0
    ) {
      for (const workExperienceDto of createContactDto.workExperiences) {
        const workExperience = new WorkExperience();
        Object.assign(workExperience, {
          ...workExperienceDto,
          contact_id: existingContact.id,
          contact: existingContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.workExperienceRepository.save(workExperience);
      }
    }

    return this.findOne(existingContact.id);
  }

  async findOne(id: number): Promise<Contact> {
    try {
      const contact = await this.contactRepository.findOne({
        where: { id },
        relations: [
          'emails',
          'phones',
          'educations',
          'workExperiences',
          'primary_email',
          'primary_phone',
        ],
      });

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${id} not found`);
      }

      return contact;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error finding contact with ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to find contact with ID ${id}`);
    }
  }

  async findOneByCandidateId(candidateId: string): Promise<Contact> {
    try {
      const contact = await this.contactRepository.findOne({
        where: { candidate_id: candidateId },
        relations: [
          'emails',
          'phones',
          'educations',
          'workExperiences',
          'primary_email',
          'primary_phone',
        ],
      });

      if (!contact) {
        return null;
      }

      return contact;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error finding contact with candidate ID ${candidateId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Failed to find contact with candidate ID ${candidateId}`,
      );
    }
  }

  async remove(id: number): Promise<void> {
    try {
      const result = await this.contactRepository.delete({ id });

      if (result.affected === 0) {
        throw new NotFoundException(`Contact with ID ${id} not found`);
      }
    } catch (error) {
      this.logger.error(
        `Error removing contact with ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async isEmailExists(email: string): Promise<boolean> {
    try {
      // Validate input
      if (!email) {
        throw new BadRequestException('Email is required');
      }
      // Check if email exists in the database
      const existingEmail = await this.emailRepository.findOne({
        where: { email },
      });
      return !!existingEmail;
    } catch (error) {
      this.logger.error(
        `Error checking email existence: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to check email existence');
    }
  }

  async isPhoneExists(phoneNumber: string): Promise<boolean> {
    try {
      // Validate input
      if (!phoneNumber) {
        throw new BadRequestException('Phone number is required');
      }
      // Standardize the phone number by removing non-digit characters
      const standardizedPhone = phoneNumber.replace(/\D/g, '');
      // Check if phone exists in the database
      const existingPhone = await this.phoneRepository.findOne({
        where: { phone_number: standardizedPhone },
      });
      return !!existingPhone;
    } catch (error) {
      this.logger.error(
        `Error checking phone existence: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to check phone existence');
    }
  }

  /**
   * Finds a contact by phone number and country code
   * Both values are mandatory
   *
   * @param phoneNumber The phone number to search for (mandatory)
   * @param countryCode The country code to search for (mandatory)
   * @returns The contact with all relations if found, null otherwise
   * @throws BadRequestException if either parameter is missing
   */
  async findContactByPhone(
    phoneNumber: string,
    countryCode: string,
  ): Promise<Contact | null> {
    try {
      // Validate required parameters
      if (!phoneNumber || !countryCode) {
        throw new BadRequestException(
          'Both phone number and country code are required',
        );
      }

      // Standardize the phone number by removing non-digit characters
      const standardizedPhone = phoneNumber.replace(/\D/g, '');

      // Find the phone with both number and country code
      const phone = await this.phoneRepository.findOne({
        where: {
          phone_number: standardizedPhone,
          country_code: countryCode,
        },
        relations: ['contact'],
      });

      // If no phone found or no associated contact, return null
      if (!phone || !phone.contact) {
        return null;
      }

      // Get the complete contact with all relations
      return this.findOne(phone.contact.id);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(
        `Error finding contact by phone ${phoneNumber} and country code ${countryCode}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Failed to find contact by phone ${phoneNumber} and country code ${countryCode}`,
      );
    }
  }

  async findContactByEmail(email: string): Promise<Contact | null> {
    try {
      // Validate required parameter
      if (!email) {
        throw new BadRequestException('Email is required');
      }

      // Find the email with the provided email address
      const foundEmail = await this.emailRepository.findOne({
        where: { email },
        relations: ['contact'],
      });

      // If no email found or no associated contact, return null
      if (!foundEmail || !foundEmail.contact) {
        return null;
      }

      // Get the complete contact with all relations
      return this.findOne(foundEmail.contact.id);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(
        `Error finding contact by email ${email}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to find contact by email ${email}`);
    }
  }

  /**
   * Helper method to create contact history records
   */
  private async createContactHistory(
    subAction: ContactUpdationSubActionType,
    oldData: any | null,
    newData: any,
    userId: number,
    metadata: any,
  ): Promise<void> {
    const historyData: any = {
      action: ActionType.CONTACT_UPDATION,
      subAction,
      leadId: null,
      contactId: newData.id,
      performedByUser: (await this.userService.findOne(userId)) ?? null,
      performedBy: userId ?? null,
      metadata,
    };

    if (subAction === ContactUpdationSubActionType.CREATE) {
      historyData.details = { data: newData };
    } else if (subAction === ContactUpdationSubActionType.UPDATE) {
      historyData.details = { oldData, newData };
    }
    this.leadHistoryService.createLeadHistory(historyData);
  }

  async updatePhoneType(phoneTypeData: {
    phoneNumber: string;
    phoneType: PhoneType;
  }): Promise<Phone> {
    try {
      const { phoneNumber, phoneType } = phoneTypeData;
      // Validate input
      if (!phoneNumber || !phoneType) {
        throw new BadRequestException('Phone number and type are required');
      }

      // Find the phone by number
      const standardizedPhone = phoneNumber.replace(/\D/g, '');
      const phone = await this.phoneRepository.findOne({
        where: { phone_number: standardizedPhone },
      });

      if (!phone) {
        throw new NotFoundException(
          `Phone with number ${phoneNumber} not found`,
        );
      }

      // Store old phone data before updating
      const oldPhone = { ...phone };

      // Update the phone type
      phone.phone_type = phoneType;
      const updatedPhone = await this.phoneRepository.save(phone);
      // Create contact history when phone is updated
      await this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        oldPhone,
        updatedPhone,
        phone.updated_by,
        phoneTypeData,
      );
      return updatedPhone;
    } catch (error) {
      this.logger.error(
        `Error updating phone type for ${phoneTypeData.phoneNumber}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updatePrimaryDetails({
    contactId,
    primaryEmailId,
    primaryPhoneId,
  }: UpdatePrimaryDetailsDto): Promise<Contact> {
    try {
      // Validate input - at least one of email or phone ID must be provided
      if (!contactId) {
        throw new BadRequestException('Contact ID is required');
      }

      if (!primaryEmailId && !primaryPhoneId) {
        throw new BadRequestException(
          'At least one of primary email ID or primary phone ID must be provided',
        );
      }

      // Find the contact
      const contact = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: ['emails', 'phones'],
      });

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found`);
      }

      // Store old contact data before updating
      const oldContact = { ...contact };
      const updateParams = {};

      // Update primary email if provided
      if (primaryEmailId) {
        const primaryEmail = contact.emails.find(
          (email) => email.id === primaryEmailId,
        );

        if (!primaryEmail) {
          throw new BadRequestException(
            `Email with ID ${primaryEmailId} not found for this contact`,
          );
        }

        // Check if this email is already primary for another contact
        const existingPrimaryContact = await this.contactRepository.findOne({
          where: { primary_email_id: primaryEmailId },
        });

        if (existingPrimaryContact && existingPrimaryContact.id !== contactId) {
          throw new BadRequestException(
            `Email with ID ${primaryEmailId} is already set as primary for another contact`,
          );
        }

        updateParams['primary_email_id'] = primaryEmail.id;
      }

      // Update primary phone if provided
      if (primaryPhoneId) {
        const primaryPhone = contact.phones.find(
          (phone) => phone.id === primaryPhoneId,
        );

        if (!primaryPhone) {
          throw new BadRequestException(
            `Phone with ID ${primaryPhoneId} not found for this contact`,
          );
        }

        // Check if this phone is already primary for another contact
        const existingPrimaryContact = await this.contactRepository.findOne({
          where: { primary_phone_id: primaryPhoneId },
        });

        if (existingPrimaryContact && existingPrimaryContact.id !== contactId) {
          throw new BadRequestException(
            `Phone with ID ${primaryPhoneId} is already set as primary for another contact`,
          );
        }

        updateParams['primary_phone_id'] = primaryPhone.id;
      }

      await this.contactRepository.update(contactId, {
        ...updateParams,
      });

      const updatedContact = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: ['emails', 'phones', 'primary_email', 'primary_phone'],
      });
      // Create contact history when primary details are updated
      await this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        oldContact,
        updatedContact,
        contact.updated_by,
        { primaryEmailId, primaryPhoneId },
      );

      return updatedContact;
    } catch (error) {
      this.logger.error(
        `Error updating primary details for contact ${contactId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Adds new email or phone to an existing contact
   * At least one of email or phone must be provided
   */
  async addContactInfo(
    addContactInfoDto: AddContactInfoDto,
    userId: number,
  ): Promise<Contact> {
    try {
      const {
        contactId,
        email,
        emailLabel,
        phoneNumber,
        countryCode,
        phoneLabel,
      } = addContactInfoDto;

      // Validate input
      if (!contactId) {
        throw new BadRequestException('Contact ID is required');
      }

      if (!email && !phoneNumber) {
        throw new BadRequestException(
          'At least one of email or phone number must be provided',
        );
      }

      // Find the contact
      const contact = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: ['emails', 'phones', 'primary_email', 'primary_phone'],
      });

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found`);
      }

      // Store old contact data before updating
      const oldContact = { ...contact };
      let newEmail: Email = null;
      let newPhone: Phone = null;

      // Add new email if provided
      if (email) {
        // Check if email already exists for this contact
        const existingEmail = contact.emails.find((e) => e.email === email);
        if (existingEmail) {
          throw new BadRequestException(
            `Email ${email} already exists for this contact`,
          );
        }

        // Check if email exists globally
        const globalEmailExists = await this.emailRepository.findOne({
          where: { email },
        });

        if (globalEmailExists) {
          throw new BadRequestException(
            `Email ${email} is already associated with another contact`,
          );
        }

        newEmail = new Email();
        newEmail.email = email;
        newEmail.label = emailLabel || 'Personal';
        newEmail.contact_id = contact.id;
        newEmail.created_by = userId;
        newEmail.updated_by = userId;

        newEmail = await this.emailRepository.save(newEmail);

        // Set as primary email if contact doesn't have one
        if (!contact.primary_email_id) {
          contact.primary_email = newEmail;
          contact.primary_email_id = newEmail.id;
        }
      }

      // Add new phone if provided
      if (phoneNumber) {
        // Standardize phone number
        const standardizedPhone = phoneNumber.replace(/\D/g, '');

        // Check if phone already exists for this contact
        const existingPhone = contact.phones.find(
          (p) => p.phone_number === standardizedPhone,
        );
        if (existingPhone) {
          throw new BadRequestException(
            `Phone number ${phoneNumber} already exists for this contact`,
          );
        }

        // Check if phone exists globally
        const globalPhoneExists = await this.phoneRepository.findOne({
          where: { phone_number: standardizedPhone },
        });

        if (globalPhoneExists) {
          throw new BadRequestException(
            `Phone number ${phoneNumber} is already associated with another contact`,
          );
        }

        newPhone = new Phone();
        Object.assign(newPhone, {
          phone_number: standardizedPhone,
          country_code: countryCode || '+91',
          label: phoneLabel || 'Mobile',
          contact_id: contact.id,
          created_by: userId,
          updated_by: userId,
        });
        newPhone = await this.phoneRepository.save(newPhone);

        // Set as primary phone if contact doesn't have one
        if (!contact.primary_phone_id) {
          contact.primary_phone = newPhone;
          contact.primary_phone_id = newPhone.id;
        }
      }

      await this.contactRepository.update(contactId, { updated_by: userId });
      const updatedContact = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: ['emails', 'phones', 'primary_email', 'primary_phone'],
      });

      // Create contact history
      await this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        oldContact,
        updatedContact,
        userId,
        {
          addedEmail: newEmail
            ? { id: newEmail.id, email: newEmail.email }
            : null,
          addedPhone: newPhone
            ? { id: newPhone.id, phone_number: newPhone.phone_number }
            : null,
        },
      );

      return this.findOne(contact.id);
    } catch (error) {
      this.logger.error(
        `Error adding contact info for contact ${addContactInfoDto.contactId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateMetadata(metadata: any, contactId: number): Promise<Contact> {
    try {
      // Validate input
      if (!metadata || !contactId) {
        throw new BadRequestException(
          'Both metadata and contact ID are required',
        );
      }

      // Find the contact
      const contact = await this.contactRepository.findOne({
        where: { id: contactId },
      });

      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found`);
      }

      // Initialize metadata if it doesn't exist
      if (!contact.metadata) {
        contact.metadata = {};
      }

      // Store old contact data before updating
      const oldContact = { ...contact };
      // Merge new metadata with existing metadata
      contact.metadata = {
        ...contact.metadata,
        ...metadata,
      };

      const updatedContact = await this.contactRepository.save(contact);
      // Create contact history when metadata is updated
      await this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        oldContact,
        updatedContact,
        contact.updated_by,
        metadata,
      );
      return updatedContact;
    } catch (error) {
      this.logger.error(
        `Error updating metadata for contact ${contactId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateContactName({
    contactId,
    firstName,
    lastName,
    userId,
  }: {
    contactId: number;
    firstName: string;
    lastName?: string;
    userId: number;
  }): Promise<Contact> {
    try {
      // Validate input
      if (!contactId || !firstName) {
        throw new BadRequestException(
          'Contact ID, first name, and last name are required',
        );
      }
      //find all the leads for this contact and get all the program interests, if for any program interest the level has status enrolled, then name update not possible
      const contact = await this.contactRepository.findOne({
        where: { id: contactId },
        relations: [
          'leads',
          'leads.program_interests',
          'leads.program_interests.lead_level',
        ],
      });
      if (!contact) {
        throw new NotFoundException(`Contact with ID ${contactId} not found`);
      }
      const hasEnrolledProgramInterest = contact.leads.some((lead) =>
        lead.program_interests.some(
          (programInterest) =>
            programInterest.lead_level?.enrollment_stage ===
            EnrollmentStage.Enrolled,
        ),
      );
      if (hasEnrolledProgramInterest) {
        throw new BadRequestException(
          'Cannot update contact name as there are enrolled program interests associated with this contact',
        );
      }
      // Store old contact data before updating
      const oldContact = { ...contact };
      // Update the contact's name
      contact.first_name = firstName;
      if (lastName) {
        contact.last_name = lastName;
      }
      contact.updated_by = userId;
      const updatedContact = await this.contactRepository.save(contact);
      // Log the update
      this.logger.log(
        `Updated contact name for contact ID ${contactId}: ${firstName} ${lastName}`,
      );
      // Create contact history when contact name is updated
      await this.createContactHistory(
        ContactUpdationSubActionType.UPDATE,
        oldContact,
        updatedContact,
        userId,
        { firstName, lastName, contactId },
      );
      return updatedContact;
    } catch (error) {
      this.logger.error(
        `Error updating contact name for contact ${contactId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Retrieves an email by its ID
   * @param emailId The ID of the email to retrieve
   * @param userId The ID of the user making the request (for audit purposes)
   * @returns The email object with contact relation
   * @throws NotFoundException if email is not found
   */
  async viewEmail(
    email: Email,
    userId: number,
    leadId?: number,
  ): Promise<Email> {
    try {
      // Log the view action for audit purposes
      this.logger.log(`User ${userId} viewed email with ID ${email.id}`);
      // getting counts from mongodb
      const total_view_count =
        await this.leadHistoryService.getEmailPhoneViewedCounts(
          EngagementSubActionType.EMAIL_VIEWED,
          userId,
          email.id,
        );
      const details = {
        email: email.masked_email,
        emailId: email.id,
        userId,
        total_view_count: total_view_count + 1,
      };
      //create lead history for email view
      this.createPhoneNumberorEmailViewedHistory(
        EngagementSubActionType.EMAIL_VIEWED,
        leadId, //leadId
        email.contact?.id,
        userId,
        details,
      );
      return email;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.logger.error(
        `Error retrieving email with ID ${email.id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to retrieve email with ID ${email.id}`);
    }
  }

  /**
   * Retrieves a phone by its ID
   * @param phoneId The ID of the phone to retrieve
   * @param userId The ID of the user making the request (for audit purposes)
   * @returns The phone object with contact relation
   * @throws NotFoundException if phone is not found
   */
  async viewPhone(
    phone: Phone,
    userId: number,
    leadId?: number,
  ): Promise<Phone> {
    try {
      // Log the view action for audit purposes
      this.logger.log(`User ${userId} viewed phone with ID ${phone.id}`);
      // getting counts from mongodb
      const total_view_count =
        await this.leadHistoryService.getEmailPhoneViewedCounts(
          EngagementSubActionType.PHONE_NUMBER_VIEWED,
          userId,
          phone.id,
        );
      const details = {
        phoneNumber: phone.masked_phone_number,
        total_view_count: total_view_count + 1,
        phoneId: phone.id,
        userId,
      };
      //create lead history for phone number view
      this.createPhoneNumberorEmailViewedHistory(
        EngagementSubActionType.PHONE_NUMBER_VIEWED,
        leadId, //leadId
        phone.contact?.id,
        userId,
        details,
      );
      return phone;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.logger.error(
        `Error retrieving phone with ID ${phone.id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to retrieve phone with ID ${phone.id}`);
    }
  }

  /*
    Helper method to create phone number or email viewed history
  */
  async createPhoneNumberorEmailViewedHistory(
    subActionType: EngagementSubActionType,
    leadId: number,
    contactId: number,
    userId: number,
    details: any,
  ): Promise<void> {
    try {
      const historyData: any = {
        action: ActionType.ENGAGEMENT,
        subAction: subActionType,
        leadId,
        contactId,
        performedByUser: (await this.userService.findOne(userId)) ?? null,
        performedBy: userId ?? null,
        details,
      };
      await this.leadHistoryService.createLeadHistory(historyData);
    } catch (error) {
      this.logger.error(
        `Error creating phone number or email viewed history: ${error.message}`,
        error.stack,
      );
    }
  }

  async getContactFromEmailId(emailId: number): Promise<Email> {
    try {
      const email = await this.emailRepository.findOne({
        where: { id: emailId },
        relations: ['contact'],
      });

      if (!email) {
        throw new NotFoundException(`Email with ID ${emailId} not found`);
      }

      return email;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error retrieving contact from email ID ${emailId}: ${error.message}`,
        error.stack,
      );
    }
  }

  async getContactFromPhoneId(phoneId: number): Promise<Phone> {
    try {
      const phone = await this.phoneRepository.findOne({
        where: { id: phoneId },
        relations: ['contact'],
      });

      if (!phone) {
        throw new NotFoundException(`Phone with ID ${phoneId} not found`);
      }

      return phone;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error retrieving contact from phone ID ${phoneId}: ${error.message}`,
        error.stack,
      );
    }
  }
}
