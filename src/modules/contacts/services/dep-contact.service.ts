import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Contact } from '../entities/contact.entity';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { Email } from '../entities/email.entity';
import { Phone } from '../entities/phone.entity';
import { Education } from '../entities/education.entity';
import { WorkExperience } from '../entities/work-experience.entity';

@Injectable()
/* //!Deprecated - Use the ContactService instead.

  Logic for new lead according to this LOGIC : 
    Either email or phone is new, create a new contact and link the old one as parent
  
*/
export class DepContactService {
  constructor(
    @InjectRepository(Contact)
    private contactRepository: Repository<Contact>,
    @InjectRepository(Email)
    private emailRepository: Repository<Email>,
    @InjectRepository(Phone)
    private phoneRepository: Repository<Phone>,
    @InjectRepository(Education)
    private educationRepository: Repository<Education>,
    @InjectRepository(WorkExperience)
    private workExperienceRepository: Repository<WorkExperience>,
  ) {}

  async handleContactDetails(
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    return this.findOrCreateContactWithRelationships(createContactDto, userId);
  }

  async findOrCreateContactWithRelationships(
    createContactDto: CreateContactDto,
    userId: number,
  ): Promise<Contact> {
    // Check if there's any existing contact with the same email or phone
    let emailContactId: number = null;
    let phoneContactId: number = null;
    let existingEmailFound = false;
    let existingPhoneFound = false;

    const dtoEmail = createContactDto.email?.email;
    const dtoPhoneNumber = createContactDto.phone?.phone_number;

    if (!dtoEmail && !dtoPhoneNumber) {
      throw new BadRequestException(
        'Either email or phone number must be provided',
      );
    }

    // Check if email exists
    if (dtoEmail) {
      const existingEmail = await this.emailRepository.findOne({
        where: { email: dtoEmail },
        relations: ['contact'],
      });

      if (existingEmail) {
        emailContactId = existingEmail.contact.id;
        existingEmailFound = true;
      }
    }

    // Check if phone exists
    if (dtoPhoneNumber) {
      // Standardize the phone number for comparison by removing any non-digit characters
      const standardizedPhone = createContactDto.phone.phone_number.replace(
        /\D/g,
        '',
      );

      const existingPhone = await this.phoneRepository.findOne({
        where: { phone_number: standardizedPhone },
        relations: ['contact'],
      });

      if (existingPhone) {
        phoneContactId = existingPhone.contact.id;
        existingPhoneFound = true;
      }
    }

    // Determine if we need to create a new contact
    const createNewContact =
      (dtoEmail && !existingEmailFound) ||
      (dtoPhoneNumber && !existingPhoneFound);

    let parentContactId = null;

    if (existingEmailFound) {
      // If only email exists, use that contact as parent
      parentContactId = emailContactId;
    } else if (existingPhoneFound) {
      // If only phone exists, use that contact as parent
      parentContactId = phoneContactId;
    }

    if (createNewContact) {
      // Create new contact
      const contact = new Contact();
      Object.assign(contact, {
        first_name: createContactDto.first_name,
        last_name: createContactDto.last_name,
        city: createContactDto.city,
        candidate_id: createContactDto.candidate_id,
        miles_office_id: createContactDto.miles_office_id,
        parentContactId: parentContactId, // Set parent contact if one was found
        created_by: userId,
        updated_by: userId,
      });

      // Save the contact first to get ID
      const savedContact = await this.contactRepository.save(contact);
      const newContactId = savedContact.id;

      // Create email if provided and unique
      if (dtoEmail && !existingEmailFound) {
        const email = new Email();
        Object.assign(email, {
          ...createContactDto.email,
          contactId: newContactId,
          created_by: userId,
          updated_by: userId,
        });
        await this.emailRepository.save(email);
      }

      // Create phone if provided and unique
      if (dtoPhoneNumber && !existingPhoneFound) {
        const phone = new Phone();
        // Standardize phone number by removing any non-digit characters
        const standardizedPhone = createContactDto.phone.phone_number.replace(
          /\D/g,
          '',
        );

        Object.assign(phone, {
          ...createContactDto.phone,
          phoneNumber: standardizedPhone,
          contactId: newContactId,
          created_by: userId,
          updated_by: userId,
        });
        await this.phoneRepository.save(phone);
      }

      // Create education records
      if (
        createContactDto.educations &&
        createContactDto.educations.length > 0
      ) {
        for (const educationDto of createContactDto.educations) {
          const education = new Education();
          Object.assign(education, {
            ...educationDto,
            contactId: newContactId,
            created_by: userId,
            updated_by: userId,
          });
          await this.educationRepository.save(education);
        }
      }

      // Create work experience records
      if (
        createContactDto.workExperiences &&
        createContactDto.workExperiences.length > 0
      ) {
        for (const workExperienceDto of createContactDto.workExperiences) {
          const workExperience = new WorkExperience();
          Object.assign(workExperience, {
            ...workExperienceDto,
            contactId: newContactId,
            created_by: userId,
            updated_by: userId,
          });
          await this.workExperienceRepository.save(workExperience);
        }
      }

      // Return the newly created contact
      return this.findOne(newContactId);
    } else {
      // Both email and phone were found in existing contacts
      let contactToReturn = emailContactId || phoneContactId;

      // Determine if we have a conflict (email and phone belong to different contacts)
      const hasConflict =
        existingEmailFound &&
        existingPhoneFound &&
        emailContactId !== phoneContactId;

      if (hasConflict) {
        // Retrieve both contacts to determine which one is older
        const emailContact = await this.contactRepository.findOne({
          where: { id: emailContactId },
        });
        const phoneContact = await this.contactRepository.findOne({
          where: { id: phoneContactId },
        });

        if (!emailContact || !phoneContact) {
          throw new Error('One of the conflicting contacts could not be found');
        }

        // Determine which contact is older (assuming lower id means created earlier)
        const parentContact =
          emailContact.id < phoneContact.id ? emailContact : phoneContact;
        const childContact =
          emailContact.id < phoneContact.id ? phoneContact : emailContact;

        // Update the child contact to have the parent relationship
        childContact.parentContactId = parentContact.id;
        await this.contactRepository.save(childContact);

        // Set contactToReturn to the parent contact's id
        contactToReturn = parentContact.id;
      }

      if (!contactToReturn) {
        throw new Error(
          'Could not determine existing contact for the provided email or phone',
        );
      }

      return this.findOne(contactToReturn);
    }
  }

  async findOne(id: number): Promise<Contact> {
    const contact = await this.contactRepository.findOne({
      where: { id },
      relations: [
        'emails',
        'phones',
        'educations',
        'workExperiences',
        'parentContact',
        'childContacts',
      ],
    });

    if (!contact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    return contact;
  }

  async remove(id: number): Promise<void> {
    const result = await this.contactRepository.delete({
      id,
    });

    if (result.affected === 0) {
      throw new NotFoundException(
        `Contact with ID ${id} not found for this client`,
      );
    }
  }

  /**
   * Updates an existing contact with new information, handling email/phone relationships
   * @param id The ID of the contact to update
   * @param updateContactDto The updated contact information
   * @param userId The ID of the user making the update
   * @returns The updated contact with all relations
   */
  async updateContact(
    id: number,
    updateContactDto: UpdateContactDto,
    userId: number,
  ): Promise<Contact> {
    // First, verify the contact exists
    const existingContact = await this.contactRepository.findOne({
      where: { id },
      relations: ['emails', 'phones', 'educations', 'workExperiences'],
    });

    if (!existingContact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    // Update basic contact info
    if (updateContactDto.first_name !== undefined) {
      existingContact.first_name = updateContactDto.first_name;
    }

    if (updateContactDto.last_name !== undefined) {
      existingContact.last_name = updateContactDto.last_name;
    }

    if (updateContactDto.city !== undefined) {
      existingContact.city = updateContactDto.city;
    }

    if (updateContactDto.candidate_id !== undefined) {
      existingContact.candidate_id = updateContactDto.candidate_id;
    }

    if (updateContactDto.miles_office_id !== undefined) {
      existingContact.miles_office_id = updateContactDto.miles_office_id;
    }

    existingContact.updated_by = userId;

    // Save the basic contact changes
    const updatedContact = await this.contactRepository.save(existingContact);

    // Handle email updates if provided
    if (updateContactDto.email) {
      const dtoEmail = updateContactDto.email.email;

      // Check if this email already exists on another contact
      const existingEmail = await this.emailRepository.findOne({
        where: { email: dtoEmail },
        relations: ['contact'],
      });

      if (existingEmail && existingEmail.contact_id !== id) {
        // Email exists but belongs to a different contact
        // Handle potential parent-child relationship
        const parentContactId = existingEmail.contact_id;

        // Update the contact to set the found contact as parent if it's not already
        if (updatedContact.parentContactId !== parentContactId) {
          updatedContact.parentContactId = parentContactId;
          await this.contactRepository.save(updatedContact);
        }
      } else if (!existingEmail) {
        // Email doesn't exist, create it for this contact
        const email = new Email();
        Object.assign(email, {
          ...updateContactDto.email,
          contactId: id,
          contact: updatedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.emailRepository.save(email);
      } else {
        // Email exists and belongs to this contact, update it
        existingEmail.label =
          updateContactDto.email.label || existingEmail.label;
        existingEmail.is_active =
          updateContactDto.email.is_active ?? existingEmail.is_active;
        existingEmail.is_masked =
          updateContactDto.email.is_masked ?? existingEmail.is_masked;
        existingEmail.updated_by = userId;
        await this.emailRepository.save(existingEmail);
      }
    }

    // Handle phone updates if provided
    if (updateContactDto.phone) {
      const dtoPhoneNumber = updateContactDto.phone.phone_number;
      // Standardize the phone number
      const standardizedPhone = dtoPhoneNumber.replace(/\D/g, '');

      // Check if this phone already exists on another contact
      const existingPhone = await this.phoneRepository.findOne({
        where: { phone_number: standardizedPhone },
        relations: ['contact'],
      });

      if (existingPhone && existingPhone.contact_id !== id) {
        // Phone exists but belongs to a different contact
        // Handle potential parent-child relationship
        const parentContactId = existingPhone.contact_id;

        // Update the contact to set the found contact as parent if it's not already
        if (updatedContact.parentContactId !== parentContactId) {
          updatedContact.parentContactId = parentContactId;
          await this.contactRepository.save(updatedContact);
        }
      } else if (!existingPhone) {
        // Phone doesn't exist, create it for this contact
        const phone = new Phone();
        Object.assign(phone, {
          ...updateContactDto.phone,
          phoneNumber: standardizedPhone,
          contactId: id,
          contact: updatedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.phoneRepository.save(phone);
      } else {
        // Phone exists and belongs to this contact, update it
        existingPhone.label =
          updateContactDto.phone.label || existingPhone.label;
        existingPhone.country_code =
          updateContactDto.phone.country_code || existingPhone.country_code;
        existingPhone.updated_by = userId;
        await this.phoneRepository.save(existingPhone);
      }
    }

    // Handle educations if provided
    if (updateContactDto.educations && updateContactDto.educations.length > 0) {
      for (const educationDto of updateContactDto.educations) {
        const education = new Education();
        Object.assign(education, {
          ...educationDto,
          contactId: id,
          contact: updatedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.educationRepository.save(education);
      }
    }

    // Handle work experiences if provided
    if (
      updateContactDto.workExperiences &&
      updateContactDto.workExperiences.length > 0
    ) {
      for (const workExperienceDto of updateContactDto.workExperiences) {
        const workExperience = new WorkExperience();
        Object.assign(workExperience, {
          ...workExperienceDto,
          contactId: id,
          contact: updatedContact,
          created_by: userId,
          updated_by: userId,
        });
        await this.workExperienceRepository.save(workExperience);
      }
    }

    // Return the updated contact with all relations
    return this.findOne(id);
  }
}
