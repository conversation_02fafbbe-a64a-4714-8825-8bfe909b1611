import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  Index,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
  ManyToOne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { Email } from './email.entity';
import { Phone } from './phone.entity';
import { WorkExperience } from './work-experience.entity';
import { Education } from './education.entity';
import { MilesOffice } from 'src/modules/miles-offices/entities/miles-office.entity';
import { UUID } from 'crypto';
import { Lead } from '@modules/leads/entities/lead.entity';

//? This is a central table without any client association
@Entity()
export class Contact extends BaseEntity {
  @Column({ type: 'uuid', default: () => 'gen_random_uuid()' })
  contact_uuid: UUID;

  @Index()
  @Column({ nullable: true, unique: true })
  candidate_id: string;

  @Column({ type: 'varchar', nullable: true })
  first_name: string;

  @Column({ type: 'varchar', nullable: true })
  last_name: string;

  @Column({ type: 'varchar', length: 100 })
  full_name: string;

  @Column({ type: 'varchar', nullable: true })
  city: string;

  @Column({ type: 'varchar', nullable: true })
  num_verify_location: string;

  @Index()
  @ManyToOne(() => MilesOffice)
  @JoinColumn({ name: 'miles_office_id' })
  miles_office: MilesOffice;

  @Column({ type: 'int', nullable: true })
  miles_office_id: number;

  @OneToOne(() => Email)
  @JoinColumn({ name: 'primary_email_id' })
  primary_email: Email;

  @Column({ type: 'int', nullable: true })
  primary_email_id: number;

  @OneToOne(() => Phone)
  @JoinColumn({ name: 'primary_phone_id' })
  primary_phone: Phone;

  @Column({ type: 'int', nullable: true })
  primary_phone_id: number;

  @OneToMany(() => Email, (email) => email.contact, {
    cascade: true,
    eager: false,
  })
  emails: Email[];

  @OneToMany(() => Phone, (phone) => phone.contact, {
    cascade: true,
    eager: false,
  })
  phones: Phone[];

  @OneToMany(() => WorkExperience, (workExperience) => workExperience.contact, {
    cascade: true,
    eager: false,
  })
  workExperiences: WorkExperience[];

  @OneToMany(() => Education, (education) => education.contact, {
    cascade: true,
    eager: false,
  })
  educations: Education[];

  // Parent-child relationship for contacts
  @Column({ type: 'integer', nullable: true })
  parentContactId: number;

  @ManyToOne(() => Contact, (contact) => contact.childContacts)
  @JoinColumn({ name: 'parentContactId' })
  parentContact: Contact;

  @OneToMany(() => Contact, (contact) => contact.parentContact)
  childContacts: Contact[];

  @OneToMany(() => Lead, (lead) => lead.contact)
  leads: Lead[];

  @BeforeInsert()
  @BeforeUpdate()
  generateFullName() {
    if (this.first_name || this.last_name) {
      const first = this.first_name ? this.capitalize(this.first_name) : '';
      const last = this.last_name ? this.capitalize(this.last_name) : '';
      this.full_name = [first, last].filter(Boolean).join(' ');
    }
  }

  private capitalize(text: string): string {
    if (!text || text.length === 0) return '';
    return (
      text.trim().charAt(0).toUpperCase() + text.trim().slice(1).toLowerCase()
    );
  }
}
