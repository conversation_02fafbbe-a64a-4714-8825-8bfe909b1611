import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity, ManyToOne, JoinColumn, BeforeInsert } from 'typeorm';
import { Contact } from './contact.entity';
import { PhoneType } from '../enums/contact.enum';

@Entity()
export class Phone extends BaseEntity {
  @Column({ type: 'varchar', length: 50, unique: true })
  phone_number: string;

  @Column({ type: 'varchar', length: 10, default: '+1' })
  country_code: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  label: string; // e.g., 'Mobile', 'Home', 'Work', 'Other'

  @Column({ type: 'varchar', length: 50, nullable: true })
  masked_phone_number: string;

  @Column({ type: 'integer', nullable: true })
  contact_id: number;

  @ManyToOne(() => Contact, { onDelete: 'CASCADE', nullable: true })
  @JoinColumn({ name: 'contact_id' })
  contact: Contact;

  @Column({
    type: 'enum',
    enum: PhoneType,
    nullable: true,
    default: PhoneType.lead,
  })
  phone_type: PhoneType;

  @BeforeInsert()
  beforeInsert() {
    if (!this.masked_phone_number && this.phone_number) {
      this.masked_phone_number =
        this.country_code + this.phone_number.replace(/.(?=.{3})/g, '*');
    }
  }
}
