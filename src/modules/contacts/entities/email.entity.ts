import { BaseEntity } from 'src/common/entities/base.entity';
import { Colum<PERSON>, Entity, ManyToOne, JoinColum<PERSON>, BeforeInsert } from 'typeorm';
import { Contact } from './contact.entity';

@Entity()
export class Email extends BaseEntity {
  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  label: string; // e.g., 'Personal', 'Work', 'Other'

  @Column({ type: 'integer' })
  contact_id: number;

  @ManyToOne(() => Contact, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contact_id' })
  contact: Contact;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'boolean', default: false })
  is_masked: boolean;

  @Column({ type: 'varchar', nullable: true })
  masked_email: string;

  @BeforeInsert()
  beforeInsert() {
    if (!this.masked_email) {
      const [username, domain] = this.email.split('@');
      if (username && domain) {
        this.masked_email = `${username.slice(0, 2)}****@${domain}`;
      }
    }
  }
}
