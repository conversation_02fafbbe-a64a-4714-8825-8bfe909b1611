import { IsBoolean, IsEmail, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEmailDto {
  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Label for the email, e.g., work, personal',
  })
  @IsOptional()
  @IsString()
  label?: string;

  @ApiPropertyOptional({ description: 'Whether the email is active' })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the email is masked for privacy',
  })
  @IsOptional()
  @IsBoolean()
  is_masked?: boolean;
}
