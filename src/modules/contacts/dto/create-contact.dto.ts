import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON>y,
  IsInt,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { CreateEmailDto } from './create-email.dto';
import { CreatePhoneDto } from './create-phone.dto';
import { CreateEducationDto } from './create-education.dto';
import { CreateWorkExperienceDto } from './create-work-experience.dto';

export class CreateContactDto {
  @ApiPropertyOptional({ description: 'First name of the contact' })
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiPropertyOptional({ description: 'Last name of the contact' })
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiPropertyOptional({ description: 'Candidate identifier' })
  @IsOptional()
  @IsString()
  candidate_id?: string;

  @ApiPropertyOptional({ description: 'City of the contact' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: 'Miles office ID associated with the contact',
  })
  @IsOptional()
  @IsInt()
  miles_office_id?: number;

  @ApiProperty({
    type: () => CreateEmailDto,
    description: 'Email details of the contact',
  })
  @ValidateNested()
  @Type(() => CreateEmailDto)
  email: CreateEmailDto;

  @ApiProperty({
    type: () => CreatePhoneDto,
    description: 'Phone details of the contact',
  })
  @ValidateNested()
  @Type(() => CreatePhoneDto)
  phone: CreatePhoneDto;

  @ApiPropertyOptional({
    type: () => [CreateEducationDto],
    description: 'List of education records',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEducationDto)
  educations?: CreateEducationDto[];

  @ApiPropertyOptional({
    type: () => [CreateWorkExperienceDto],
    description: 'List of work experience records',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateWorkExperienceDto)
  workExperiences?: CreateWorkExperienceDto[];
}
