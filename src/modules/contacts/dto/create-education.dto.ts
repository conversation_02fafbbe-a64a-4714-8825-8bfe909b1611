import { IsBoolean, IsDateString, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateEducationDto {
  @ApiProperty({ description: 'Name of the educational institution' })
  @IsString()
  institution: string;

  @ApiPropertyOptional({ description: 'Degree obtained or pursued' })
  @IsOptional()
  @IsString()
  degree?: string;

  @ApiPropertyOptional({ description: 'Field of study or major' })
  @IsOptional()
  @IsString()
  field_of_study?: string;

  @ApiPropertyOptional({
    description: 'Start date of the education period',
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({
    description: 'End date of the education period',
    type: String,
    format: 'date',
  })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiPropertyOptional({
    description: 'Indicates if the education is currently ongoing',
  })
  @IsOptional()
  @IsBoolean()
  is_current?: boolean;

  @ApiPropertyOptional({
    description: 'Additional description or notes about the education',
  })
  @IsOptional()
  @IsString()
  description?: string;
}
