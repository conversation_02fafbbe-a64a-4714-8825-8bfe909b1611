import { IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreatePhoneDto {
  @ApiProperty({ description: 'Phone number' })
  @IsString()
  phone_number: string;

  @ApiPropertyOptional({ description: 'Country code of the phone number' })
  @IsOptional()
  @IsString()
  country_code?: string;

  @ApiPropertyOptional({
    description: 'Label for the phone number, e.g., home, work',
  })
  @IsOptional()
  @IsString()
  label?: string;
}
