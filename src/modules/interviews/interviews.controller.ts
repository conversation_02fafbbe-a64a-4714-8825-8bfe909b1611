import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { InterviewsService } from './interviews.service';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { FilterInterviewDto } from './dto/filter-interview.dto';
import { CompleteInterviewDto } from './dto/complete-interview.dto';
import { CancelInterviewDto } from './dto/cancel-interview.dto';
import { RescheduleInterviewDto } from './dto/reschedule-interview.dto';
import { Interview } from './entities/interview.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';

@ApiTags('interviews')
@ApiBearerAuth()
@Controller('interviews')
export class InterviewsController {
  constructor(private readonly interviewsService: InterviewsService) {}

  @Post('add')
  @ApiOperation({ summary: 'Create a new interview' })
  @ApiResponse({
    status: 201,
    description: 'Interview created successfully',
    type: Interview,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  create(
    @Body() createInterviewDto: CreateInterviewDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.interviewsService.create(createInterviewDto, req.user);
  }

  @Get('get-all')
  @ApiOperation({ summary: 'Get all interviews with filtering and pagination' })
  @ApiResponse({
    status: 200,
    description: 'Interviews retrieved successfully',
  })
  findAll(
    @Query() filterDto: FilterInterviewDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;

    return this.interviewsService.findAll(filterDto, clientId);
  }

  @Get('get/:id')
  @ApiOperation({ summary: 'Get interview by ID' })
  @ApiResponse({
    status: 200,
    description: 'Interview retrieved successfully',
    type: Interview,
  })
  @ApiResponse({ status: 404, description: 'Interview not found' })
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;

    return this.interviewsService.findOne(id, clientId);
  }

  @Get('get-for-lead-interest/:lead_id')
  @ApiOperation({ summary: 'Get interview by ID' })
  @ApiResponse({
    status: 200,
    description: 'Interview retrieved successfully',
    type: Interview,
  })
  @ApiResponse({ status: 404, description: 'Interview not found' })
  findOneForLead(
    @Param('lead_id', ParseIntPipe) leadId: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;

    return this.interviewsService.findOneForLead(leadId, clientId);
  }

  @Post('complete')
  @ApiOperation({ summary: 'Complete an interview with proof attachment' })
  @ApiResponse({
    status: 200,
    description: 'Interview completed successfully',
    type: Interview,
  })
  @ApiResponse({ status: 404, description: 'Interview not found' })
  completeInterview(
    @Body() completeInterviewDto: CompleteInterviewDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.interviewsService.completeInterview(
      completeInterviewDto,
      req.user,
    );
  }

  @Post('cancel')
  @ApiOperation({ summary: 'Cancel an interview' })
  @ApiResponse({
    status: 200,
    description: 'Interview cancelled successfully',
    type: Interview,
  })
  @ApiResponse({ status: 404, description: 'Interview not found' })
  cancelInterview(
    @Body() cancelInterviewDto: CancelInterviewDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.interviewsService.cancelInterview(cancelInterviewDto, req.user);
  }

  @Post('reschedule')
  @ApiOperation({ summary: 'Reschedule an interview' })
  @ApiResponse({
    status: 200,
    description: 'Interview rescheduled successfully',
    type: Interview,
  })
  @ApiResponse({ status: 404, description: 'Interview not found' })
  rescheduleInterview(
    @Body() rescheduleInterviewDto: RescheduleInterviewDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.interviewsService.rescheduleInterview(
      rescheduleInterviewDto,
      req.user,
    );
  }

  @Get('get-counsellors')
  @ApiOperation({ summary: 'Get all interviews counsellors' })
  @ApiResponse({
    status: 200,
    description: 'Counsellors retrieved successfully',
  })
  getAllCounsellors(@Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;
    return this.interviewsService.getInterviewCouncellor(clientId);
  }
}
