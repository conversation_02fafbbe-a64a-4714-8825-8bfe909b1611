import { ValidateBy } from 'class-validator';
import { ValidationOptions } from 'class-validator/types/decorator/ValidationOptions';

export function IsFutureDate(validationOptions?: ValidationOptions) {
  return ValidateBy(
    {
      name: 'isFutureDate',
      validator: {
        validate: (value: any) => {
          if (!value) return false;
          const inputDate = new Date(value);
          const now = new Date();
          return inputDate > now;
        },
        defaultMessage: () => 'Interview date must be in the future',
      },
    },
    validationOptions,
  );
}
