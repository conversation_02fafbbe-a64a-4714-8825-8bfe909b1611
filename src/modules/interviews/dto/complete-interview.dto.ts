import {
  IsInt,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CompleteInterviewDto {
  @ApiProperty({
    description: 'Interview ID for the completed interview',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'Proof attachment ID for the completed interview',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  proof_attachment_id: number;

  @ApiPropertyOptional({
    description: 'Additional comments about the interview completion',
    example: 'Interview completed successfully. Candidate performed well.',
  })
  @IsString()
  @IsOptional()
  comment?: string;

  @ApiPropertyOptional({
    description: 'Duration of the interview in seconds',
    example: '1800',
  })
  @IsNumber()
  @IsOptional()
  duration?: number;
}
