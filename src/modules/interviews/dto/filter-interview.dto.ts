import {
  IsOptional,
  IsEnum,
  IsDateString,
  <PERSON>Int,
  IsN<PERSON>ber,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { InterviewStatus } from '../enums/interview-status.enum';

export class FilterInterviewDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Filter by lead ID',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  lead_id?: number;

  @ApiPropertyOptional({
    description: 'Filter by assigned user ID',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  assigned_user_id?: number;

  @ApiPropertyOptional({
    description: 'Filter by university  ID',
    example: 1,
  })
  @IsOptional()
  @IsInt()
  university?: number;

  @ApiPropertyOptional({
    enum: InterviewStatus,
    description: 'Filter by interview status',
  })
  @IsEnum(InterviewStatus)
  @IsOptional()
  interview_status?: InterviewStatus;

  @ApiPropertyOptional({
    description: 'Filter interviews from this date (ISO string)',
    example: '2024-01-01T00:00:00Z',
  })
  @IsDateString()
  @IsOptional()
  date_from?: string;

  @ApiPropertyOptional({
    description: 'Filter interviews until this date (ISO string)',
    example: '2024-12-31T23:59:59Z',
  })
  @IsDateString()
  @IsOptional()
  date_to?: string;
}
