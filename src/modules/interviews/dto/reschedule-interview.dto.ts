import {
  IsString,
  IsNotEmpty,
  IsDateString,
  ValidateBy,
  ValidationOptions,
  IsInt,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsFutureDate } from '../utils/is-future-date.function';

// Custom validator for future dates

export class RescheduleInterviewDto {
  @ApiProperty({
    description: 'Interview ID for the rescheduling',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description:
      'New date and time for the rescheduled interview (must be in the future)',
    example: '2024-01-20T14:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  @IsFutureDate()
  new_date_time: string;

  @ApiProperty({
    description: 'Required comment explaining the reschedule reason',
    example: 'Interview rescheduled due to interviewer unavailability',
  })
  @IsString()
  @IsNotEmpty()
  comment: string;
}
