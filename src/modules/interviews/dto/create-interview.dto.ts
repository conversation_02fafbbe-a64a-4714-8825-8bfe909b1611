import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsInt,
  IsDateString,
  ValidateBy,
  ValidationOptions,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsFutureDate } from '../utils/is-future-date.function';
export class CreateInterviewDto {
  @ApiProperty({
    description: 'Lead ID for the interview',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  lead_program_interest_id: number;

  @ApiPropertyOptional({
    description: 'Assigned user ID for the interview',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  assigned_user_id?: number;

  @ApiProperty({
    description: 'University ID for the interview',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  university_id: number;

  @ApiProperty({
    description: 'Date and time of the interview (must be in the future)',
    example: '2024-01-15T10:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  @IsFutureDate()
  date_time: string;
}
