import { IsString, IsNotEmpty, IsInt } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CancelInterviewDto {
  @ApiProperty({
    description: 'Interview ID for the cancellation',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'Required comment explaining the cancellation reason',
    example: 'Interview cancelled due to candidate unavailability',
  })
  @IsString()
  @IsNotEmpty()
  comment: string;
}
