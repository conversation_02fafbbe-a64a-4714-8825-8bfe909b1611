import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Interview } from './entities/interview.entity';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { FilterInterviewDto } from './dto/filter-interview.dto';
import { CompleteInterviewDto } from './dto/complete-interview.dto';
import { CancelInterviewDto } from './dto/cancel-interview.dto';
import { RescheduleInterviewDto } from './dto/reschedule-interview.dto';
import { InterviewStatus } from './enums/interview-status.enum';
import { InterviewHistorySubActionType } from './enums/interview-history-subactions.enum';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { AttachmentsService } from '@modules/attachments/attachments.service';
import {
  CalendarEventData,
  GoogleCalendarService,
} from 'src/common/service/google/google-calendar.service';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { UserService } from '@modules/users/services/user.service';
import { University } from '@modules/applications/entities/university.entity';
import { AppConfig } from 'src/config/app.config';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class InterviewsService {
  constructor(
    private configService: ConfigService,
    @InjectRepository(Interview)
    private readonly interviewRepository: Repository<Interview>,
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(LeadProgramInterest)
    private readonly leadProgramInterstRepository: Repository<LeadProgramInterest>,
    @InjectRepository(University)
    private readonly universityRepository: Repository<University>,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly attachmentService: AttachmentsService,
    private readonly googleCalendarService: GoogleCalendarService,
    private readonly userService: UserService,
  ) {}

  private localCounsellorEmails = ['<EMAIL>'];
  private preproductionCounsellorEmails = ['<EMAIL>'];
  private productionCounsellorEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  async create(createInterviewDto: CreateInterviewDto, user: Express.User) {
    const leadProgram = await this.leadProgramInterstRepository.findOne({
      where: { id: createInterviewDto.lead_program_interest_id },
      relations: {
        lead: { contact: { primary_email: true } },
      },
    });
    if (!leadProgram) {
      throw new NotFoundException(
        `Lead program interest not found for ID ${createInterviewDto.lead_program_interest_id}`,
      );
    }
    const assignedUser = await this.userService.findOne(
      createInterviewDto.assigned_user_id,
    );
    if (!assignedUser) {
      throw new NotFoundException(
        `Assigned user not found for ID ${createInterviewDto.assigned_user_id}`,
      );
    }
    const scheduledUser = await this.userService.findOne(user.id);
    if (!scheduledUser) {
      throw new NotFoundException(`Scheduled user not found for ID ${user.id}`);
    }
    const university = await this.universityRepository.findOne({
      where: { id: createInterviewDto.university_id },
    });
    if (!university) {
      throw new NotFoundException(
        `University not found for ID ${createInterviewDto.university_id}`,
      );
    }
    const interview = this.interviewRepository.create({
      ...createInterviewDto,
      client_id: user.currentClientId,
      created_by: user.id,
    });
    const attendeeEmails = [
      leadProgram?.lead?.contact?.primary_email?.email,
      assignedUser.email,
      scheduledUser.email,
    ];
    if (university.schedule_meet == true) {
      university.interviewers.forEach((interviewer: any) => {
        if (interviewer.email) {
          attendeeEmails.push(interviewer.email);
        }
      });
    }
    const leadName =
      `${leadProgram?.lead?.contact?.first_name || ''} ${
        leadProgram?.lead?.contact?.last_name || ''
      }`.trim() || 'Lead';
    const startDate = new Date(createInterviewDto.date_time);
    const endDate = new Date(startDate.getTime() + 1800 * 1000);
    const eventData: CalendarEventData = {
      summary: `Interview with ${leadName}`,
      description: '',
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString(),
      attendeesEmails: attendeeEmails,
      location: '',
    };
    const calendarResponse =
      await this.googleCalendarService.createCalendarEvent(eventData);
    // Update interview  with calendar event ID
    interview.event_id = calendarResponse.eventId;
    if (university.schedule_meet == true) {
      const googleMeetResposne =
        await this.googleCalendarService.createGoogleMeetEvent(eventData);
      // Update interview with google meet link
      interview.google_meet_link = googleMeetResposne.meetLink;
    }

    let createdInterview = await this.interviewRepository.save(interview);
    createdInterview = await this.interviewRepository.findOne({
      where: { id: createdInterview.id },
      relations: {
        lead_program_interest: {
          lead: true,
          program: true,
          lead_level: true,
        },
        assigned_user: true,
        university: true,
      },
    });
    this.createInterviewHistory(
      {
        date: createdInterview.date_time.toISOString(),
        duration: 0,
        counselledBy:
          createdInterview.assigned_user?.first_name ??
          '' + ' ' + createdInterview.assigned_user?.last_name ??
          '',
        counselledById: createdInterview.assigned_user?.id,
        counsellingLevel: '',
        university: createdInterview.university.university_name,
      },
      createdInterview.lead_program_interest?.lead?.id,
      user,
      InterviewHistorySubActionType.INTERVIEW_SCHEDULED,
      [
        {
          initial: createdInterview.lead_program_interest?.lead_level?.name,
          next: createdInterview.lead_program_interest?.lead_level?.name,
          type: createdInterview.lead_program_interest?.lead_level?.temperature,
          program: createdInterview.lead_program_interest?.program?.name,
          comment: createdInterview.lead_program_interest?.notes,
        },
      ],
    );
    return {
      success: true,
      message: 'Interview created successfully',
      data: createdInterview,
    };
  }

  async getInterviewCouncellor(clientId: number) {
    const appConfig = new AppConfig(this.configService);

    const counsellorIds = appConfig.environment.isDevelopment
      ? this.localCounsellorEmails
      : appConfig.environment.isProduction
        ? this.productionCounsellorEmails
        : this.preproductionCounsellorEmails;
    const counsellors = await this.userService.findByEmails(counsellorIds);
    return {
      data: counsellors,
      meta: null,
    };
  }

  async findAll(filterDto: FilterInterviewDto, clientId: number) {
    const {
      page = 1,
      size = 10,
      lead_id,
      assigned_user_id,
      university,
      interview_status,
      date_from,
      date_to,
    } = filterDto;

    const queryBuilder = this.interviewRepository
      .createQueryBuilder('interview')
      .leftJoinAndSelect('interview.lead', 'lead')
      .leftJoinAndSelect('interview.assigned_user', 'assigned_user')
      .where('interview.client_id = :clientId', { clientId });

    if (lead_id) {
      queryBuilder.andWhere('interview.lead_id = :lead_id', { lead_id });
    }

    if (assigned_user_id) {
      queryBuilder.andWhere('interview.assigned_user_id = :assigned_user_id', {
        assigned_user_id,
      });
    }

    if (university) {
      queryBuilder.andWhere('interview.university = :university', {
        university,
      });
    }

    if (interview_status) {
      queryBuilder.andWhere('interview.interview_status = :interview_status', {
        interview_status,
      });
    }

    if (date_from && date_to) {
      queryBuilder.andWhere(
        'interview.date_time BETWEEN :date_from AND :date_to',
        {
          date_from,
          date_to,
        },
      );
    } else if (date_from) {
      queryBuilder.andWhere('interview.date_time >= :date_from', { date_from });
    } else if (date_to) {
      queryBuilder.andWhere('interview.date_time <= :date_to', { date_to });
    }

    queryBuilder
      .orderBy('interview.date_time', 'DESC')
      .skip((page - 1) * size)
      .take(size);

    const [interviews, total] = await queryBuilder.getManyAndCount();

    return {
      data: interviews,
      total,
      page,
      size,
      totalPages: Math.ceil(total / size),
    };
  }

  async findOne(id: number, clientId: number): Promise<Interview> {
    const interview = await this.interviewRepository.findOne({
      where: { id, client_id: clientId },
      relations: {
        lead_program_interest: { lead: true, program: true, lead_level: true },
        assigned_user: true,
        university: true,
      },
    });

    if (!interview) {
      throw new NotFoundException(`Interview with ID ${id} not found`);
    }

    return interview;
  }

  async findOneForLead(leadId: number, clientId: number) {
    const interview = await this.interviewRepository.find({
      where: {
        lead_program_interest: { lead_id: leadId },
        client_id: clientId,
      },
      relations: {
        university: true,
        lead_program_interest: { program: true, lead: true },
        assigned_user: true,
      },
    });

    if (!interview) {
      throw new NotFoundException(`Interview with lead ID ${leadId} not found`);
    }

    return {
      data: interview,
      meta: null,
    };
  }

  async remove(id: number, clientId: number): Promise<void> {
    const interview = await this.findOne(id, clientId);
    await this.interviewRepository.remove(interview);
  }

  async completeInterview(
    completeInterviewDto: CompleteInterviewDto,
    user: Express.User,
  ) {
    try {
      const interview = await this.findOne(
        completeInterviewDto.id,
        user.currentClientId,
      );
      if (interview.interview_status === InterviewStatus.COMPLETED) {
        return {
          success: false,
          message: 'Interview is already completed',
          data: null,
        };
      } else if (interview.interview_status === InterviewStatus.CANCELLED) {
        return {
          success: false,
          message: 'Cancelled interview cannot be complete!',
          data: null,
        };
      } else if (interview.interview_status === InterviewStatus.RESCHEDULED) {
        return {
          success: false,
          message: 'Rescheduled interview cannot be complete!',
          data: null,
        };
      } else if (interview.interview_status === InterviewStatus.MISSED) {
        return {
          success: false,
          message: 'Missed interview cannot be complete!',
          data: null,
        };
      }
      Object.assign(interview, {
        duration: completeInterviewDto.duration,
        interview_status: InterviewStatus.COMPLETED,
        completed_at: new Date(),
        comment: completeInterviewDto.comment || interview.comment,
        attachment_id: completeInterviewDto.proof_attachment_id,
        updated_by: user.id,
      });

      await this.interviewRepository.save(interview);
      const attachment = await this.attachmentService.getPresignedSignedUrl(
        interview.attachment_id,
      );
      this.createInterviewHistory(
        {
          date: interview.date_time.toISOString(),
          duration: interview.duration,
          counselledBy:
            interview.assigned_user?.first_name ??
            '' + ' ' + interview.assigned_user?.last_name ??
            '',
          counselledById: interview.assigned_user_id,
          counsellingLevel: '',
          university: interview.university?.university_name,
          attachmentProof: attachment.url,
        },
        interview.lead_program_interest?.lead?.id,
        user,
        InterviewHistorySubActionType.INTERVIEW_COMPLETED,
        [
          {
            initial: interview.lead_program_interest?.lead_level?.name,
            next: interview.lead_program_interest?.lead_level?.name,
            type: interview.lead_program_interest?.lead_level?.temperature,
            program: interview.lead_program_interest?.program?.name,
            comment: interview.lead_program_interest?.notes,
          },
        ],
      );
      return {
        success: true,
        message: 'Interview completed successfully',
        data: interview,
      };
    } catch (e) {
      return {
        success: false,
        message: 'Interview completion failed',
        data: e,
      };
    }
  }

  async cancelInterview(
    cancelInterviewDto: CancelInterviewDto,
    user: Express.User,
  ) {
    try {
      const interview = await this.findOne(
        cancelInterviewDto.id,
        user.currentClientId,
      );

      Object.assign(interview, {
        interview_status: InterviewStatus.CANCELLED,
        comment: cancelInterviewDto.comment,
        is_active: false,
        status: false,
        updated_by: user.id,
      });
      this.googleCalendarService.deleteEvent(interview.event_id);
      await this.interviewRepository.save(interview);
      this.createInterviewHistory(
        {
          date: interview.date_time.toISOString(),
          duration: interview.duration || 0,
          reason: interview.comment,
          counselledBy:
            interview.assigned_user?.first_name ??
            '' + ' ' + interview.assigned_user?.last_name ??
            '',
          counselledById: interview.assigned_user_id,
          counsellingLevel: '',
          university: interview.university.university_name,
        },
        interview.lead_program_interest?.lead?.id,
        user,
        InterviewHistorySubActionType.INTERVIEW_CANCELLED,
        [
          {
            initial: interview.lead_program_interest?.lead_level?.name,
            next: interview.lead_program_interest?.lead_level?.name,
            type: interview.lead_program_interest?.lead_level?.temperature,
            program: interview.lead_program_interest?.program?.name,
            comment: interview.lead_program_interest?.notes,
          },
        ],
      );
      return {
        success: true,
        message: 'Interview cancelled successfully',
        data: interview,
      };
    } catch (e) {
      return {
        success: false,
        message: 'Interview cancellation failed',
        data: e,
      };
    }
  }

  async rescheduleInterview(
    rescheduleInterviewDto: RescheduleInterviewDto,
    user: Express.User,
  ) {
    try {
      const existingInterview = await this.findOne(
        rescheduleInterviewDto.id,
        user.currentClientId,
      );

      // Cancel the existing interview
      Object.assign(existingInterview, {
        interview_status: InterviewStatus.RESCHEDULED,
        comment: `Original interview cancelled for rescheduling. ${rescheduleInterviewDto.comment}`,
        is_active: false,
        status: false,
        updated_by: user.id,
      });

      await this.interviewRepository.save(existingInterview);

      // Create new interview with new date_time
      const newInterview = this.interviewRepository.create({
        lead_program_interest_id: existingInterview.lead_program_interest_id,
        assigned_user_id: existingInterview.assigned_user_id,
        university: existingInterview.university,
        date_time: new Date(rescheduleInterviewDto.new_date_time),
        interview_status: InterviewStatus.PENDING,
        comment: `Rescheduled interview. ${rescheduleInterviewDto.comment}`,
        attachment_id: null,
        client_id: user.currentClientId,
        created_by: user.id,
      });

      await this.interviewRepository.save(newInterview);
      this.createInterviewHistory(
        {
          date: newInterview.date_time.toISOString(),
          duration: 0,
          counselledBy:
            existingInterview.assigned_user?.first_name ??
            '' + ' ' + existingInterview.assigned_user?.last_name ??
            '',
          counselledById: existingInterview.assigned_user?.id,
          counsellingLevel: '',
          university: existingInterview.university.university_name,
        },
        existingInterview.lead_program_interest?.lead?.id,
        user,
        InterviewHistorySubActionType.INTERVIEW_RESCHEDULED,
        [
          {
            initial: existingInterview.lead_program_interest?.lead_level?.name,
            next: existingInterview.lead_program_interest?.lead_level?.name,
            type: existingInterview.lead_program_interest?.lead_level
              ?.temperature,
            program: existingInterview.lead_program_interest?.program?.name,
            comment: existingInterview.lead_program_interest?.notes,
          },
        ],
      );
      return {
        success: true,
        message: 'Interview rescheduled successfully',
        data: newInterview,
      };
    } catch (e) {
      return {
        success: false,
        message: 'Interview rescheduling failed',
        data: e,
      };
    }
  }

  async createInterviewHistory(
    details: any,
    leadId: number,
    user: Express.User,
    subAction: InterviewHistorySubActionType,
    levels: any[] = [],
  ) {
    const lead = await this.leadRepository.findOne({
      where: { id: leadId },
      relations: ['contact'],
    });
    let data = {
      action: 'appointment',
      subAction: subAction,
      leadId: lead?.id,
      contactId: lead?.contact?.id,
      details: details,
      performedBy: user.id,
      performedByUser: user,
      levels: levels,
    };
    this.leadHistoryService.createLeadHistory(data);
  }
}
