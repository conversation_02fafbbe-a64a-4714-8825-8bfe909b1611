import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InterviewsService } from './interviews.service';
import { InterviewsController } from './interviews.controller';
import { Interview } from './entities/interview.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadHistoryModule } from '@modules/lead-histories/lead-history.module';
import { AttachmentsModule } from '@modules/attachments/attachments.module';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { UserModule } from '@modules/users/user.module';
import { University } from '@modules/applications/entities/university.entity';
import { GoogleCalendarService } from 'src/common/service/google/google-calendar.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Interview,
      Lead,
      LeadProgramInterest,
      University,
    ]),
    LeadHistoryModule,
    AttachmentsModule,
    UserModule,
  ],
  controllers: [InterviewsController],
  providers: [InterviewsService, GoogleCalendarService],
  exports: [InterviewsService, GoogleCalendarService],
})
export class InterviewsModule {}
