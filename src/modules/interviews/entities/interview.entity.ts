import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { InterviewStatus } from '../enums/interview-status.enum';
// Type imports to avoid circular dependency
import type { User } from 'src/modules/users/entities/user.entity';
import { University } from '@modules/applications/entities/university.entity';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';

@Entity('interviews')
export class Interview extends ClientAwareEntity {
  @Index()
  @ManyToOne('LeadProgramInterest', { nullable: false })
  @JoinColumn({ name: 'lead_program_interest_id' })
  lead_program_interest: LeadProgramInterest;

  @Column({ type: 'integer', nullable: false })
  lead_program_interest_id: number;

  @Index()
  @ManyToOne('User', { nullable: true })
  @JoinColumn({ name: 'assigned_user_id' })
  assigned_user: User;

  @Column({ type: 'integer', nullable: true })
  assigned_user_id: number;

  @ManyToOne(() => University, { nullable: false })
  @JoinColumn({ name: 'university_id' })
  university: University;

  @Column({ type: 'integer', nullable: false })
  university_id: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: false,
  })
  date_time: Date;

  @Column({
    type: 'enum',
    enum: InterviewStatus,
    default: InterviewStatus.PENDING,
    nullable: false,
  })
  interview_status: InterviewStatus;

  @Column({
    type: 'text',
    nullable: true,
  })
  comment: string;

  @Column({
    type: 'integer',
    nullable: true,
  })
  duration: number;

  // @OneToOne(() => Attachment)
  // @JoinColumn({ name: 'attachment_id' })
  // attachments: Attachment;

  @Column({ type: 'integer', nullable: true })
  attachment_id: number;

  @Column({ type: 'text', nullable: true })
  event_id: string;

  @Column({ type: 'text', nullable: true })
  google_meet_link: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
  })
  completed_at: Date;
}
