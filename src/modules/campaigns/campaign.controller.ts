import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
} from '@nestjs/common';
import { CampaignService } from './services/campaign.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';

import { ApiTags, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import { Public } from '@modules/auth/decorators/public.decorator';

@ApiTags('campaigns')
@Controller('campaigns')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Public()
  @Post('add')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new campaign' })
  @ApiBody({ type: CreateCampaignDto })
  async create(
    @Body() createCampaignDto: CreateCampaignDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id || null;
    return this.campaignService.create(createCampaignDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get campaign by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Campaign ID' })
  async findOne(@Param('id') id: string) {
    return this.campaignService.findOne(+id);
  }

  @Public()
  @Get('/name/:name')
  @ApiParam({ name: 'name', type: String, description: 'Campaign Name' })
  @ApiOperation({ summary: 'Get campaign by Name' })
  async findOneByName(@Param('name') name: string) {
    return this.campaignService.findByName(name);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing campaign' })
  @ApiParam({ name: 'id', type: String, description: 'Campaign ID' })
  @ApiBody({ type: UpdateCampaignDto })
  async update(
    @Param('id') id: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id || null;
    return this.campaignService.update(+id, updateCampaignDto, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a campaign by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Campaign ID' })
  async remove(@Param('id') id: string) {
    return this.campaignService.remove(+id);
  }
}
