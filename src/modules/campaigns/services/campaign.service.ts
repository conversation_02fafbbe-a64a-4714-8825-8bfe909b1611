import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Campaign } from '../entities/campaign.entity';
import { CreateCampaignDto } from '../dto/create-campaign.dto';
import { UpdateCampaignDto } from '../dto/update-campaign.dto';
import { LeadSourceService } from '@modules/lead-allocations/services/lead-source.service';

export enum CampaignType {
  OUTREACH = 1,
  REMARKETING = 2,
  AWARENESS = 3,
}

@Injectable()
export class CampaignService {
  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,

    private readonly leadSourceService: LeadSourceService,
  ) {}

  async create(
    createCampaignDto: CreateCampaignDto,
    userId?: number,
  ): Promise<Campaign> {
    try {
      const campaign = new Campaign();
      const source = await this.leadSourceService.findOne(
        createCampaignDto.source_id,
      );
      if (!source) {
        throw new HttpException('Source not found', HttpStatus.NO_CONTENT);
      }

      if (createCampaignDto.type_id) {
        const campaign_type = CampaignType[createCampaignDto.type_id];
        if (!campaign_type) {
          throw new HttpException(
            'Campaign type not found',
            HttpStatus.NO_CONTENT,
          );
        }
        // campaign.type = campaign_type;
      }
      campaign.campaign_name = createCampaignDto?.campaign_name;
      campaign.destination_url = createCampaignDto?.destination_url;
      campaign.execution_time = createCampaignDto?.execution_time;
      campaign.identifier = createCampaignDto?.identifier;
      campaign.medium = createCampaignDto?.medium;
      campaign.objective = createCampaignDto?.objective;
      campaign.platform_campaignId = createCampaignDto?.platform_campaignId;
      campaign.replay_flow = createCampaignDto?.replay_flow;
      campaign.requested_by = createCampaignDto?.requested_by;
      campaign.lead_source_id = createCampaignDto?.source_id;
      campaign.term = createCampaignDto?.term;
      campaign.youtube_link = createCampaignDto?.youtube_link;
      campaign.whatsapp_message = createCampaignDto?.whatsapp_message;
      campaign.created_by = userId;
      return this.campaignRepository.save(campaign);
    } catch (e) {
      throw new HttpException(e.message, 400);
    }
  }

  async findAll(): Promise<Campaign[]> {
    return this.campaignRepository.find();
  }

  async findOne(id: number): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { id },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }

    return campaign;
  }

  async findByName(name: string): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { campaign_name: name?.trim() },
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with name ${name} not found`);
    }

    return campaign;
  }

  async update(
    id: number,
    updateCampaignDto: UpdateCampaignDto,
    userId?: number,
  ): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { id },
    });
    await this.leadSourceService.findOne(updateCampaignDto.source_id);
    const campaign_type = CampaignType[updateCampaignDto.type_id];
    campaign.campaign_name = updateCampaignDto.campaign_name;
    campaign.destination_url = updateCampaignDto.destination_url;
    campaign.execution_time = updateCampaignDto.execution_time;
    campaign.identifier = updateCampaignDto.identifier;
    campaign.medium = updateCampaignDto.medium;
    campaign.objective = updateCampaignDto.objective;
    campaign.platform_campaignId = updateCampaignDto.platform_campaignId;
    campaign.replay_flow = updateCampaignDto.replay_flow;
    campaign.requested_by = updateCampaignDto.requested_by;
    campaign.lead_source_id = updateCampaignDto.source_id;
    campaign.term = updateCampaignDto.term;
    // campaign.type = campaign_type;
    campaign.youtube_link = updateCampaignDto.youtube_link;
    campaign.whatsapp_message = updateCampaignDto.whatsapp_message;
    campaign.updated_by = userId;
    return await this.campaignRepository.save(campaign);
  }

  async remove(id: number): Promise<void> {
    const result = await this.campaignRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Campaign with ID ${id} not found`);
    }
  }

  async findOneByTK(tk: any) {
    return await this.campaignRepository.findOne({
      where: { uuid: tk },
      relations: { lead_source: true },
    });
  }
}
