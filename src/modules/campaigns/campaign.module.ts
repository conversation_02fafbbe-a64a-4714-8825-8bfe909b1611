import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CampaignController } from './campaign.controller';
import { Campaign } from './entities/campaign.entity';
import { CampaignService } from './services/campaign.service';
import { LeadAllocationModule } from '@modules/lead-allocations/lead-allocation.module';

@Module({
  imports: [TypeOrmModule.forFeature([Campaign]), LeadAllocationModule],
  controllers: [CampaignController],
  providers: [CampaignService],
  exports: [CampaignService],
})
export class CampaignModule {}
