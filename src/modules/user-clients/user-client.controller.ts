import {
  <PERSON>,
  Get,
  Post,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Body,
  Query,
  Req,
  HttpException,
} from '@nestjs/common';
import { Request } from 'express';
import { UserClientService } from './services/user-client.service';
import { UserClient } from './entities/user-client.entity';
import { CreateUserClientDto } from './dto/user-client.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';

@ApiTags('user-clients')
@Controller('user-clients')
export class UserClientController {
  constructor(private readonly userClientService: UserClientService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new user-client association' })
  @ApiBody({ type: CreateUserClientDto })
  async create(
    @Body() createUserClientDto: CreateUserClientDto,
  ): Promise<UserClient> {
    return this.userClientService.create(createUserClientDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Get user-client associations by userId or clientId or all',
  })
  @ApiQuery({ name: 'userId', required: false, type: Number })
  @ApiQuery({ name: 'clientId', required: false, type: Number })
  async findAll(
    @Query('userId') userId?: number,
    @Query('clientId') clientId?: number,
  ): Promise<UserClient[]> {
    if (userId) {
      return this.userClientService.findByUser(+userId);
    } else if (clientId) {
      return this.userClientService.findByClient(+clientId);
    }
    return this.userClientService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user-client association by ID' })
  @ApiParam({ name: 'id', description: 'UserClient association ID' })
  async findOne(@Param('id') id: string): Promise<UserClient> {
    return this.userClientService.findOne(+id);
  }

  @Delete(':userId/:clientId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Remove user-client association by userId and clientId',
  })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'clientId', description: 'Client ID' })
  async remove(
    @Param('userId') userId: string,
    @Param('clientId') clientId: string,
  ): Promise<void> {
    return this.userClientService.remove(+userId, +clientId);
  }

  @Get('access/:userId/:clientId')
  @ApiOperation({ summary: 'Check if user has access to client' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiParam({ name: 'clientId', description: 'Client ID' })
  async checkAccess(
    @Param('userId') userId: string,
    @Param('clientId') clientId: string,
  ): Promise<{ hasAccess: boolean }> {
    const hasAccess = await this.userClientService.hasAccess(
      +userId,
      +clientId,
    );
    return { hasAccess };
  }

  @Get('user/:userId/clients')
  @ApiOperation({ summary: 'Get all client IDs associated with a user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  async getUserClients(@Param('userId') userId: string): Promise<number[]> {
    return this.userClientService.getUserClients(+userId);
  }

  @Post('switch-client')
  @ApiOperation({ summary: 'Switch primary client for the authenticated user' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        clientId: { type: 'number', description: 'Client ID to switch to' },
      },
      required: ['clientId'],
    },
  })
  async switchClient(@Req() req: Request, @Body('clientId') clientId: number) {
    const userId = req.user?.id;
    if (!userId) {
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    await this.userClientService.setPrimaryClient(userId, clientId);
    const userClient = await this.userClientService.getPrimaryClient(userId);

    return {
      success: true,
      currentClient: userClient.client,
    };
  }
}
