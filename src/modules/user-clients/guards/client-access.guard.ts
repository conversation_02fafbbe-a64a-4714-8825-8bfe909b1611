import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { UserClientService } from '../services/user-client.service';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { Reflector } from '@nestjs/core';

@Injectable()
export class ClientAccessGuard implements CanActivate {
  private readonly logger = new Logger(ClientAccessGuard.name);

  constructor(
    private reflector: Reflector,
    private userClientService: UserClientService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const user = request.user;

    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );

    if (isPublic) {
      this.logger.log('Public route detected, skipping JWT auth');
      return true;
    }

    if (!user) {
      this.logger.warn('Access denied: No user found in request');
      throw new UnauthorizedException('User not authenticated');
    }

    const clientId =
      request.headers['x-client-id'] ||
      request.params.clientId ||
      request.body.clientId ||
      user.currentClientId;

    this.logger.log(
      `Checking access for user ${user.id} to client ${clientId}`,
    );

    if (!clientId) {
      this.logger.warn(
        `Access denied for user ${user.id}: No client context found`,
      );
      throw new ForbiddenException('No client context found');
    }

    const numericClientId = Number(clientId);

    if (isNaN(numericClientId)) {
      this.logger.warn(
        `Access denied for user ${user.id}: clientId is not a number - received: ${clientId}`,
      );
      throw new ForbiddenException('Invalid client ID format');
    }

    const hasAccess = await this.userClientService.hasAccess(
      user.id,
      numericClientId,
    );

    if (!hasAccess) {
      this.logger.warn(
        `Access denied: User ${user.id} does not have access to client ${numericClientId}`,
      );
      throw new ForbiddenException(
        `You don't have access to client with ID ${clientId}`,
      );
    }

    user.currentClientId = numericClientId;
    this.logger.log(
      `Access granted: User ${user.id} granted access to client ${numericClientId}`,
    );

    return true;
  }
}
