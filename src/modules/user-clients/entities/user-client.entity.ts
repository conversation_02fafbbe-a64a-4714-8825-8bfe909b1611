import { <PERSON><PERSON>ty, Column, Index, Unique, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { User } from 'src/modules/users/entities/user.entity';
import { Client } from 'src/modules/clients/entities/client.entity';
import { BaseEntity } from 'src/common/entities/base.entity';

@Entity()
@Unique(['user_id', 'client_id'])
export class UserClient extends BaseEntity {
  @Column()
  @Index()
  user_id: number;

  @Column()
  @Index()
  client_id: number;

  @ManyToOne(() => User, (user) => user.user_clients, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Client, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'client_id' })
  client: Client;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'boolean', default: false })
  is_primary_client: boolean;
}
