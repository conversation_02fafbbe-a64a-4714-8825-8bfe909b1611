import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserClient } from '../entities/user-client.entity';
import { CreateUserClientDto } from '../dto/user-client.dto';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';
import { Role } from '@modules/roles/enities/role.entity';

@Injectable()
export class UserClientService {
  constructor(
    @InjectRepository(UserClient)
    private readonly userClientRepository: Repository<UserClient>,
    @InjectRepository(UserClientRole)
    private readonly userClientRoleRepository: Repository<UserClientRole>,
  ) {}

  async create(createUserClientDto: CreateUserClientDto): Promise<UserClient> {
    const existing = await this.userClientRepository.findOneBy({
      user_id: createUserClientDto.user_id,
      client_id: createUserClientDto.client_id,
    });

    if (existing) {
      throw new ConflictException(
        'This user-client association already exists',
      );
    }

    // Check if this is the first client for the user
    const userClients = await this.findByUser(createUserClientDto.user_id);
    const isPrimaryClient = userClients.length === 0;

    const userClient = this.userClientRepository.create({
      ...createUserClientDto,
      is_primary_client: isPrimaryClient,
    });

    return this.userClientRepository.save(userClient);
  }

  async findAll(): Promise<UserClient[]> {
    return this.userClientRepository.find({
      relations: ['user', 'client'],
    });
  }

  async findByUser(userId: number): Promise<UserClient[]> {
    return this.userClientRepository.find({
      where: { user_id: userId },
      relations: ['client'],
      order: {
        is_primary_client: 'DESC',
      },
    });
  }

  async findByClient(clientId: number): Promise<UserClient[]> {
    return this.userClientRepository.find({
      where: { client_id: clientId },
      relations: ['user'],
    });
  }

  async findOne(id: number): Promise<UserClient> {
    const userClient = await this.userClientRepository.findOne({
      where: { id },
      relations: ['user', 'client'],
    });

    if (!userClient) {
      throw new NotFoundException(`UserClient with ID ${id} not found`);
    }

    return userClient;
  }

  async remove(userId: number, clientId: number): Promise<void> {
    const result = await this.userClientRepository.delete({
      user_id: userId,
      client_id: clientId,
    });

    if (result.affected === 0) {
      throw new NotFoundException(
        `UserClient with userId ${userId} and clientId ${clientId} not found`,
      );
    }
  }

  async hasAccess(userId: number, clientId: number): Promise<boolean> {
    const count = await this.userClientRepository.count({
      where: { user_id: userId, client_id: clientId },
    });

    return count > 0;
  }

  async getUserClients(userId: number): Promise<number[]> {
    const userClients = await this.userClientRepository.find({
      where: { user_id: userId },
      select: ['client_id', 'is_primary_client'],
      order: {
        is_primary_client: 'DESC', // Primary client will come first
        client_id: 'ASC', // Secondary sort by client_id
      },
    });

    return userClients.map((uc) => uc.client_id);
  }
  /**
   * Gets all roles for a user in a specific client
   * @param userId - User ID
   * @param clientId - Client ID
   * @returns Array of Role entities
   */
  async getUserRoles(userId: number, clientId: number): Promise<Role[]> {
    // Find the user-client association
    const userClient = await this.userClientRepository.findOne({
      where: {
        user_id: userId,
        client_id: clientId,
      },
    });

    if (!userClient) {
      return [];
    }

    // Get role IDs from the user-client-role table
    const userClientRoles = await this.userClientRoleRepository.find({
      where: { user_client_id: userClient.id },
      relations: ['role'],
    });

    // Return the role entities
    return userClientRoles.map((ucr) => ucr.role);
  }

  async setPrimaryClient(userId: number, clientId: number): Promise<void> {
    // Verify the association exists
    const exists = await this.hasAccess(userId, clientId);
    if (!exists) {
      throw new NotFoundException(
        `User ${userId} does not have access to client ${clientId}`,
      );
    }

    await this.userClientRepository.manager.transaction(async (manager) => {
      // Reset all primary flags for this user
      await manager.update(
        UserClient,
        { user_id: userId },
        { is_primary_client: false },
      );

      // Set the new primary client
      await manager.update(
        UserClient,
        { user_id: userId, client_id: clientId },
        { is_primary_client: true },
      );
    });
  }

  /**
   * Get detailed user information including roles, cities, and business units
   * @param userId - User ID
   * @param clientId - Current client ID
   * @returns Enhanced user object with related data
   */
  async getUserDetailedInfo(userId: number, clientId: number) {
    // Get user's client information
    const userClient = await this.userClientRepository.findOne({
      where: {
        user_id: userId,
        client_id: clientId,
      },
      relations: ['client'],
    });

    if (!userClient) {
      throw new NotFoundException(
        `User ${userId} does not have access to client ${clientId}`,
      );
    }

    // Get user roles for the current client
    const userClientRoles = await this.getUserRoles(userId, clientId);

    // Get all business units (user clients) for this user
    const userClients = await this.findByUser(userId);

    // Format the roles for the response
    const formattedRoles = userClientRoles.map((role) => ({
      id: role.id,
      name: role.name,
      business_unit_id: clientId,
    }));

    // Format the business units for the response
    const formattedBusinessUnits = userClients.map((uc) => ({
      id: uc.client?.id,
      name: uc.client?.name || 'N/A',
      is_primary_client: uc.is_primary_client,
    }));

    return {
      roles: formattedRoles,
      business_units: formattedBusinessUnits,
      currentClient: userClient.client,
      availableClients: userClients.map((uc) => uc.client),
    };
  }
  async getPrimaryClient(userId: number): Promise<UserClient> {
    // Try to find the primary client
    let userClient = await this.userClientRepository.findOne({
      where: {
        user_id: userId,
        is_primary_client: true,
      },
      relations: ['client'],
    });

    // If no primary client is set, get the first available client
    if (!userClient) {
      userClient = await this.userClientRepository.findOne({
        where: { user_id: userId },
        relations: ['client'],
      });

      if (!userClient) {
        throw new NotFoundException('No clients found for this user');
      }

      // Set this as primary client
      await this.setPrimaryClient(userId, userClient.client_id);
    }

    return userClient;
  }
}
