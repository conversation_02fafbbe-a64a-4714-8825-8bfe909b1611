import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserClientService } from './services/user-client.service';
import { UserClientController } from './user-client.controller';
import { UserClient } from './entities/user-client.entity';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserClient, UserClientRole])],
  controllers: [UserClientController],
  providers: [UserClientService],
  exports: [UserClientService],
})
export class UserClientModule {}
