import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import { AppConfig } from '../../config/app.config';
import { AuthPayloadDto } from './dto/auth.dto';
import { SignupPayloadDto } from './dto/signup.dto';
import { RefreshDto } from './dto/refresh.dto';
import { User } from '../users/entities/user.entity';
import { UserService } from '../users/services/user.service';
import { UserClientService } from '@modules/user-clients/services/user-client.service';
import { InjectRepository } from '@nestjs/typeorm';
import { UserCity } from '@modules/lead-allocations/entities';
import { Repository } from 'typeorm';

/**
 * Authentication service that handles user authentication, registration and OAuth flows
 */
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly ssoApi: string;
  private readonly applicationId: number;

  constructor(
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly userClientService: UserClientService,
    private readonly appConfig: AppConfig,
    @InjectRepository(UserCity)
    private readonly userCityRepository: Repository<UserCity>,
  ) {
    const { api, applicationId } = this.appConfig.sso;
    this.ssoApi = api;
    this.applicationId = Number(applicationId);
    this.logger.log(
      `Auth service initialized with SSO API: ${api}, Application ID: ${applicationId}`,
    );
  }

  /**
   * Validates user credentials and returns auth tokens with client information
   * @param email - User's email address
   * @param password - User's password
   * @returns Auth tokens and client information
   * @throws HttpException if credentials are invalid
   */
  async validateUser({ email, password }: AuthPayloadDto) {
    this.logger.log(`Validating credentials for user: ${email}`);
    try {
      this.logger.debug(`Making SSO login request for user: ${email}`);
      const authUser = await axios.post(`${this.ssoApi}/auth/login`, {
        user_name: email,
        password,
        application_id: this.applicationId,
      });

      // Get primary client for the user
      this.logger.debug(`Finding user data in local database for: ${email}`);
      const user = await this.userService.findByEmail(email);

      if (!user) {
        this.logger.warn(`User not found in local database: ${email}`);
        throw new HttpException('User not found', HttpStatus.UNAUTHORIZED);
      }

      if (user.is_active === false) {
        this.logger.warn(`User is inactive: ${email}`);
        throw new HttpException('User is inactive', HttpStatus.UNAUTHORIZED);
      }

      this.logger.debug(`User found: ${user.id}, UUID: ${user.uuid}`);
      this.logger.debug(`Getting primary client for user ID: ${user.id}`);
      const userClient = await this.userClientService.getPrimaryClient(user.id);

      // Get user roles for the primary client
      this.logger.debug(
        `Getting roles for user ID: ${user.id} and client ID: ${userClient.client.id}`,
      );
      const userClientRoles = await this.userClientService.getUserRoles(
        user.id,
        userClient.client.id,
      );

      // Get user cities for the primary client
      this.logger.debug(
        `Getting cities for user ID: ${user.id} and client ID: ${userClient.client.id}`,
      );
      const userCities = await this.userCityRepository.find({
        where: {
          user_id: user.id,
          client_id: userClient.client.id,
        },
        relations: ['city'],
      });

      // Get all business units (user clients) for this user
      this.logger.debug(`Getting all business units for user ID: ${user.id}`);
      const userClients = await this.userClientService.findByUser(user.id);

      // Format the roles, cities, and business units for the response
      const formattedRoles = userClientRoles.map((role) => ({
        id: role.id,
        name: role.name,
        business_unit_id: userClient.client.id,
      }));

      const formattedCities = userCities.map((userCity) => ({
        id: userCity.city?.id,
        name: userCity.city?.city || 'N/A',
        client_id: userCity.client_id,
      }));

      const formattedBusinessUnits = userClients.map((uc) => ({
        id: uc.client?.id,
        name: uc.client?.name || 'N/A',
        is_primary_client: uc.is_primary_client,
      }));

      this.logger.debug(`Updating last login date for user: ${email}`);
      await this.userService.updateLastLoginDate(
        authUser.data.result.user.email,
      );

      this.logger.log(`User ${email} authenticated successfully`);

      // Enhance the user object with roles, cities, and business units
      const enhancedUser = {
        ...user,
        roles: formattedRoles,
        cities: formattedCities,
        business_units: formattedBusinessUnits,
      };

      return {
        ...authUser.data.result,
        user: enhancedUser,
        currentClient: userClient.client,
        availableClients: userClients,
      };
    } catch (error) {
      this.logger.error(
        `Authentication failed for user ${email}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Wrong username or password!',
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  /**
   * Creates a new user account
   * @param body - User signup information
   * @returns Created user entity
   */
  async signUp(body: SignupPayloadDto) {
    this.logger.log(`Processing signup request for: ${body.email}`);
    try {
      //todo:  Check if user already exists on sso
      const existingUser = await this.userService.findByEmail(body.email);
      if (existingUser) {
        throw new HttpException(
          'User with this email already exists',
          HttpStatus.BAD_REQUEST,
        );
      }

      const existingPhone = await this.userService.findByOfficialNumber(
        body.phone,
      );
      if (existingPhone) {
        throw new HttpException(
          'User with this phone number already exists',
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.debug(`Making SSO signup request for: ${body.email}`);
      const authUser = await axios.post(`${this.ssoApi}/auth/signup`, {
        first_name: body.first_name,
        last_name: body.last_name,
        password: body.password,
        email: body.email,
        phone: body.phone,
        country_code: body.country_code ?? '',
        profile_picture: '',
        application_id: this.applicationId,
        user_type: 1,
        access: {},
        role: 0,
      });

      this.logger.debug(
        `Creating local user record for: ${body.email} with SSO ID: ${JSON.stringify(authUser.data.result)}`,
      );
      const user = await this.createUserFromSignup(
        body,
        authUser.data.result.userId,
      );

      this.logger.log(
        `User ${body.email} signed up successfully with ID: ${user.id}`,
      );
      return user;
    } catch (error) {
      this.logger.error(
        `Signup failed for ${body.email}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Creates a new user from signup information
   * @param body - Signup payload
   * @param userId - SSO user ID
   * @returns Created user entity
   */
  private async createUserFromSignup(
    body: SignupPayloadDto,
    userId: string,
  ): Promise<User> {
    this.logger.debug(
      `Creating new user from signup data: ${body.email}, SSO ID: ${userId}`,
    );
    const user = new User();
    Object.assign(user, {
      first_name: body.first_name,
      last_name: body.last_name,
      email: body.email,
      official_number: body.phone,
      country_code: body.country_code,
      uuid: userId,
    });

    const savedUser = await this.userService.saveUser(user);
    this.logger.debug(`User created successfully with ID: ${savedUser.id}`);
    return savedUser;
  }

  /**
   * Refreshes auth tokens using a refresh token
   * @param refreshToken - Refresh token DTO
   * @returns New auth tokens
   * @throws HttpException if refresh token is invalid
   */
  async refresh(refreshToken: RefreshDto) {
    this.logger.log(`Processing token refresh request`);
    try {
      this.logger.debug(`Making SSO refresh token request`);
      const authUser = await axios.post(`${this.ssoApi}/auth/refresh`, {
        refresh_token: refreshToken.refresh_token,
      });

      this.logger.debug(
        `Updating last login date for user: ${authUser.data.result.user.email}`,
      );
      await this.userService.updateLastLoginDate(
        authUser.data.result.user.email,
      );

      this.logger.log(
        `Token refresh successful for user: ${authUser.data.result.user.email}`,
      );
      return authUser.data.result;
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`, error.stack);
      throw new HttpException('Invalid refresh token', HttpStatus.UNAUTHORIZED);
    }
  }

  /**
   * Handles Google OAuth login flow
   * @param token - Google OAuth token
   * @returns Auth tokens and user information
   * @throws HttpException if token is invalid or other errors occur
   */
  async googleLogin(token: string) {
    this.logger.log(`Processing Google login request`);
    try {
      this.validateGoogleToken(token);

      this.logger.debug(`Making Google login API call to SSO service`);
      const response = await this.callGoogleLogin(token);

      this.logger.debug(`Processing Google user data from response`);
      const user = await this.handleGoogleUser(response.data.result);

      // Get primary client for Google user
      this.logger.debug(
        `Getting primary client for Google user ID: ${user.id}`,
      );
      const userClient = await this.userClientService.getPrimaryClient(user.id);

      this.logger.debug(
        `Getting all available clients for user ID: ${user.id}`,
      );
      const availableClients = await this.userClientService.findByUser(user.id);

      this.logger.log(`Google login successful for user: ${user.email}`);
      return {
        ...response.data.result,
        user: { ...response.data.result.user, ...user },
        currentClient: userClient.client,
        availableClients: availableClients,
      };
    } catch (error) {
      this.logger.error(`Google login failed: ${error.message}`, error.stack);
      this.handleGoogleLoginError(error);
    }
  }

  /**
   * Validates Google OAuth token
   * @param token - Google OAuth token
   * @throws HttpException if token is missing
   */
  private validateGoogleToken(token: string) {
    this.logger.debug(`Validating Google token`);
    if (!token) {
      this.logger.warn(`Google token validation failed: Token is missing`);
      throw new HttpException(
        'Google token is required',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Makes API call to SSO service for Google login
   * @param token - Google OAuth token
   * @returns SSO service response
   * @throws HttpException on invalid response
   */
  private async callGoogleLogin(token: string) {
    this.logger.debug(`Calling SSO service for Google login`);
    const response = await axios.post(
      `${this.ssoApi}/auth/login-with-google`,
      {
        google_token: token,
        application_id: this.applicationId,
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
        validateStatus: (status) => status < 500,
      },
    );

    if (!response.data?.result) {
      this.logger.warn(`Invalid response from SSO service for Google login`);
      throw new HttpException(
        'Invalid response from authentication service',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.logger.debug(
      `Received valid response from SSO service for Google login`,
    );
    return response;
  }

  /**
   * Handles Google user creation/retrieval
   * @param result - SSO service response
   * @returns User entity
   * @throws HttpException if email is missing
   */
  private async handleGoogleUser(result: any) {
    this.logger.debug(`Handling Google user data from SSO response`);
    const userEmail = result.user?.email;
    if (!userEmail) {
      this.logger.warn(`No email found in Google user data`);
      throw new HttpException('No email in response', HttpStatus.BAD_REQUEST);
    }

    this.logger.debug(`Checking if Google user already exists: ${userEmail}`);
    let user = await this.userService.findByEmail(userEmail);

    if (!user) {
      this.logger.debug(
        `Google user not found in database, creating new user: ${userEmail}`,
      );
      user = await this.createGoogleUser(result.user);
    } else {
      this.logger.debug(
        `Google user found in database: ${userEmail}, ID: ${user.id}`,
      );
    }

    this.logger.debug(`Updating last login date for Google user: ${userEmail}`);
    await this.userService.updateLastLoginDate(userEmail);

    return user;
  }

  /**
   * Creates a new user from Google profile
   * @param googleUser - Google user profile
   * @returns Created user entity
   */
  private async createGoogleUser(googleUser: any): Promise<User> {
    this.logger.debug(
      `Creating new user from Google profile: ${googleUser.email}`,
    );
    const user = new User();
    Object.assign(user, {
      email: googleUser.email,
      first_name: googleUser.firstName || '',
      last_name: googleUser.lastName || '',
      is_approved: true,
      status: true,
    });

    const savedUser = await this.userService.saveUser(user);
    this.logger.debug(
      `Google user created successfully with ID: ${savedUser.id}`,
    );
    return savedUser;
  }

  /**
   * Handles errors from Google login flow
   * @param error - Error object
   * @throws HttpException with appropriate status and message
   */
  private handleGoogleLoginError(error: any) {
    this.logger.error(
      `Google login error: ${error?.response?.data?.message || error.message}`,
      error.stack,
    );

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 500) {
        throw new HttpException(
          'Authentication service is unavailable',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }
      throw new HttpException(
        error.response?.data?.message || 'Google login failed',
        error.response?.status || HttpStatus.BAD_REQUEST,
      );
    }

    throw new HttpException(
      'Internal server error during Google login',
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }

  /**
   * Logs out a user by invalidating their session
   * @param userId - User ID to logout
   * @returns Success message
   * @throws HttpException if logout fails
   */
  async logout(userId: string) {
    this.logger.log(`Processing logout request for user ID: ${userId}`);
    try {
      this.logger.debug(`Making SSO logout request for user ID: ${userId}`);
      await axios.post(`${this.ssoApi}/auth/logout`, {
        user_id: userId,
        application_id: this.applicationId,
      });

      this.logger.log(`User ${userId} logged out successfully`);
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      this.logger.error(
        `Logout failed for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to logout',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
