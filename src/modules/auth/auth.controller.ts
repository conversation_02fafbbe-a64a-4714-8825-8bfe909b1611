import {
  Controller,
  Post,
  Req,
  Res,
  Get,
  Body,
  Query,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignupPayloadDto } from './dto/signup.dto';
import { AuthPayloadDto } from './dto/auth.dto';
import { Request, Response } from 'express';
import { Public } from './decorators/public.decorator';
import { RefreshDto } from './dto/refresh.dto';
import { GoogleLoginDto } from './dto/google-login.dto';
import { ApiTags, ApiOperation, ApiBody, ApiQuery } from '@nestjs/swagger';
import { AppConfig } from 'src/config/app.config';
import { ConfigService } from '@nestjs/config';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private authService: AuthService,
    private config: ConfigService,
    private appConfig: AppConfig,
  ) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'User login with email and password' })
  @ApiBody({ type: AuthPayloadDto })
  async login(@Req() req: Request, @Body() body: AuthPayloadDto) {
    this.logger.log(`Login attempt for email: ${body.email}`);
    try {
      const result = await this.authService.validateUser(body);
      this.logger.log(`Successful login for email: ${body.email}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Login failed for email: ${body.email} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('status')
  @ApiOperation({ summary: 'Get current authentication status/user info' })
  status(@Req() req: Request) {
    const userId = req.user?.id;
    if (userId) {
      this.logger.debug(`Status check for user ID: ${userId}`);
      return req.user;
    } else {
      this.logger.warn('Status check attempted without valid user session');
      return { message: 'No user found' };
    }
  }

  @Public()
  @Post('signup')
  @ApiOperation({ summary: 'User signup / registration' })
  @ApiBody({ type: SignupPayloadDto })
  async signup(@Body() body: SignupPayloadDto) {
    this.logger.log(`Signup attempt for email: ${body.email}`);
    try {
      const result = await this.authService.signUp(body);
      this.logger.log(`Successful signup for email: ${body.email}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Signup failed for email: ${body.email} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Public()
  @Get('google')
  @ApiOperation({ summary: 'Redirect to Google OAuth login' })
  @ApiQuery({
    name: 'redirect',
    required: false,
    description: 'Redirect URL after successful Google login',
    type: String,
  })
  async google(@Res() res: Response, @Query('redirect') redirect?: string) {
    const redirectUrl = redirect ?? 'http://localhost:3211/api/cb?token=';
    const appId = this.appConfig.sso.applicationId;
    this.logger.log(
      `Google OAuth redirect initiated with redirect URL: ${redirectUrl} , appId:  ${appId}, redirect: ${redirect}`,
    );

    const googleAuthUrl = `${this.config.get('SSO_API')}/auth/google?app=${appId}&redirect_uri=${redirectUrl}`;
    this.logger.debug(`Redirecting to Google OAuth URL: ${googleAuthUrl}`);

    return res.redirect(googleAuthUrl);
  }

  @Public()
  @Post('google-login')
  @ApiOperation({ summary: 'Login using Google token' })
  @ApiBody({ type: GoogleLoginDto })
  async loginWithGoogle(@Body() body: GoogleLoginDto) {
    this.logger.log('Google login attempt initiated');
    this.logger.debug(
      `Google token received: ${body.google_token?.substring(0, 10)}...`,
    );

    try {
      const result = await this.authService.googleLogin(body.google_token);
      this.logger.log('Google login successful');
      return result;
    } catch (error) {
      this.logger.error(
        `Google login failed: ${error?.response?.data?.message || error.message}`,
        error.stack,
      );
      throw new HttpException(
        error?.response?.data?.message || 'Google login failed',
        error?.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Public()
  @Post('refresh')
  @ApiOperation({ summary: 'Refresh JWT access token' })
  @ApiBody({ type: RefreshDto })
  async refresh(@Body() refreshToken: RefreshDto) {
    this.logger.log('Token refresh attempt initiated');
    this.logger.debug(
      `Refresh token received: ${refreshToken.refresh_token?.substring(0, 10)}...`,
    );

    try {
      const result = await this.authService.refresh(refreshToken);
      this.logger.log('Token refresh successful');
      return result;
    } catch (error) {
      this.logger.error(`Token refresh failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Post('logout')
  @ApiOperation({ summary: 'Logout the current user' })
  async logout(@Req() req: Request) {
    const userId = req.user?.id;

    if (!userId) {
      this.logger.warn('Logout attempted without valid user session');
      throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
    }

    this.logger.log(`Logout initiated for user ID: ${userId}`);

    try {
      const result = await this.authService.logout(userId.toString());
      this.logger.log(`Successful logout for user ID: ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Logout failed for user ID: ${userId} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
