import { Request } from 'express';
import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { UserService } from 'src/modules/users/services/user.service';
import { ConfigService } from '@nestjs/config';
import { UserClientService } from '@modules/user-clients/services/user-client.service';
import { CacheKeys } from 'src/common/cache/constants/cache-keys.enum';
import { AppConfig } from 'src/config/app.config';

/**
 * JWT Strategy for authenticating users via JSON Web Tokens
 * Extends Passport's JWT strategy for handling token-based authentication
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  /**
   * Initializes the JWT strategy with required dependencies and configuration
   * @param cacheService - Cache manager for storing user data
   * @param configService - Service for accessing environment configuration
   * @param userClientService - Service for managing user-client relationships
   * @param userService - Service for user operations
   * @param redis - Redis client for caching
   */
  constructor(
    @Inject(CACHE_MANAGER) private cacheService: Cache,
    private readonly configService: ConfigService,
    private userClientService: UserClientService,
    private userService: UserService,
    @InjectRedis() private readonly redis: Redis,
    private readonly appConfig: AppConfig,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: appConfig.jwt.secret,
      passReqToCallback: true, // Pass request object to validate method
    });
    this.logger.log('JWT Strategy initialized');
  }

  /**
   * Validates the JWT payload and retrieves user information
   * - Checks user type authorization
   * - Retrieves user from cache or database
   * - Gets client information, allowing client switching via headers
   *
   * @param req - The request object containing headers
   * @param payload - Decoded JWT payload containing user information
   * @returns User object with current client ID
   * @throws UnauthorizedException if user type is invalid or user not found
   */
  async validate(req: Request, payload: any) {
    this.logger.debug(
      `Validating JWT for user ID: ${payload.userId}, user type: ${payload.userType}`,
    );

    // Verify user has correct type (1 = regular user)
    if (payload.userType !== 1) {
      this.logger.warn(
        `Unauthorized access attempt - Invalid user type: ${payload.userType}`,
      );
      throw new UnauthorizedException('Not authorized');
    }

    // Try to get user from cache first
    const cacheKey = `${CacheKeys.AUTH_USER}${payload.userId}`;
    this.logger.debug(`Checking cache for user data with key: ${cacheKey}`);
    let user: any = await this.cacheService.get(cacheKey);

    // If not in cache, fetch from database
    if (!user) {
      this.logger.debug(
        `User data not found in cache, fetching from database for UUID: ${payload.userId}`,
      );
      user = await this.userService.findUserByUUID(payload.userId);

      if (!user) {
        this.logger.warn(`User not found for UUID: ${payload.userId}`);
        throw new UnauthorizedException('User not found');
      } else if (user.is_active === false) {
        this.logger.warn(
          `Unauthorized access attempt - Inactive user UUID: ${payload.userId}`,
        );
        throw new UnauthorizedException('User is inactive');
      } else {
        this.logger.debug(`Caching user data for ID: ${user.id}`);
        await this.cacheService.set(cacheKey, user);
      }
    } else {
      this.logger.debug(`User data found in cache for ID: ${user.id}`);
    }

    const headerClientId = req.headers['x-client-id'] as string;
    let clientId: number;

    if (!headerClientId) {
      // If no client ID is provided in header, get the user's primary client
      this.logger.debug(
        `No client ID found in header, fetching primary client for user ${user.id}`,
      );
      try {
        const primaryClient = await this.userClientService.getPrimaryClient(
          user.id,
        );
        clientId = primaryClient.client_id;
        this.logger.debug(`Using primary client ID: ${clientId}`);
      } catch (error) {
        this.logger.warn(
          `No clients found for user ${user.id} , error : ${error.message}`,
        );
        throw new UnauthorizedException('User has no assigned clients');
      }
    } else {
      clientId = Number(headerClientId);

      // If a client ID is provided in the header, verify access
      const hasAccess = await this.userClientService.hasAccess(
        user.id,
        clientId,
      );

      if (!hasAccess) {
        this.logger.warn(
          `User ${user.id} attempted to access unauthorized client ${clientId}`,
        );
        throw new UnauthorizedException('Not authorized to access this client');
      }
    }

    // Return user data with their client ID
    this.logger.log(
      `JWT validated successfully for user ID: ${user.id}, email: ${user.email}, client ID: ${clientId}`,
    );
    return {
      ...(user as object),
      currentClientId: clientId,
    };
  }
}
