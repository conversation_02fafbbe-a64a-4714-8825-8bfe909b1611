import { Injectable, NestMiddleware } from '@nestjs/common';
import { Response, NextFunction } from 'express';
import { UserClientService } from '../../../modules/user-clients/services/user-client.service';
import { AuthenticatedRequest } from '../interfaces/request.interface';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly userClientService: UserClientService) {}

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    if (req.user?.id) {
      try {
        const userClient = await this.userClientService.getPrimaryClient(
          req.user.id,
        );
        req.user.currentClientId = userClient.client_id;
      } catch (error) {
        console.error('Error setting primary client:', error);
      }
    }
    next();
  }
}
