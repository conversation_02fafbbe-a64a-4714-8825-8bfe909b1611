import { Role } from '@modules/roles/enities/role.entity';
import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity, OneToMany } from 'typeorm';

@Entity()
export class Client extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  website: string;

  //to handle configs for google meets, call dates, etc to be considered from lead table or lead program interest table
  @Column({ type: 'jsonb', nullable: true })
  configuration: Record<string, any>;

  @OneToMany(() => Role, (role) => role.client)
  roles: Role[];
}
