import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Req,
  Query,
} from '@nestjs/common';
import { ClientService } from './client.service';
import { CreateClientDto } from './dto/create-client.dto';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('clients')
@ApiResource('client')
@Controller('clients')
export class ClientController {
  constructor(private readonly clientService: ClientService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new client' })
  @ApiBody({ type: CreateClientDto })
  create(
    @Body() createClientDto: CreateClientDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id || null;
    return this.clientService.create(createClientDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get paginated list of clients' })
  @ApiQuery({
    name: 'page',
    type: Number,
    required: false,
    description: 'Page number (default 1)',
  })
  @ApiQuery({
    name: 'size',
    type: Number,
    required: false,
    description: 'Number of items per page (default 10)',
  })
  findAll(@Query() paginationDto: PaginationDto) {
    const { page = 1, size = 10 } = paginationDto;
    return this.clientService.findAll(page, size);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get client by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Client ID' })
  findOne(@Param('id') id: string) {
    return this.clientService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing client' })
  @ApiParam({ name: 'id', type: String, description: 'Client ID' })
  @ApiBody({ type: CreateClientDto })
  @ApiBearerAuth()
  update(
    @Req() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateClientDto: CreateClientDto,
  ) {
    // Placeholder userId, replace with real user from auth context in production
    const userId = req.user?.id || 1;
    return this.clientService.update(+id, updateClientDto, userId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a client by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Client ID' })
  remove(@Param('id') id: string) {
    return this.clientService.remove(+id);
  }
}
