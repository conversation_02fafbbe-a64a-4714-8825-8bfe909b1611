import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Client } from './entities/client.entity';
import { CreateClientDto } from './dto/create-client.dto';

@Injectable()
export class ClientService {
  constructor(
    @InjectRepository(Client)
    private clientsRepository: Repository<Client>,
  ) {}

  async create(
    createClientDto: CreateClientDto,
    userId?: number,
  ): Promise<Client> {
    const client = this.clientsRepository.create({
      ...createClientDto,
      created_by: userId,
    });
    return this.clientsRepository.save(client);
  }

  async findAll(page = 1, size = 10): Promise<{ data: Client[]; meta: any }> {
    const skip = (page - 1) * size;

    const queryBuilder = this.clientsRepository
      .createQueryBuilder('client')
      .leftJoinAndSelect('client.roles', 'role');

    const [clients, total] = await queryBuilder
      .skip(skip)
      .take(size)
      .getManyAndCount();

    const totalPages = Math.ceil(total / size);

    return {
      data: clients,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: number): Promise<Client> {
    const client = await this.clientsRepository.findOne({
      where: { id },
      relations: ['contacts'],
    });

    if (!client) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }

    return client;
  }

  async update(
    id: number,
    updateClientDto: CreateClientDto,
    userId?: number,
  ): Promise<Client> {
    const client = await this.findOne(id);

    const updatedClient = {
      ...client,
      ...updateClientDto,
      updated_by: userId,
    };

    return this.clientsRepository.save(updatedClient);
  }

  async remove(id: number): Promise<void> {
    const result = await this.clientsRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Client with ID ${id} not found`);
    }
  }
}
