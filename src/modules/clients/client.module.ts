import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Client } from './entities/client.entity';
import { ClientController } from './client.controller';
import { ClientService } from './client.service';
import { RoleModule } from '@modules/roles/role.module';
import { UserClientModule } from '@modules/user-clients/user-client.module';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { ClientAccessGuard } from '@modules/user-clients/guards/client-access.guard';

@Module({
  imports: [TypeOrmModule.forFeature([Client]), RoleModule, UserClientModule],
  controllers: [ClientController],
  providers: [ClientService],
  exports: [ClientService],
})
export class ClientModule {}
