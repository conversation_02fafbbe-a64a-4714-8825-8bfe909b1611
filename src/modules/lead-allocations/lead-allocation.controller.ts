import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { LeadAllocationService } from './services/lead-allocation.service';
import { LeadAllocationHistory } from './entities/lead-allocation-history.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { Action, Resource } from '@modules/permissions/enums/permission.enum';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { SpocManagementService } from './services/spoc-management.service';
import { LeadAllocationHistoryService } from './services/lead-allocation-history.service';
import { LeadSourceService } from './services/lead-source.service';
import { LeadSource } from './entities';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { Public } from '@modules/auth/decorators/public.decorator';

//todo: Add role guards
@ApiTags('lead-allocations')
@Controller('lead-allocations')
export class LeadAllocationController {
  constructor(
    private readonly leadAllocationService: LeadAllocationService,
    private readonly spocManagementService: SpocManagementService,
    private readonly leadAllocationHistoryService: LeadAllocationHistoryService,
    private readonly leadSourceService: LeadSourceService,
  ) {}

  @Post('allocate/:leadId')
  @ApiOperation({ summary: 'Allocate a lead to an appropriate SPOC' })
  @ApiParam({ name: 'leadId', description: 'ID of the lead to allocate' })
  @ApiResponse({ status: 200, description: 'Lead allocated successfully' })
  @ApiResponse({ status: 400, description: 'Lead allocation failed' })
  @RequirePermission({
    resource: Resource.LEAD_PROGRAM_INTEREST,
    action: Action.ADMIN,
  })
  async allocateLead(
    @Param('leadId') leadId: number,
    @Req() req: AuthenticatedRequest,
    @Body('sourceId') sourceId: number,
  ): Promise<{ success: boolean; message: string }> {
    const userId = req.user?.id;
    const success = await this.leadAllocationService.allocateCCSpocToLead({
      leadId,
      sourceId,
      allocatedBy: `user_${userId}`,
    });

    return {
      success,
      message: success
        ? 'Lead allocated successfully'
        : 'Lead allocation failed',
    };
  }

  @Post('manually-reassign')
  @ApiResource(Resource.LEAD_PROGRAM_INTEREST)
  @UseGuards(PermissionGuard)
  @RequirePermission({
    resource: Resource.LEAD_PROGRAM_INTEREST,
    action: Action.UPDATE,
  })
  @ApiOperation({ summary: 'Manually reassign a lead to a different SPOC' })
  @ApiResponse({ status: 200, description: 'Lead reassigned successfully' })
  @ApiResponse({ status: 400, description: 'Lead reassignment failed' })
  @RequirePermission({
    resource: Resource.LEAD_PROGRAM_INTEREST,
    action: Action.ADMIN,
  })
  async manuallyReassignLead(
    @Body()
    reassignmentDto: {
      leadProgInterestId: number;
      newSpocId: number;
    },
    @Req() req: AuthenticatedRequest,
  ): Promise<{ success: boolean; message: string }> {
    const userId = req.user?.id;
    const success = await this.leadAllocationService.manuallyReassignLead({
      leadProgInterestId: reassignmentDto.leadProgInterestId,
      newSpocId: reassignmentDto.newSpocId,
      reassignedByUserId: userId,
    });

    return {
      success,
      message: success
        ? 'Lead reassigned successfully'
        : 'Lead reassignment failed',
    };
  }

  @Get('spoc-leads/:spocId')
  @ApiOperation({ summary: 'Get all leads allocated to a specific SPOC' })
  @ApiParam({ name: 'spocId', description: 'ID of the SPOC' })
  @ApiResponse({
    status: 200,
    description: 'List of leads assigned to the SPOC',
  })
  async getLeadsBySpoc(
    @Param('spocId') spocId: number,
  ): Promise<LeadProgramInterest[]> {
    return this.spocManagementService.getLeadProgInterestsBySpoc(spocId);
  }

  @Get('history/:leadProgInterestId')
  @ApiResource(Resource.LEAD_PROGRAM_INTEREST)
  @UseGuards(PermissionGuard)
  @RequirePermission({
    resource: Resource.LEAD_PROGRAM_INTEREST,
    action: Action.READ,
  })
  @ApiOperation({ summary: 'Get allocation history for a specific lead' })
  @ApiParam({ name: 'leadId', description: 'ID of the lead' })
  @ApiResponse({ status: 200, description: 'Allocation history for the lead' })
  async getLeadAllocationHistory(
    @Param('leadId') leadId: number,
    // @Req() req: AuthenticatedRequest,
  ): Promise<LeadAllocationHistory[]> {
    //todo : authenticate this request
    return this.leadAllocationHistoryService.getLeadAllocationHistory(leadId);
  }

  @Public()
  @Get('lead-sources')
  @ApiOperation({ summary: 'Get all lead sources' })
  @ApiResponse({ status: 200, description: 'List of lead sources' })
  async getLeadSources(): Promise<PaginatedResponse<LeadSource>> {
    return this.leadSourceService.findAllLeadSources();
  }
}
