import { EntityManager } from 'typeorm';
import { AllocationTypeEnum } from '../enums/allocation-type.enum';
import { LeadAllocationCriteriaType } from '../enums/allocation-rule.enum';
import { Lead } from '@modules/leads/entities/lead.entity';
import { SpocType } from '@modules/leads/enums/spoc.enum';

// Interfaces for method parameters
export interface FetchEligibleSpocsParams {
  cityId: number;
  sourceId: number | null;
  criteriaType: LeadAllocationCriteriaType;
  clientId: number;
}

export interface AllocateLeadParams {
  leadId: number;
  sourceId: number;
  spocType?: SpocType;
  allocatedBy?: string;
}

export interface UpdateLeadOwnerParams {
  spocType?: SpocType;
  manager: EntityManager;
  leadId: number;
  ownerId: number | null;
}

export interface UpdateLeadProgInterestStatusParams {
  manager: EntityManager;
  leadProgInterestId: number;
  spocId: number | null;
}

export interface UpdateCityRoundRobinStateParams {
  manager: EntityManager;
  cityId: number;
  spocId: number;
  clientId: number;
}

export interface RecordAllocationHistoryParams {
  manager: EntityManager;
  lead: Lead;
  spocId: number | null;
  allocationType: AllocationTypeEnum;
  allocatedBy: string;
  notes: string | null;
  clientId: number;
  spocType?: SpocType;
  cityId?: number | null;
  sourceId?: number | null;
  startTime?: number;
  isSuccessful?: boolean;
  transactionDuration?: number;
  allocatedByUserId?: number;
}

export interface RecordAllocationHistoryNonTransactionalParams {
  lead: Lead;
  spocId: number | null;
  allocationType: AllocationTypeEnum;
  allocatedBy: string;
  notes: string | null;
  clientId: number;
  cityId?: number | null;
  sourceId?: number | null;
  allocatedByUserId?: number;
  allocated_at?: Date;
}

export interface ManuallyReassignLeadParams {
  leadProgInterestId: number;
  newSpocId: number;
  reassignedByUserId: number;
  notes?: string;
}

export interface GetLeadProgInterestAllocationHistoryParams {
  leadId: number;
}
