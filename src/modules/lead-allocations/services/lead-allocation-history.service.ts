import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@modules/users/entities/user.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import {
  RecordAllocationHistoryNonTransactionalParams,
  RecordAllocationHistoryParams,
} from '../types/lead-allocation.type';
import { LeadAllocationHistory } from '../entities';
import { UserService } from '@modules/users/services/user.service';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { AllocationTypeEnum } from '../enums/allocation-type.enum';
import { SpocType } from '@modules/leads/enums/spoc.enum';

@Injectable()
export class LeadAllocationHistoryService {
  private readonly logger = new Logger(LeadAllocationHistoryService.name);

  constructor(
    @InjectRepository(LeadAllocationHistory)
    private readonly leadAllocationHistoryRepository: Repository<LeadAllocationHistory>,

    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  /**
   * Helper method to record allocation history outside of a transaction context
   */
  async recordAllocationHistoryNonTransactional(
    params: RecordAllocationHistoryNonTransactionalParams,
  ): Promise<void> {
    const {
      lead,
      spocId,
      allocationType,
      allocatedBy,
      notes,
      clientId,
      cityId = null,
      sourceId = null,
      allocated_at = new Date(),
    } = params;

    try {
      const finalLeadId = lead.id;

      // Get the previous SPOC (owner) of the lead if available
      let previousSpocId = null;
      if (finalLeadId) {
        const lead = await this.leadRepository.findOne({
          where: { id: finalLeadId },
          relations: ['owner_spoc'],
        });
        previousSpocId = lead?.owner_spoc?.spoc_id || null;
      }

      const history = this.leadAllocationHistoryRepository.create({
        lead_id: finalLeadId,
        client_id: clientId,
        spoc_id: spocId,
        previous_spoc_id: previousSpocId,
        allocation_type: allocationType,
        allocated_by: allocatedBy,
        allocated_at,
        notes: notes,
        allocation_metadata: {
          previousSpocId,
          allocationType,
          clientId,
          cityId,
          sourceId,
          leadId: finalLeadId,
        },
      });

      await this.leadAllocationHistoryRepository.save(history);
    } catch (error) {
      this.logger.error(
        `Failed to record allocation history for LeadProgInterest ${lead.id}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Records the allocation history with detailed information within an existing transaction
   */
  async recordAllocationHistory(
    params: RecordAllocationHistoryParams,
  ): Promise<void> {
    const {
      manager,
      lead,
      spocId,
      allocationType,
      allocatedBy,
      notes,
      clientId,
      cityId = null,
      sourceId = null,
      startTime = Date.now(),
      isSuccessful = false,
      transactionDuration,
      spocType,
      allocatedByUserId = null,
    } = params;

    const finalLeadId = lead.id;

    // Get the previous SPOC (owner) from the lead
    let previousSpocId = null;
    if (finalLeadId) {
      const lead = await manager.findOne(Lead, {
        where: { id: finalLeadId },
        relations: ['owner_spoc'],
      });
      previousSpocId = lead?.owner_spoc?.spoc_id || null;
    }

    let allocatedByUser: User | undefined;
    if (allocatedByUserId) {
      allocatedByUser = await manager.findOne(User, {
        where: { id: allocatedByUserId },
      });
    }

    const history = manager.create(LeadAllocationHistory, {
      lead_id: finalLeadId,
      client_id: clientId,
      spoc_id: spocId,
      previous_spoc_id: previousSpocId,
      spoc_type: spocType,
      allocation_type: allocationType,
      allocated_by: allocatedBy,
      notes: notes,
      city_id: cityId,
      source_id: sourceId,
      process_duration_ms: Date.now() - startTime,
      is_successful: isSuccessful,
      allocated_by_user: allocatedByUser,
      allocation_metadata: {
        previousSpocId,
        allocationType,
        clientId,
        cityId,
        sourceId,
        transactionDuration,
        leadId: finalLeadId,
      },
    });

    await manager.save(history);
  }

  /**
   * Get allocation history for a specific Lead
   */
  async getLeadAllocationHistory(
    leadId: number,
  ): Promise<LeadAllocationHistory[]> {
    return this.leadAllocationHistoryRepository.find({
      where: { lead_id: leadId },
      relations: ['spoc', 'previousSpoc', 'city', 'source'],
      order: { allocated_at: 'DESC' },
    });
  }

  /**
   * Get recent allocations for analysis
   */
  async getRecentAllocations(
    limit: number = 100,
    clientId?: number,
  ): Promise<LeadAllocationHistory[]> {
    const query = this.leadAllocationHistoryRepository
      .createQueryBuilder('history')
      .leftJoinAndSelect('history.spoc', 'spoc')
      .leftJoinAndSelect('history.previousSpoc', 'previousSpoc')
      .leftJoinAndSelect('history.city', 'city')
      .leftJoinAndSelect('history.source', 'source')
      .orderBy('history.allocated_at', 'DESC')
      .limit(limit);

    if (clientId) {
      query.where('history.client_id = :clientId', { clientId });
    }

    return query.getMany();
  }

  /**
   * Get allocation stats by SPOCs for a specific client and time period
   */
  async getAllocationStatsBySpoc(
    clientId: number,
    startDate: Date,
    endDate: Date,
  ): Promise<any[]> {
    return this.leadAllocationHistoryRepository
      .createQueryBuilder('history')
      .select('spoc.id', 'spoc_id')
      .addSelect('spoc.name', 'spoc_name')
      .addSelect('COUNT(history.id)', 'allocation_count')
      .leftJoin('history.spoc', 'spoc')
      .where('history.client_id = :clientId', { clientId })
      .andWhere('history.allocated_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('history.is_successful = :isSuccessful', { isSuccessful: true })
      .groupBy('spoc.id')
      .addGroupBy('spoc.name')
      .orderBy('allocation_count', 'DESC')
      .getRawMany();
  }

  /**
   * Get allocation stats by city for a specific client and time period
   */
  async getAllocationStatsByCity(
    clientId: number,
    startDate: Date,
    endDate: Date,
  ): Promise<any[]> {
    return this.leadAllocationHistoryRepository
      .createQueryBuilder('history')
      .select('city.id', 'city_id')
      .addSelect('city.name', 'city_name')
      .addSelect('COUNT(history.id)', 'allocation_count')
      .leftJoin('history.city', 'city')
      .where('history.client_id = :clientId', { clientId })
      .andWhere('history.allocated_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('history.is_successful = :isSuccessful', { isSuccessful: true })
      .groupBy('city.id')
      .addGroupBy('city.name')
      .orderBy('allocation_count', 'DESC')
      .getRawMany();
  }

  /**
   * Helper function fro creating lead history from allocation
   */
  async createLeadHistoryFromAllocation(params: {
    lead: Lead;
    spocId: number;
    allocationType: AllocationTypeEnum;
    allocatedBy: string;
    allocatedByUserId?: number;
    previousSpocId?: number;
    spocType?: SpocType;
  }): Promise<void> {
    const {
      lead,
      spocId,
      allocationType,
      allocatedBy,
      allocatedByUserId,
      previousSpocId,
      spocType,
    } = params;

    const subAction =
      allocationType === AllocationTypeEnum.MANUAL_REASSIGNMENT
        ? 'lead_reallocation'
        : 'lead_allocation';

    const performedByUser = allocatedByUserId
      ? await this.userService.findOne(allocatedByUserId)
      : null;

    await this.leadHistoryService.createLeadHistory({
      action: 'lead_management',
      subAction,
      leadId: lead.id,
      contactId: lead?.contact?.id,
      performedByUser,
      performedBy: allocatedByUserId || null,
      details: {
        candidateId: lead?.contact?.candidate_id,
        name: lead?.contact?.full_name,
        source: lead?.program_interests?.[0]?.lead_source?.name,
        allocatedBy,
        allocatedById: allocatedByUserId,
        allocationType,
        spoc: await this.userService.findOne(spocId),
        spocType,
        ...(previousSpocId && {
          oldSpoc: await this.userService.findOne(previousSpocId),
        }),
      },
    });
  }
}
