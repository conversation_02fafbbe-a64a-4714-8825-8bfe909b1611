import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { UserCity, UserSource, CityRoundRobinState } from '../entities';
import {
  FetchEligibleSpocsParams,
  UpdateCityRoundRobinStateParams,
  UpdateLeadProgInterestStatusParams,
  UpdateLeadOwnerParams,
} from '../types/lead-allocation.type';
import { LeadAllocationCriteriaType } from '../enums/allocation-rule.enum';
import { User } from '@modules/users/entities/user.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadSpoc } from '@modules/leads/entities/lead-spoc.entity';
import { SpocType } from '@modules/leads/enums/spoc.enum';
import { RoleEnum } from '@modules/roles/enums/role.enum';

@Injectable()
export class SpocManagementService {
  private readonly logger = new Logger(SpocManagementService.name);

  constructor(
    @InjectRepository(UserCity)
    private readonly userCityRepository: Repository<UserCity>,

    @InjectRepository(UserSource)
    private readonly userSourceRepository: Repository<UserSource>,

    @InjectRepository(CityRoundRobinState)
    private readonly cityRoundRobinStateRepository: Repository<CityRoundRobinState>,

    @InjectRepository(LeadProgramInterest)
    private readonly leadProgramRepository: Repository<LeadProgramInterest>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Fetches all active SPOCs for a city, irrespective of source
   * Used for city-wide round robin tracking
   */
  async fetchAllCitySpocIds(
    cityId: number,
    clientId: number,
  ): Promise<number[]> {
    try {
      // Using DISTINCT to ensure we get unique SPOC IDs
      // Also joining with user_client_role and role tables to filter by SPOC role
      const allSpocsByCity = await this.userCityRepository
        .createQueryBuilder('uc')
        .select('DISTINCT uc.user_id')
        .innerJoin('user', 'u', 'uc.user_id = u.id')
        .innerJoin('user_client', 'ucl', 'ucl.user_id = u.id')
        .innerJoin('user_client_role', 'ucr', 'ucr.user_client_id = ucl.id')
        .innerJoin('role', 'r', 'r.id = ucr.role_id')
        .where('uc.city_id = :cityId', { cityId })
        .andWhere('u.is_active = :isActive', { isActive: true })
        .andWhere('ucl.client_id = :clientId', { clientId })
        .andWhere('r.name = :roleName', { roleName: RoleEnum.SPOC })
        .getRawMany();

      // Extract user_ids from the raw result and sort them
      const uniqueSpocIds = allSpocsByCity
        .map((row) => row.user_id)
        .filter((id) => id !== null && id !== undefined)
        .sort((a, b) => a - b); // Sort for consistent round robin order

      return uniqueSpocIds;
    } catch (error) {
      this.logger.error(
        `Error fetching city SPOCs: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Fetches eligible SPOC IDs based on city and optionally source
   * This runs OUTSIDE the main transaction for performance
   */
  async fetchEligibleSpocIds(
    params: FetchEligibleSpocsParams,
  ): Promise<number[]> {
    const { cityId, clientId, sourceId, criteriaType } = params;

    try {
      // Get all SPOCs for this city using the existing function
      // This already filters by SPOC role
      const spocIdsByCity = await this.fetchAllCitySpocIds(cityId, clientId);

      if (criteriaType === LeadAllocationCriteriaType.CITY_AND_SOURCE) {
        if (sourceId === null) {
          // If rule requires source but lead has none, no SPOC is eligible
          return [];
        }
        // If source criteria is needed, fetch SPOCs by source and intersect with city results
        const eligibleSpocsBySource = await this.userSourceRepository
          .createQueryBuilder('us')
          .select('DISTINCT us.user_id')
          .innerJoin('user', 'u', 'us.user_id = u.id')
          .innerJoin('user_client', 'ucl', 'ucl.user_id = u.id')
          .innerJoin('user_client_role', 'ucr', 'ucr.user_client_id = ucl.id')
          .innerJoin('role', 'r', 'r.id = ucr.role_id')
          .where('us.source_id = :sourceId', { sourceId })
          .andWhere('u.is_active = :isActive', { isActive: true })
          .andWhere('ucl.client_id = :clientId', { clientId })
          .andWhere('r.name = :roleName', { roleName: RoleEnum.SPOC })
          .getRawMany();

        const spocIdsBySource = eligibleSpocsBySource
          .map((row) => row.user_id)
          .filter((id) => id !== null && id !== undefined);

        // Intersection of the two arrays
        const eligibleSpocIds = spocIdsByCity.filter((id) =>
          spocIdsBySource.includes(id),
        );

        return eligibleSpocIds; // Already sorted by fetchAllCitySpocIds
      } else if (criteriaType === LeadAllocationCriteriaType.CITY_ONLY) {
        // If only city criteria is needed, return sorted city SPOCs
        return spocIdsByCity; // Already sorted by fetchAllCitySpocIds
      }

      // Default empty array if no criteria matched
      return [];
    } catch (error) {
      this.logger.error(
        `Error fetching eligible SPOCs: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Updates the Lead's owner (SPOC)
   */
  async updateLeadOwner(params: UpdateLeadOwnerParams): Promise<void> {
    const { manager, leadId, ownerId, spocType = SpocType.CC } = params;

    // Check if this is the first time a SPOC is being assigned to this lead
    const lead = await manager.findOne(Lead, {
      where: { id: leadId },
      relations: ['spocs'],
    });

    const isFirstAssignment = !lead.spocs || lead.spocs.length === 0;

    // Check if there's already a spoc with the same type for this lead
    const existingLeadSpoc =
      lead.spocs && lead.spocs.length > 0
        ? lead.spocs.find((spoc) => spoc.spoc_type === spocType)
        : null;

    let leadSpocId: number;

    if (existingLeadSpoc) {
      // Update the existing lead spoc entry
      await manager.update(
        LeadSpoc,
        { id: existingLeadSpoc.id },
        {
          spoc_id: ownerId,
          updated_at: new Date(),
        },
      );
      leadSpocId = existingLeadSpoc.id;
      this.logger.log(
        `Updated existing LeadSpoc with id ${existingLeadSpoc.id} for Lead ${leadId} and type ${spocType}`,
      );
    } else {
      // Create a new LeadSpoc entry for this assignment
      const leadSpoc = manager.create(LeadSpoc, {
        lead_id: leadId,
        spoc_id: ownerId,
        spoc_type: spocType,
        is_initial_owner: isFirstAssignment,
        client_id: lead.client_id,
      });

      // Save the new LeadSpoc
      const savedLeadSpoc = await manager.save(leadSpoc);
      leadSpocId = savedLeadSpoc.id;
      this.logger.log(
        `Created new LeadSpoc with id ${savedLeadSpoc.id} for Lead ${leadId} and type ${spocType}`,
      );
    }

    // Update the lead's owner and owner_spoc reference
    await manager.update(
      Lead,
      { id: leadId },
      {
        owner_spoc_id: leadSpocId,
        updated_at: new Date(),
      },
    );
  }

  /**
   * Updates the LeadProgInterest's allocation status and assigned SPOC
   * @deprecated Use updateLeadOwner instead as SPOC is now assigned at the lead level
   */
  async updateLeadProgInterestStatus(
    params: UpdateLeadProgInterestStatusParams,
  ): Promise<void> {
    const { manager, leadProgInterestId, spocId } = params;

    // Get the leadProgInterest to find its lead_id
    const leadProgInterest = await manager.findOne(LeadProgramInterest, {
      where: { id: leadProgInterestId },
      select: ['lead_id'],
    });

    if (leadProgInterest && leadProgInterest.lead_id) {
      // Update the lead owner instead
      await this.updateLeadOwner({
        manager,
        leadId: leadProgInterest.lead_id,
        ownerId: spocId,
      });
    }
  }

  /**
   * Updates or creates the city's Round Robin state
   */
  async updateCityRoundRobinState(
    params: UpdateCityRoundRobinStateParams,
  ): Promise<void> {
    const { manager, cityId, spocId, clientId } = params;

    // Check if the record exists
    const existingState = await manager.findOne(CityRoundRobinState, {
      where: { city_id: cityId, client_id: clientId },
    });

    if (existingState) {
      // Update existing record
      await manager.update(
        CityRoundRobinState,
        { city_id: cityId, client_id: clientId },
        {
          last_allocated_spoc_id: spocId,
          updated_at: new Date(),
        },
      );
    } else {
      // Create new record
      const newState = manager.create(CityRoundRobinState, {
        city_id: cityId,
        client_id: clientId,
        last_allocated_spoc_id: spocId,
        updated_at: new Date(),
      });
      await manager.save(newState);
    }
  }

  /**
   * Find the next SPOC for round robin allocation based on city and eligible SPOCs
   */
  async findNextRoundRobinSpoc(
    cityId: number,
    clientId: number,
    eligibleSpocIds: number[],
  ): Promise<number | null> {
    // If no eligible SPOCs, return null
    if (!eligibleSpocIds || eligibleSpocIds.length === 0) {
      return null;
    }

    // Get current city round robin state
    const cityState = await this.cityRoundRobinStateRepository.findOne({
      where: { city_id: cityId, client_id: clientId },
    });

    // If no existing state or last allocated SPOC
    if (!cityState || !cityState.last_allocated_spoc_id) {
      return eligibleSpocIds[0];
    }

    // Get all SPOCs for this city for round-robin order
    const allCitySpocIds = await this.fetchAllCitySpocIds(cityId, clientId);

    // Find the last allocated SPOC's position in the city-wide list
    const lastAllocatedSpocId = cityState.last_allocated_spoc_id;
    const lastSpocIndex = allCitySpocIds.indexOf(lastAllocatedSpocId);

    // If last allocated SPOC is not in city list anymore
    if (lastSpocIndex === -1) {
      return eligibleSpocIds[0];
    }

    // Try each SPOC in the city's round-robin order until we find an eligible one
    let checkedCount = 0;
    let currentIndex = (lastSpocIndex + 1) % allCitySpocIds.length;

    while (checkedCount < allCitySpocIds.length) {
      const candidateSpocId = allCitySpocIds[currentIndex];

      // Check if this SPOC is eligible for this lead
      if (eligibleSpocIds.includes(candidateSpocId)) {
        return candidateSpocId;
      }

      // Move to next SPOC in round-robin order
      currentIndex = (currentIndex + 1) % allCitySpocIds.length;
      checkedCount++;
    }

    // If we couldn't find any eligible SPOC after going through entire list
    // This shouldn't happen since we already checked eligibleSpocIds is not empty
    this.logger.warn(
      `Could not find next round robin SPOC in city ${cityId} after checking all ${allCitySpocIds.length} SPOCs.`,
    );
    return eligibleSpocIds[0]; // Fallback to first eligible
  }

  /**
   * Get all LeadProgInterests where the lead has a specific owner (SPOC)
   */
  async getLeadProgInterestsBySpoc(
    spocId: number,
  ): Promise<LeadProgramInterest[]> {
    return this.leadProgramRepository
      .createQueryBuilder('lpi')
      .innerJoin('lpi.lead', 'lead')
      .where('lead.owner_spoc_id = :spocId', { spocId })
      .orderBy('lpi.updated_at', 'DESC')
      .getMany();
  }

  /**
   * Get all SPOCs for a specific client
   */
  async getSpocsByClient(clientId: number): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .innerJoin('user_client', 'uc', 'uc.user_id = user.id')
      .where('uc.client_id = :clientId', { clientId })
      .andWhere('user.is_active = :isActive', { isActive: true })
      .getMany();
  }

  /**
   * Map a SPOC to a city
   */
  async mapSpocToCity(spocId: number, cityId: number): Promise<UserCity> {
    // Check if mapping already exists
    const existingMapping = await this.userCityRepository.findOne({
      where: { user_id: spocId, city_id: cityId },
    });

    if (existingMapping) {
      return existingMapping;
    }

    // Create new mapping
    const mapping = this.userCityRepository.create({
      user_id: spocId,
      city_id: cityId,
      created_at: new Date(),
      updated_at: new Date(),
    });

    return this.userCityRepository.save(mapping);
  }

  /**
   * Map a SPOC to a source
   */
  async mapSpocToSource(spocId: number, sourceId: number): Promise<UserSource> {
    // Check if mapping already exists
    const existingMapping = await this.userSourceRepository.findOne({
      where: { user_id: spocId, source_id: sourceId },
    });

    if (existingMapping) {
      return existingMapping;
    }

    // Create new mapping
    const mapping = this.userSourceRepository.create({
      user_id: spocId,
      source_id: sourceId,
      created_at: new Date(),
      updated_at: new Date(),
    });

    return this.userSourceRepository.save(mapping);
  }

  /**
   * Get all cities a SPOC is mapped to
   */
  async getSpocCities(spocId: number): Promise<number[]> {
    const mappings = await this.userCityRepository.find({
      where: { user_id: spocId },
    });

    return mappings.map((mapping) => mapping.city_id);
  }

  /**
   * Get all sources a SPOC is mapped to
   */
  async getSpocSources(spocId: number): Promise<number[]> {
    const mappings = await this.userSourceRepository.find({
      where: { user_id: spocId },
    });

    return mappings.map((mapping) => mapping.source_id);
  }

  /**
   * Check if a SPOC is eligible for a specific lead (city and source)
   */
  async isSpocEligibleForLead(
    spocId: number,
    cityId: number,
    sourceId: number | null,
    criteriaType: LeadAllocationCriteriaType,
  ): Promise<boolean> {
    // Check if SPOC is mapped to the city
    const cityMapping = await this.userCityRepository.findOne({
      where: { user_id: spocId, city_id: cityId },
    });

    if (!cityMapping) {
      return false;
    }

    // If only city criteria needed, SPOC is eligible
    if (criteriaType === LeadAllocationCriteriaType.CITY_ONLY) {
      return true;
    }

    // If source criteria needed, check if SPOC is mapped to the source
    if (criteriaType === LeadAllocationCriteriaType.CITY_AND_SOURCE) {
      if (sourceId === null) {
        return false;
      }

      const sourceMapping = await this.userSourceRepository.findOne({
        where: { user_id: spocId, source_id: sourceId },
      });

      return !!sourceMapping;
    }

    return false;
  }
}
