import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LeadSource } from '../entities';
import { Repository } from 'typeorm';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { Campaign } from '@modules/campaigns/entities/campaign.entity';

@Injectable()
export class LeadSourceService {
  // This service can be expanded with methods to handle lead source operations
  // such as creating, updating, deleting, and retrieving lead sources.
  // Currently, it serves as a placeholder for future functionality.
  constructor(
    @InjectRepository(LeadSource)
    private readonly leadSourceRepository: Repository<LeadSource>,
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
  ) {}

  findOne(id: number) {
    return this.leadSourceRepository.findOne({ where: { id: id } });
  }

  async findAllLeadSources(): Promise<
    PaginatedResponse<LeadSource & { campaigns: Campaign[] }>
  > {
    try {
      // Get all lead sources first
      const leadSources = await this.leadSourceRepository.find({
        order: { id: 'ASC' },
      });

      // Use a single query to get all campaigns grouped by lead_source_id
      const campaigns = await this.campaignRepository
        .createQueryBuilder('campaign')
        .leftJoinAndSelect('campaign.lead_source', 'leadSource')
        .where('campaign.lead_source_id IS NOT NULL')
        .andWhere('campaign.is_active = :isactive', { isactive: true })
        .orderBy('campaign.lead_source_id', 'ASC')
        .addOrderBy('campaign.id', 'ASC')
        .getMany();

      // Group campaigns by lead_source_id
      const campaignsBySourceId = campaigns.reduce(
        (acc, campaign) => {
          const sourceId = campaign.lead_source_id;
          if (!acc[sourceId]) {
            acc[sourceId] = [];
          }

          //eslint-disable-next-line
          const { lead_source, ...campaignWithoutSource } = campaign;

          acc[sourceId].push(campaignWithoutSource);
          return acc;
        },
        {} as Record<number, Campaign[]>,
      );

      // Combine lead sources with their campaigns
      const leadSourcesWithCampaigns = leadSources.map((source) => ({
        ...source,
        campaigns: campaignsBySourceId[source.id] || [],
      }));

      const total = leadSources.length;

      return {
        data: leadSourcesWithCampaigns,
        meta: {
          total,
          page: 1,
          size: leadSourcesWithCampaigns.length,
          totalPages: Math.ceil(total / leadSourcesWithCampaigns.length) || 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };
    } catch (error) {
      throw new Error(`Error fetching lead sources: ${error.message}`);
    }
  }
}
