import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { AllocationTypeEnum } from '../enums/allocation-type.enum';
import { LeadAllocationCriteriaType } from '../enums/allocation-rule.enum';
import {
  AllocateLeadParams,
  ManuallyReassignLeadParams,
} from '../types/lead-allocation.type';
import { ClientLeadAllocationRule } from '../entities';
import { SpocManagementService } from './spoc-management.service';
import { LeadAllocationHistoryService } from './lead-allocation-history.service';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { UserService } from '@modules/users/services/user.service';

@Injectable()
export class LeadAllocationService {
  private readonly logger = new Logger(LeadAllocationService.name);
  private readonly TRANSACTION_TIMEOUT_MS = 10000; // 10 seconds max for allocation transaction

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,

    @InjectRepository(ClientLeadAllocationRule)
    private readonly clientleadAllocationRuleRepository: Repository<ClientLeadAllocationRule>,

    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,

    private readonly spocManagementService: SpocManagementService,
    private readonly historyService: LeadAllocationHistoryService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  /**
   * Main function to allocate a lead to an appropriate SPOC
   */
  async allocateCCSpocToLead(params: AllocateLeadParams): Promise<boolean> {
    const { leadId, sourceId, allocatedBy = 'system' } = params;

    try {
      // First get basic information about the lead program interest without a transaction
      const leadPreCheck = await this.leadRepository.findOne({
        where: { id: leadId },
        relations: ['client', 'contact'],
        select: ['id', 'client_id'],
      });
      if (!leadPreCheck) {
        this.logger.log(`Lead ${leadId} not found.`);
        return false;
      }

      //! IMPORTANT DETAILS !!!!
      const clientId = leadPreCheck.client_id;
      const leadCityId = leadPreCheck?.contact?.miles_office_id;
      //! IMPORTANT DETAILS !!!!

      // Basic pre-checks outside transaction
      if (!leadCityId || !sourceId) {
        this.logger.log(
          `Lead ${leadId} missing city information. Cannot allocate.`,
        );

        // Record failure outside transaction
        await this.historyService.recordAllocationHistoryNonTransactional({
          lead: leadPreCheck,
          spocId: null,
          allocationType: AllocationTypeEnum.NO_ELIGIBLE_SPOC,
          allocatedBy,
          notes: !leadCityId
            ? 'Missing city information'
            : 'Missing source information',
          clientId,
          cityId: leadCityId,
          sourceId,
        });

        return false;
      }

      // Fetch allocation rules outside transaction
      const rule = await this.clientleadAllocationRuleRepository.findOne({
        where: { client_id: clientId },
      });

      if (!rule) {
        this.logger.log(
          `No allocation rule found for client ${clientId}. Cannot allocate.`,
        );

        // Record failure outside transaction
        await this.historyService.recordAllocationHistoryNonTransactional({
          lead: leadPreCheck,
          spocId: null,
          allocationType: AllocationTypeEnum.NO_ELIGIBLE_SPOC,
          allocatedBy,
          notes: `No allocation rule for client ${clientId}`,
          clientId,
          cityId: leadCityId,
          sourceId: sourceId,
        });

        return false;
      }

      const criteriaType = rule.criteria_type;

      // Get SPOCs eligible for this specific lead (filtered by source if needed)
      const sortedEligibleSpocIds =
        await this.spocManagementService.fetchEligibleSpocIds({
          cityId: leadCityId,
          sourceId: sourceId,
          criteriaType,
          clientId,
        });

      // If no eligible SPOCs found, log and record failure
      if (!sortedEligibleSpocIds || sortedEligibleSpocIds.length === 0) {
        this.logger.log(
          `No eligible SPOCs found for Lead ${leadId} with criteria City: ${leadCityId}, Source: ${sourceId}, Client: ${clientId} (${criteriaType}).`,
        );

        // Record failure outside transaction
        await this.historyService.recordAllocationHistoryNonTransactional({
          lead: leadPreCheck,
          spocId: null,
          allocationType: AllocationTypeEnum.NO_ELIGIBLE_SPOC,
          allocatedBy,
          notes:
            `No active SPOCs matching City: ${leadCityId}` +
            (criteriaType === LeadAllocationCriteriaType.CITY_AND_SOURCE &&
            sourceId
              ? ` and Source: ${sourceId}`
              : '') +
            ` for Client: ${clientId}`,
          clientId,
          cityId: leadCityId,
          sourceId,
        });

        return false;
      }

      //! Start the transaction with a timeout
      const queryRunner = this.dataSource.createQueryRunner();
      const transactionStartTime = Date.now();
      let transactionTimer: NodeJS.Timeout;

      try {
        await queryRunner.connect();

        // Set up a timeout to prevent long-running transactions
        const transactionPromise = new Promise<void>(
          async (resolve, reject) => {
            try {
              await queryRunner.startTransaction('READ COMMITTED'); // Explicit isolation level
              resolve();
            } catch (err) {
              reject(err);
            }
          },
        );

        const timeoutPromise = new Promise<void>((_, reject) => {
          transactionTimer = setTimeout(() => {
            reject(
              new Error(
                `Transaction timeout after ${this.TRANSACTION_TIMEOUT_MS}ms`,
              ),
            );
          }, this.TRANSACTION_TIMEOUT_MS);
        });

        await Promise.race([transactionPromise, timeoutPromise]);

        const manager = queryRunner.manager;
        const startTime = Date.now();

        // Inside transaction, verify data is still valid
        const lead = await manager.findOne(Lead, {
          where: { id: leadId },
          relations: ['owner_spoc', 'contact', 'owner_spoc.spoc'],
          select: ['id', 'client_id', 'owner_spoc', 'contact'],
        });

        if (!lead) {
          throw new Error(`Lead ${leadId} not found or was deleted.`);
        }

        // Use SpocManagementService to determine next SPOC in round-robin
        const nextSpocId =
          await this.spocManagementService.findNextRoundRobinSpoc(
            leadCityId,
            lead.client_id,
            sortedEligibleSpocIds,
          );

        if (nextSpocId === null) {
          throw new Error(`Could not determine next SPOC for Lead ${leadId}.`);
        }

        // Check if the lead already has the same owner
        const currentOwnerId = lead.owner_spoc?.spoc_id;
        if (currentOwnerId === nextSpocId) {
          this.logger.log(
            `Lead ${leadId} already assigned to SPOC ${nextSpocId}. No changes needed.`,
          );

          await queryRunner.commitTransaction();
          return true;
        }

        // Update the lead's owner
        await this.spocManagementService.updateLeadOwner({
          manager,
          leadId,
          ownerId: nextSpocId,
        });

        // Update the Round Robin state for the city
        await this.spocManagementService.updateCityRoundRobinState({
          manager,
          cityId: leadCityId,
          spocId: nextSpocId,
          clientId,
        });

        const transactionDuration = Date.now() - transactionStartTime;

        // Record the allocation history
        await this.historyService.recordAllocationHistory({
          manager,
          lead,
          spocId: nextSpocId,
          spocType: lead.owner_spoc?.spoc_type,
          allocationType: AllocationTypeEnum.SYSTEM_ROUND_ROBIN,
          allocatedBy,
          notes: null,
          clientId,
          cityId: leadCityId,
          sourceId: sourceId,
          startTime,
          isSuccessful: true,
          transactionDuration,
        });

        // Create lead history from allocation for auto allocation
        await this.historyService.createLeadHistoryFromAllocation({
          lead,
          spocId: nextSpocId,
          allocationType: AllocationTypeEnum.SYSTEM_ROUND_ROBIN,
          allocatedBy,
          spocType: lead.owner_spoc?.spoc_type,
        });

        // Log transaction duration for monitoring
        if (transactionDuration > 1000) {
          // Log if transaction takes over 1 second
          this.logger.warn(
            `Lead allocation transaction for lead ${leadId} took ${transactionDuration}ms`,
          );
        }

        // Commit the transaction
        await queryRunner.commitTransaction();

        this.logger.log(
          `Lead ${leadId} allocated to SPOC ${nextSpocId} for Client ${clientId}, City ${leadCityId}. History recorded.`,
        );
        return true;
      } catch (error) {
        // Rollback transaction on error
        try {
          // Only rollback if transaction is active
          if (queryRunner.isTransactionActive) {
            await queryRunner.rollbackTransaction();
          }
        } catch (rollbackError) {
          this.logger.error(
            `Error rolling back transaction: ${rollbackError.message}`,
          );
        }

        this.logger.error(
          `Error allocating Lead ${leadId}: ${error.message}`,
          error.stack,
        );

        // Try to update lead status to indicate allocation failure in a separate transaction
        try {
          await this.leadRepository.update(
            { id: leadId },
            {
              updated_at: new Date(),
            },
          );
        } catch (statusUpdateError) {
          this.logger.error(
            `Failed to update timestamp for Lead ${leadId}: ${statusUpdateError.message}`,
            statusUpdateError.stack,
          );
        }

        return false;
      } finally {
        // Always clear the timeout and release the query runner
        if (transactionTimer) {
          clearTimeout(transactionTimer);
        }

        // Release connection back to the pool, even if an error occurred
        try {
          await queryRunner.release();
        } catch (releaseError) {
          this.logger.error(
            `Error releasing query runner: ${releaseError.message}`,
          );
        }
      }
    } catch (error) {
      // Error in pre-transaction phase
      this.logger.error(
        `Error in pre-transaction phase for lead ${leadId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Method to manually reassign a Lead to a different SPOC
   */
  async manuallyReassignLead(
    params: ManuallyReassignLeadParams,
  ): Promise<boolean> {
    const {
      leadProgInterestId,
      newSpocId,
      reassignedByUserId,
      notes = null,
    } = params;

    //todo : Fix this to handle the different spoc types properly, same spoc type can be reassigned

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const manager = queryRunner.manager;
    const startTime = Date.now();

    try {
      // Fetch the leadProgInterest data to get the lead
      const leadProgInterest = await manager.findOne(LeadProgramInterest, {
        where: { id: leadProgInterestId },
        relations: ['client', 'lead'],
      });

      if (!leadProgInterest || !leadProgInterest.lead) {
        this.logger.log(
          `LeadProgInterest ${leadProgInterestId} or its lead not found for manual reassignment.`,
        );
        await queryRunner.rollbackTransaction();
        return false;
      }

      const leadId = leadProgInterest.lead_id;
      const clientId = leadProgInterest.client_id;
      const leadCityId = leadProgInterest.metadata?.city_id;
      const leadSourceId = leadProgInterest.metadata?.source_id;

      // Get the current owner_id
      const lead = await manager.findOne(Lead, {
        where: { id: leadId },
        relations: ['owner_spoc', 'contact', 'owner_spoc.spoc'],
      });

      //todo : also handle the SPOC type from params

      const previousOwnerId = lead?.owner_spoc?.spoc_id;
      const currentSpocType = lead?.owner_spoc?.spoc_type;

      // Update lead owner with the same spoc type as the previous owner
      await this.spocManagementService.updateLeadOwner({
        manager,
        leadId,
        ownerId: newSpocId,
        spocType: currentSpocType,
      });

      // Record the manual reassignment in history
      await this.historyService.recordAllocationHistory({
        manager,
        lead,
        spocId: newSpocId,
        allocationType: AllocationTypeEnum.MANUAL_REASSIGNMENT,
        allocatedBy: reassignedByUserId?.toString(),
        allocatedByUserId: reassignedByUserId,
        notes,
        clientId,
        cityId: leadCityId,
        sourceId: leadSourceId,
        startTime,
        isSuccessful: true,
      });

      // Create lead history from allocation for manual reassignment
      await this.historyService.createLeadHistoryFromAllocation({
        lead,
        spocId: newSpocId,
        allocationType: AllocationTypeEnum.MANUAL_REASSIGNMENT,
        allocatedBy: reassignedByUserId?.toString(),
        allocatedByUserId: reassignedByUserId,
        previousSpocId: previousOwnerId,
        spocType: currentSpocType,
      });

      await queryRunner.commitTransaction();

      this.logger.log(
        `Lead ${leadId} manually reassigned from SPOC ${previousOwnerId} to SPOC ${newSpocId} by ${reassignedByUserId}.`,
      );
      return true;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error manually reassigning Lead: ${error.message}`,
        error.stack,
      );
      return false;
    } finally {
      await queryRunner.release();
    }
  }
}
