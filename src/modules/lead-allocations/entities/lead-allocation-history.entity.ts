import { User } from '@modules/users/entities/user.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { AllocationTypeEnum } from '../enums/allocation-type.enum';
import { Lead } from '@modules/leads/entities/lead.entity';
import { SpocType } from '@modules/leads/enums/spoc.enum';

@Entity('lead_allocation_history')
export class LeadAllocationHistory extends ClientAwareEntity {
  @ManyToOne(() => Lead, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lead_id' })
  @Index('idx_lah_lead_id')
  lead: Lead;

  @Column({ type: 'integer', nullable: true })
  lead_id: number;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'spoc_id' })
  @Index('idx_lah_spoc_id')
  spoc: User;

  @Column({ type: 'integer', nullable: true })
  spoc_id: number;

  @Column({
    type: 'enum',
    enum: SpocType,
    nullable: true,
  })
  spoc_type: SpocType;

  // Previous SPOC if this is a reassignment
  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'previous_spoc_id' })
  previous_spoc: User;

  @Column({ type: 'integer', nullable: true })
  previous_spoc_id: number;

  @Column({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    name: 'allocated_at',
  })
  @Index('idx_lah_allocated_at')
  allocated_at: Date;

  @Column({
    type: 'enum',
    enum: AllocationTypeEnum,
    nullable: false,
  })
  allocation_type: AllocationTypeEnum;

  @Column({ type: 'varchar', length: 255, nullable: true })
  allocated_by: string;

  @Index()
  @ManyToOne(() => User)
  allocated_by_user: User;

  // Optional city and source relationships for filtering
  @Column({ type: 'integer', nullable: true })
  city_id: number;

  @Column({ type: 'integer', nullable: true })
  source_id: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'jsonb', nullable: true })
  allocation_metadata: Record<string, any>;

  @Column({ type: 'integer', nullable: true })
  process_duration_ms: number;

  @Column({ type: 'boolean', default: true })
  is_successful: boolean;
}
