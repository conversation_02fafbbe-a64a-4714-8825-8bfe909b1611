import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Column, Entity } from 'typeorm';
import {
  LeadAllocationCriteriaType,
  LeadAllocationMethod,
} from '../enums/allocation-rule.enum';

@Entity('client_lead_allocation_rule')
export class ClientLeadAllocationRule extends ClientAwareEntity {
  @Column({
    type: 'enum',
    enum: LeadAllocationCriteriaType,
    nullable: false,
  })
  criteria_type: LeadAllocationCriteriaType;

  @Column({
    type: 'enum',
    enum: LeadAllocationMethod,
    default: 'round_robin',
  })
  allocation_method: LeadAllocationMethod;
}
