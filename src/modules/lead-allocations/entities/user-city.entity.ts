import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { MilesOffice } from '@modules/miles-offices/entities/miles-office.entity';
import { User } from '@modules/users/entities/user.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity('user_city')
export class UserCity extends ClientAwareEntity {
  @PrimaryColumn()
  user_id: number;

  @PrimaryColumn()
  city_id: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => MilesOffice, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'city_id' })
  city: MilesOffice;
}
