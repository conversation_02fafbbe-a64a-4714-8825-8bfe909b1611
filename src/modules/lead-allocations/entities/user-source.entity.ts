import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { LeadSource } from './lead-source.entity';
import { User } from '@modules/users/entities/user.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity('user_source')
export class UserSource extends ClientAwareEntity {
  @PrimaryColumn()
  user_id: number;

  @PrimaryColumn()
  source_id: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => LeadSource, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'source_id' })
  source: LeadSource;
}
