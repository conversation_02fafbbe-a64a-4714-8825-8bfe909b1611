import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { MilesOffice } from '@modules/miles-offices/entities/miles-office.entity';
import { User } from '@modules/users/entities/user.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity('city_round_robin_state')
export class CityRoundRobinState extends ClientAwareEntity {
  @PrimaryColumn()
  city_id: number;

  @Column({ nullable: true })
  last_allocated_spoc_id: number;

  @ManyToOne(() => MilesOffice, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'city_id' })
  city: MilesOffice;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'last_allocated_spoc_id' })
  last_allocated_spoc: User;
}
