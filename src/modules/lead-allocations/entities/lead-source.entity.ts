import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity } from 'typeorm';

export enum LeadBuckets {
  bucket_0 = 0,
  bucket_1 = 1,
  bucket_2 = 2,
  bucket_3 = 3,
}

@Entity('lead_source')
export class LeadSource extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    nullable: false,
  })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  icon: string;

  @Column({ type: 'enum', enum: LeadBuckets, nullable: true })
  bucket: LeadBuckets;
}
