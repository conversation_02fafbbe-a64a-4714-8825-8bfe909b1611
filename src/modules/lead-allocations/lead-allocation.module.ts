import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LeadAllocationController } from './lead-allocation.controller';
import { LeadAllocationService } from './services/lead-allocation.service';
import { SpocManagementService } from './services/spoc-management.service';
import { LeadAllocationHistoryService } from './services/lead-allocation-history.service';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { User } from '@modules/users/entities/user.entity';
import { LeadSpoc } from '@modules/leads/entities/lead-spoc.entity';
import { LeadSourceService } from './services/lead-source.service';
import { ClientLeadAllocationRule } from './entities/client-lead-allocation-rule.entity';
import { UserCity } from './entities/user-city.entity';
import { UserSource } from './entities/user-source.entity';
import { CityRoundRobinState } from './entities/city-round-robin-state.entity';
import { LeadAllocationHistory } from './entities/lead-allocation-history.entity';
import { LeadSource } from './entities';
import { Campaign } from '@modules/campaigns/entities/campaign.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Lead,
      LeadProgramInterest,
      User,
      LeadSpoc,
      ClientLeadAllocationRule,
      UserCity,
      UserSource,
      CityRoundRobinState,
      LeadAllocationHistory,
      LeadSource,
      Campaign,
    ]),
  ],
  controllers: [LeadAllocationController],
  providers: [
    LeadAllocationService,
    SpocManagementService,
    LeadAllocationHistoryService,
    LeadSourceService,
  ],
  exports: [
    LeadAllocationService,
    SpocManagementService,
    LeadAllocationHistoryService,
    LeadSourceService,
  ],
})
export class LeadAllocationModule {}
