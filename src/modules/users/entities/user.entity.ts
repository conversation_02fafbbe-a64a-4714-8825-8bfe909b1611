import { UUID } from 'crypto';
import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Entity,
  Column,
  OneToMany,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UserClient } from 'src/modules/user-clients/entities/user-client.entity';
import { CallingMode } from '../enums/calling-mode.enum';
import { CallType } from '@modules/call-logs/enums/call-log.enum';

@Entity()
export class User extends BaseEntity {
  @Column({
    type: 'uuid',
    unique: true,
    nullable: false,
    default: () => 'gen_random_uuid()',
  })
  uuid: UUID;

  @Column({ type: 'varchar', unique: true, nullable: true })
  employee_id: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  country_code: string;

  @Column({ type: 'varchar', default: null })
  official_number: string;

  @Column({ type: 'varchar' })
  first_name: string;

  @Column({ type: 'varchar' })
  last_name: string;

  @Column({ type: 'varchar', nullable: true })
  profile_image_url: string;

  @Column({ type: 'varchar', default: null })
  virtual_number: string;

  @Column({ type: 'boolean', default: false })
  is_approved: boolean;

  @Column({ type: 'varchar', nullable: true })
  region: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
  })
  last_login_date: Date;

  @OneToMany(() => UserClient, (userClient) => userClient.user)
  user_clients: UserClient[];

  @Column({
    type: 'enum',
    enum: CallingMode,
    nullable: true,
    default: CallingMode.CLOUD,
  })
  calling_mode: CallingMode;

  @Column({ type: 'enum', enum: CallType, nullable: true })
  call_type: CallType;

  @Index()
  @ManyToOne(() => User)
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @Column({ type: 'number', nullable: true })
  manager_id: number;
}
