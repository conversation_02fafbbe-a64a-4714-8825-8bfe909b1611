/**
 * Utility function to format employee ID based on environment
 * @param employeeId - Original employee ID
 * @returns Formatted employee ID with environment prefix
 */
export function formatEmployeeId(employeeId?: string): string | undefined {
  if (!employeeId) {
    return undefined;
  }

  const environment = process.env.NODE_ENV || 'development';

  switch (environment) {
    case 'production':
      return `MF3_${employeeId}`;
    case 'uat':
    case 'pre-prod':
      return `MF_PRO_${employeeId}`;
    default:
      // For development and other environments, return as-is
      return employeeId;
  }
}
