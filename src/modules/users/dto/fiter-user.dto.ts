import { IsArray, IsOptional } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class FilterUserDto extends PaginationDto {
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return undefined;
  })
  isActive?: boolean;

  @IsOptional()
  @IsArray()
  @Type(() => Number)
  @ApiPropertyOptional({
    type: [Number],
    description:
      'Optional array of client IDs (business units) to filter managers linked to all provided IDs',
  })
  business_units?: number[];
}
