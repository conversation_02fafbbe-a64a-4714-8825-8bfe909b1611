import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { ClientAwareDto } from 'src/common/dto/client-aware.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BusinessUnitWithRoles {
  @ApiProperty({
    description: 'Business Unit/Client ID',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'List of role IDs to assign for this business unit',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  role_ids: number[];

  @ApiPropertyOptional({
    description: 'List of lead source IDs to assign to user',
    example: [1, 2],
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @ApiPropertyOptional({ type: [Number] })
  source_ids?: number[];

  @ApiPropertyOptional({
    description: 'List of city IDs to assign to user',
    example: [1, 2],
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  city_ids?: number[];
}

export class CreateUserDto extends ClientAwareDto {
  @ApiPropertyOptional({
    description: 'User employee ID',
    example: 'MPE001',
  })
  @IsOptional()
  @IsString()
  employee_id?: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Country code for phone number',
    example: '+91',
  })
  @IsOptional()
  @IsString()
  country_code?: string;

  @ApiPropertyOptional({
    description: 'Official phone number',
    example: '9876543210',
  })
  @IsOptional()
  @IsString()
  official_number?: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @IsNotEmpty()
  @IsString()
  first_name: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @IsNotEmpty()
  @IsString()
  last_name: string;

  @ApiPropertyOptional({
    description: 'URL to user profile image',
    example: 'https://example.com/profile.jpg',
  })
  @IsOptional()
  @IsString()
  profile_image_url?: string;

  @ApiPropertyOptional({
    description: 'Virtual phone number',
    example: '1234567890',
  })
  @IsOptional()
  @IsString()
  virtual_number?: string;

  @ApiPropertyOptional({
    description: 'Whether user can approve actions',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  can_approve?: boolean;

  @ApiPropertyOptional({
    description: 'Manager user ID',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  manager_id?: number;

  @ApiPropertyOptional({
    description: 'Legacy: List of role IDs (use business_unit_ids instead)',
    example: [1, 2],
    type: [Number],
    deprecated: true,
  })
  @ApiProperty({
    description: 'Business units with specific roles for each',
    type: [BusinessUnitWithRoles],
    example: [
      { id: 1, role_ids: [1, 2, 3] },
      { id: 2, role_ids: [2] },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessUnitWithRoles)
  business_units?: BusinessUnitWithRoles[];
}
