import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { BusinessUnitWithRoles } from './create-user.dto';

export class UpdateUserWithRelationsDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  first_name?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  last_name?: string;

  @ApiPropertyOptional({
    description: 'User employee ID',
    example: 'MPE001',
  })
  @IsOptional()
  @IsString()
  employee_id?: string;

  @IsOptional()
  @IsEmail()
  @ApiPropertyOptional()
  email?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  country_code?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  official_number?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  profile_image_url?: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  virtual_number?: string;

  @IsOptional()
  @IsNumber()
  @ApiPropertyOptional()
  manager_id?: number;

  @IsOptional()
  @IsArray()
  @ApiPropertyOptional({ type: [BusinessUnitWithRoles] })
  business_units?: BusinessUnitWithRoles[];
}
