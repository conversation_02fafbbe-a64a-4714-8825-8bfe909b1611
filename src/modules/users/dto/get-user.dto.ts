import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Optional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationDto } from 'src/common/dto/pagination.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for filtering users by business unit and role
 */
export class BusinessUnitRoleFilter {
  @ApiProperty({
    description: 'Business Unit/Client ID',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'List of role IDs to filter by',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  role_ids: number[];

  @ApiPropertyOptional({
    description: 'List of city IDs to filter by for this business unit',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  city_ids?: number[];

  @ApiPropertyOptional({
    description: 'List of source IDs to filter by for this business unit',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  source_ids?: number[];
}

/**
 * Specialized DTO for getAllUsers endpoint with clear filter parameters
 */
export class GetUsersDto extends PaginationDto {
  @ApiPropertyOptional({
    description: 'Filter users by active status',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Search pattern for name or email',
    example: 'john',
  })
  @IsOptional()
  @IsString()
  pattern?: string;

  @ApiPropertyOptional({
    description: 'Filter users by business unit and role combination',
    type: BusinessUnitRoleFilter,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => BusinessUnitRoleFilter)
  business_units?: BusinessUnitRoleFilter;
}
