import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { BusinessUnitWithRoles, CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserWithRelationsDto } from '../dto/update-user.dto';
import { UUID } from 'crypto';
import { UserCacheService } from 'src/common/cache/services/cache.service';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';
import { UserCity, UserSource } from '@modules/lead-allocations/entities';
import { FilterUserDto } from '../dto/fiter-user.dto';
import { Role } from '@modules/roles/enities/role.entity';
import { RoleService } from '@modules/roles/role.service';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { GetUsersDto } from '../dto/get-user.dto';
import { UserClientService } from '@modules/user-clients/services/user-client.service';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { RoleEnum } from '@modules/roles/enums/role.enum';
import { formatEmployeeId } from '../utils/user.util';
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserClient)
    private readonly userClientRepository: Repository<UserClient>,
    @InjectRepository(UserClientRole)
    private readonly userClientRoleRepository: Repository<UserClientRole>,
    @InjectRepository(UserCity)
    private readonly userCityRepository: Repository<UserCity>,
    @InjectRepository(UserSource)
    private readonly userSourceReository: Repository<UserSource>,
    private readonly cacheService: UserCacheService,
    private readonly roleService: RoleService,
    private readonly userClientService: UserClientService,
  ) {}

  async createUser(
    createUserDto: CreateUserDto,
    createdBy?: number,
  ): Promise<User> {
    // Check if email already exists
    const existingEmail = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingEmail) {
      throw new ConflictException(
        `User with email ${createUserDto.email} already exists`,
      );
    }

    const existingEmpId = await this.userRepository.findOne({
      where: { employee_id: createUserDto.employee_id },
    });

    if (existingEmpId) {
      throw new ConflictException(
        `User with employee ID ${createUserDto.employee_id} already exists`,
      );
    }

    // Check if official number already exists (if provided)
    if (createUserDto.official_number) {
      const existingPhone = await this.userRepository.findOne({
        where: { official_number: createUserDto.official_number },
      });

      if (existingPhone) {
        throw new ConflictException(
          `User with phone number ${createUserDto.official_number} already exists`,
        );
      }
    }

    // Check if manager_id is provided (make it mandatory)
    if (!createUserDto.manager_id) {
      throw new ConflictException(`Manager ID is required to create a user`);
    }

    // Verify that the manager exists
    const manager = await this.userRepository.findOneBy({
      id: createUserDto.manager_id,
    });
    if (!manager) {
      throw new NotFoundException(
        `Manager with ID ${createUserDto.manager_id} not found`,
      );
    }

    try {
      // If validations pass, create the user
      const user = this.userRepository.create({
        ...createUserDto,
        created_by: createdBy,
      });
      const savedUser = await this.userRepository.save(user);

      // Enhance the user object with a success message
      return Object.assign(savedUser, {
        _message: `User ${savedUser.first_name} ${savedUser.last_name} created successfully!`,
      });
    } catch (error) {
      // Better error handling with detailed messages
      if (error?.code === '23505') {
        // PostgreSQL unique constraint violation
        if (error.detail?.includes('email')) {
          throw new ConflictException(`User with this email already exists`);
        } else if (error.detail?.includes('official_number')) {
          throw new ConflictException(
            `User with this phone number already exists`,
          );
        }
      }

      // Re-throw the error if it's not handled specifically
      throw error;
    }
  }

  async createUserWithRelations(
    userData: Omit<CreateUserDto, 'business_units'>,
    businessUnitData: BusinessUnitWithRoles[] = [],
    currentUserId: number,
  ): Promise<User> {
    // Format employee_id based on environment using AppConfig
    const formattedUserData = {
      ...userData,
      employee_id: formatEmployeeId(userData.employee_id),
    };

    // Extract business unit IDs for validation
    const businessUnitIds = businessUnitData.map((bu) => bu.id);

    // Verify that the manager exists and belongs to ALL of the specified business units
    if (formattedUserData.manager_id && businessUnitIds.length > 0) {
      // Get the manager's business units
      const managerUserClients = await this.userClientRepository.find({
        where: { user_id: formattedUserData.manager_id },
      });

      const managerBusinessUnitIds = managerUserClients.map(
        (uc) => uc.client_id,
      );

      // Find any business units that the manager doesn't have access to
      const missingBusinessUnits = businessUnitIds.filter(
        (buId) => !managerBusinessUnitIds.includes(buId),
      );

      // If manager is missing any business units, throw an error
      if (missingBusinessUnits.length > 0) {
        throw new ConflictException(
          `Manager (ID: ${formattedUserData.manager_id}) must be associated with all assigned business units. Missing business units: ${missingBusinessUnits.join(', ')}`,
        );
      }
    }

    // 1. Create the user (manager check is now handled in createUser)
    const user = await this.createUser(formattedUserData, currentUserId);

    // 2. If businessUnitData is provided, create user-client relationships
    if (businessUnitData.length > 0) {
      // Check for existing user-client relationships to avoid duplicates
      const existingUserClients = await this.userClientRepository.find({
        where: {
          user_id: user.id,
          client_id: In(businessUnitIds),
        },
      });

      // Filter out business units that already have relationships
      const existingClientIds = existingUserClients.map((uc) => uc.client_id);
      const newBusinessUnitEntries = businessUnitData.filter(
        (bu) => !existingClientIds.includes(bu.id),
      );

      // Store all user clients (new and existing) for role assignment
      const allUserClients = [...existingUserClients];

      if (newBusinessUnitEntries.length > 0) {
        // Create user-client entries only for new business units
        const userClients = newBusinessUnitEntries.map((bu, index) =>
          this.userClientRepository.create({
            user_id: user.id,
            client_id: bu.id,
            is_active: true,
            // Mark the first business unit as primary only if no existing primary
            is_primary_client:
              index === 0 &&
              !existingUserClients.some((uc) => uc.is_primary_client),
          }),
        );

        const savedUserClients =
          await this.userClientRepository.save(userClients);
        allUserClients.push(...savedUserClients);
      }

      // Create a map for quick lookup of user clients by client_id
      const userClientsByClientId = new Map();
      allUserClients.forEach((uc) => {
        userClientsByClientId.set(uc.client_id, uc);
      });

      // 3. Create user-client-role relationships based on the specific roles for each business unit
      // First, get all existing user-client-role relationships
      const userClientIds = allUserClients.map((uc) => uc.id);
      const existingUserClientRoles =
        userClientIds.length > 0
          ? await this.userClientRoleRepository.find({
              where: {
                user_client_id: In(userClientIds),
              },
            })
          : [];

      // Create mapping for quick lookup of existing relationships
      const existingMap = new Map();
      existingUserClientRoles.forEach((ucr) => {
        existingMap.set(`${ucr.user_client_id}-${ucr.role_id}`, true);
      });

      // Process each business unit with its specific roles
      const userClientRolesToCreate = [];

      for (const businessUnit of businessUnitData) {
        const { id: clientId, role_ids: roleIds } = businessUnit;
        const userClient = userClientsByClientId.get(clientId);

        // Skip if we couldn't find the user-client relationship
        if (!userClient || !roleIds || roleIds.length === 0) continue;

        // Add roles that don't already exist
        for (const roleId of roleIds) {
          if (!existingMap.has(`${userClient.id}-${roleId}`)) {
            userClientRolesToCreate.push(
              this.userClientRoleRepository.create({
                user_client_id: userClient.id,
                role_id: roleId,
              }),
            );
          }
        }
      }
      if (userClientRolesToCreate.length > 0) {
        await this.userClientRoleRepository.save(userClientRolesToCreate);
      }

      // 4. Process city relationships for each business unit
      for (const businessUnit of businessUnitData) {
        const { id: clientId, city_ids: businessUnitCityIds } = businessUnit;

        if (businessUnitCityIds && businessUnitCityIds.length > 0) {
          // First check for existing user-city relationships for this specific business unit
          const existingUserCities = await this.userCityRepository.find({
            where: {
              user_id: user.id,
              city_id: In(businessUnitCityIds),
              client_id: clientId,
            },
          });

          // Create a set of existing city IDs for this business unit
          const existingCityIds = new Set(
            existingUserCities.map((uc) => uc.city_id),
          );

          // Create entries only for non-existing cities in this business unit
          const userCities = [];

          for (const cityId of businessUnitCityIds) {
            if (!existingCityIds.has(cityId)) {
              userCities.push(
                this.userCityRepository.create({
                  user_id: user.id,
                  city_id: cityId,
                  client_id: clientId,
                }),
              );
            }
          }

          if (userCities.length > 0) {
            await this.userCityRepository.save(userCities);
          }
        }
      }

      // 5. Process source relationships for each business unit
      for (const businessUnit of businessUnitData) {
        const { id: clientId, source_ids: businessUnitSourceIds } =
          businessUnit;

        if (businessUnitSourceIds && businessUnitSourceIds.length > 0) {
          // First check for existing user-source relationships for this specific business unit
          const existingUserSources = await this.userSourceReository.find({
            where: {
              user_id: user.id,
              source_id: In(businessUnitSourceIds),
              client_id: clientId, // Assuming UserSource entity has client_id from ClientAwareEntity
            },
          });

          // Create a set of existing source IDs for this business unit
          const existingSourceIds = new Set(
            existingUserSources.map((us) => us.source_id),
          );

          // Create entries only for non-existing sources in this business unit
          const userSources = [];

          for (const sourceId of businessUnitSourceIds) {
            if (!existingSourceIds.has(sourceId)) {
              userSources.push(
                this.userSourceReository.create({
                  user_id: user.id,
                  source_id: sourceId,
                  client_id: clientId, // Set the client_id for this source relationship
                }),
              );
            }
          }

          if (userSources.length > 0) {
            await this.userSourceReository.save(userSources);
          }
        }
      }
    }

    // Return the created user with all relationships
    return this.userRepository.findOneBy({ id: user.id });
  }

  async saveUser(user: User) {
    // this.cacheService.clearUserTypeCache(CacheKeys.AUTH_USER, user);
    // this.cacheService.clearBulkUserCache(CacheKeys.SUBORDINATES);
    return this.userRepository.save(user);
  }

  async updateUserWithRelations(
    userId: number,
    updateData: Omit<UpdateUserWithRelationsDto, 'business_units'>,
    businessUnitData: BusinessUnitWithRoles[] = [],
    currentUserId: number,
  ): Promise<User> {
    const formattedUpdateData = {
      ...updateData,
      employee_id: updateData.employee_id
        ? formatEmployeeId(updateData.employee_id)
        : updateData.employee_id,
    };

    // 1. Find the user to update
    const user = await this.findOne(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // 2. Verify email uniqueness if changing
    if (formattedUpdateData.email && formattedUpdateData.email !== user.email) {
      const existingEmail = await this.userRepository.findOne({
        where: { email: formattedUpdateData.email, id: Not(userId) },
      });
      if (existingEmail) {
        throw new ConflictException(
          `Email ${formattedUpdateData.email} is already in use by another user`,
        );
      }
    }

    // 3. Verify phone uniqueness if changing
    if (
      formattedUpdateData.official_number &&
      formattedUpdateData.official_number !== user.official_number
    ) {
      const existingPhone = await this.userRepository.findOne({
        where: {
          official_number: formattedUpdateData.official_number,
          id: Not(userId),
        },
      });
      if (existingPhone) {
        throw new ConflictException(
          `Phone number ${formattedUpdateData.official_number} is already in use by another user`,
        );
      }
    }

    // 4. If manager is changing, verify the new manager exists
    if (
      formattedUpdateData.manager_id &&
      formattedUpdateData.manager_id !== user.manager_id
    ) {
      const manager = await this.userRepository.findOneBy({
        id: formattedUpdateData.manager_id,
      });
      if (!manager) {
        throw new NotFoundException(
          `Manager with ID ${formattedUpdateData.manager_id} not found`,
        );
      }

      // Prevent circular management hierarchy
      if (formattedUpdateData.manager_id === userId) {
        throw new ConflictException(`User cannot be their own manager`);
      }

      // Check if the new manager has all the business units the user will have
      if (businessUnitData.length > 0) {
        const businessUnitIds = businessUnitData.map((bu) => bu.id);
        const managerUserClients = await this.userClientRepository.find({
          where: { user_id: formattedUpdateData.manager_id },
        });
        const managerBusinessUnitIds = managerUserClients.map(
          (uc) => uc.client_id,
        );

        const missingBusinessUnits = businessUnitIds.filter(
          (buId) => !managerBusinessUnitIds.includes(buId),
        );

        if (missingBusinessUnits.length > 0) {
          throw new ConflictException(
            `Manager (ID: ${formattedUpdateData.manager_id}) must be associated with all assigned business units. Missing business units: ${missingBusinessUnits.join(', ')}`,
          );
        }
      }
    }

    // 5. Update the basic user properties
    Object.assign(user, {
      ...formattedUpdateData,
      updated_by: currentUserId,
      updated_at: new Date(),
    });

    // Save the updated user
    await this.userRepository.save(user);

    // 6. Process business unit changes if provided
    if (businessUnitData.length > 0) {
      // Get existing user-client relationships
      const existingUserClients = await this.userClientRepository.find({
        where: { user_id: userId },
      });

      const existingClientIdsMap = new Map(
        existingUserClients.map((uc) => [uc.client_id, uc]),
      );

      // Track business units to add, update or keep as is
      const businessUnitIdsToManage = new Set(
        businessUnitData.map((bu) => bu.id),
      );
      const userClientsToCreate = [];
      const userClientRolesToAdd = [];

      // Process each business unit in the updated data
      for (const businessUnit of businessUnitData) {
        const { id: clientId } = businessUnit;

        // Case 1: New business unit relationship
        if (!existingClientIdsMap.has(clientId)) {
          const newUserClient = this.userClientRepository.create({
            user_id: userId,
            client_id: clientId,
            is_active: true,
            is_primary_client: false, // Never set a new business unit as primary during update
          });

          userClientsToCreate.push(newUserClient);
        }
        // Case 2: Existing business unit - we'll update roles next
      }

      // Save new user-client relationships
      if (userClientsToCreate.length > 0) {
        await this.userClientRepository.save(userClientsToCreate);
      }

      // Now handle roles for all business units (new and existing)
      // First get all user-client records again (including newly created ones)
      const updatedUserClients = await this.userClientRepository.find({
        where: { user_id: userId, client_id: In([...businessUnitIdsToManage]) },
      });

      const userClientByClientId = new Map(
        updatedUserClients.map((uc) => [uc.client_id, uc]),
      );

      // Get existing user-client-roles
      const userClientIds = updatedUserClients.map((uc) => uc.id);
      const existingUserClientRoles = await this.userClientRoleRepository.find({
        where: { user_client_id: In(userClientIds) },
      });

      // Create map of existing role relationships
      const existingRolesMap = new Map();
      existingUserClientRoles.forEach((ucr) => {
        existingRolesMap.set(`${ucr.user_client_id}-${ucr.role_id}`, ucr);
      });

      // Map to track which roles to delete per user-client
      const rolesToKeepByUserClient = new Map();

      // Process roles for each business unit
      for (const businessUnit of businessUnitData) {
        const { id: clientId, role_ids } = businessUnit;
        const userClient = userClientByClientId.get(clientId);

        if (!userClient || !role_ids || role_ids.length === 0) continue;

        // Initialize set of roles to keep for this user-client
        if (!rolesToKeepByUserClient.has(userClient.id)) {
          rolesToKeepByUserClient.set(userClient.id, new Set());
        }

        // Add new roles and track roles to keep
        for (const roleId of role_ids) {
          rolesToKeepByUserClient.get(userClient.id).add(roleId);

          // Add only if it doesn't exist
          if (!existingRolesMap.has(`${userClient.id}-${roleId}`)) {
            userClientRolesToAdd.push(
              this.userClientRoleRepository.create({
                user_client_id: userClient.id,
                role_id: roleId,
              }),
            );
          }
        }
      }

      // Find roles to delete (existing roles not in the updated data)
      const userClientRolesToDelete = existingUserClientRoles.filter((ucr) => {
        const rolesToKeep = rolesToKeepByUserClient.get(ucr.user_client_id);
        return rolesToKeep && !rolesToKeep.has(ucr.role_id);
      });

      // Execute database operations
      if (userClientRolesToDelete.length > 0) {
        await this.userClientRoleRepository.remove(userClientRolesToDelete);
      }

      if (userClientRolesToAdd.length > 0) {
        await this.userClientRoleRepository.save(userClientRolesToAdd);
      }

      // Handle removal of business units not in the update
      const businessUnitIdsToKeep = new Set(
        businessUnitData.map((bu) => bu.id),
      );
      const userClientsToRemove = existingUserClients.filter(
        (uc) => !businessUnitIdsToKeep.has(uc.client_id),
      );

      if (userClientsToRemove.length > 0) {
        // If removing the primary client, set another one as primary
        const isPrimaryRemoved = userClientsToRemove.some(
          (uc) => uc.is_primary_client,
        );

        if (isPrimaryRemoved && updatedUserClients.length > 0) {
          // Set the first remaining client as primary
          const firstRemaining = updatedUserClients.find(
            (uc) => !userClientsToRemove.some((ucr) => ucr.id === uc.id),
          );

          if (firstRemaining) {
            firstRemaining.is_primary_client = true;
            await this.userClientRepository.save(firstRemaining);
          }
        }

        await this.userClientRepository.remove(userClientsToRemove);
      }

      // 7. Process city changes for each business unit
      for (const businessUnit of businessUnitData) {
        const { id: clientId, city_ids: businessUnitCityIds } = businessUnit;

        if (businessUnitCityIds && businessUnitCityIds.length > 0) {
          // Get existing user-city relationships for this business unit
          const existingUserCities = await this.userCityRepository.find({
            where: {
              user_id: userId,
              client_id: clientId,
            },
          });

          // Create a set of existing city IDs for this business unit
          const existingCityIds = new Set(
            existingUserCities.map((uc) => uc.city_id),
          );

          // Create new city entries for cities not already assigned to this business unit
          const userCitiesToCreate = [];
          for (const cityId of businessUnitCityIds) {
            if (!existingCityIds.has(cityId)) {
              userCitiesToCreate.push(
                this.userCityRepository.create({
                  user_id: userId,
                  city_id: cityId,
                  client_id: clientId,
                }),
              );
            }
          }

          if (userCitiesToCreate.length > 0) {
            await this.userCityRepository.save(userCitiesToCreate);
          }

          // Remove cities not in the updated list for this business unit
          const cityIdsSet = new Set(businessUnitCityIds);
          const userCitiesToRemove = existingUserCities.filter(
            (uc) => !cityIdsSet.has(uc.city_id),
          );

          if (userCitiesToRemove.length > 0) {
            await this.userCityRepository.remove(userCitiesToRemove);
          }
        } else {
          // If no city_ids provided for this business unit, remove all existing cities for this business unit
          const existingUserCities = await this.userCityRepository.find({
            where: {
              user_id: userId,
              client_id: clientId,
            },
          });

          if (existingUserCities.length > 0) {
            await this.userCityRepository.remove(existingUserCities);
          }
        }
      }

      // 8. Process source changes for each business unit
      for (const businessUnit of businessUnitData) {
        const { id: clientId, source_ids: businessUnitSourceIds } =
          businessUnit;

        if (businessUnitSourceIds && businessUnitSourceIds.length > 0) {
          // Get existing user-source relationships for this business unit
          const existingUserSources = await this.userSourceReository.find({
            where: {
              user_id: userId,
              client_id: clientId,
            },
          });

          // Create a set of existing source IDs for this business unit
          const existingSourceIds = new Set(
            existingUserSources.map((us) => us.source_id),
          );

          // Create new source entries for sources not already assigned to this business unit
          const userSourcesToCreate = [];
          for (const sourceId of businessUnitSourceIds) {
            if (!existingSourceIds.has(sourceId)) {
              userSourcesToCreate.push(
                this.userSourceReository.create({
                  user_id: userId,
                  source_id: sourceId,
                  client_id: clientId,
                }),
              );
            }
          }

          if (userSourcesToCreate.length > 0) {
            await this.userSourceReository.save(userSourcesToCreate);
          }

          // Remove sources not in the updated list for this business unit
          const sourceIdsSet = new Set(businessUnitSourceIds);
          const userSourcesToRemove = existingUserSources.filter(
            (us) => !sourceIdsSet.has(us.source_id),
          );

          if (userSourcesToRemove.length > 0) {
            await this.userSourceReository.remove(userSourcesToRemove);
          }
        } else {
          // If no source_ids provided for this business unit, remove all existing sources for this business unit
          const existingUserSources = await this.userSourceReository.find({
            where: {
              user_id: userId,
              client_id: clientId,
            },
          });

          if (existingUserSources.length > 0) {
            await this.userSourceReository.remove(existingUserSources);
          }
        }
      }
    }

    // Return the updated user with all relationships
    return this.findOne(userId);
  }

  async getAllUsers(
    filterDto: GetUsersDto,
    clientId?: number,
  ): Promise<PaginatedResponse<any>> {
    const {
      isActive,
      pattern,
      page = 1,
      size = 10,
      business_units: business_unit_with_roles,
    } = filterDto;

    const pageNum = Number(page);
    const sizeNum = Number(size);
    const skip = (pageNum - 1) * sizeNum;
    const trimmedPattern = pattern ? pattern.trim() : undefined;

    // Create a more efficient query using a single-pass strategy
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .select('user.id')
      .distinct(true);

    // Apply basic filters
    if (isActive !== undefined) {
      queryBuilder.andWhere('user.is_active = :isActive', { isActive });
    }

    if (trimmedPattern && trimmedPattern.length > 0) {
      queryBuilder.andWhere(
        '(user.first_name ILIKE :pattern OR user.last_name ILIKE :pattern OR user.email ILIKE :pattern OR user.official_number ILIKE :pattern)',
        { pattern: `%${trimmedPattern}%` },
      );
    }

    // Add clientId filter only once regardless of other filters
    if (clientId) {
      queryBuilder.innerJoin(
        'user_client',
        'uc_main',
        'uc_main.user_id = user.id AND uc_main.client_id = :clientId',
        { clientId },
      );
    }

    // We'll use subqueries for complex filters to improve performance

    // Business unit with roles, cities, and sources filter
    if (
      business_unit_with_roles &&
      Array.isArray(business_unit_with_roles) &&
      business_unit_with_roles.length > 0
    ) {
      // Process each business unit with roles filter
      business_unit_with_roles.forEach((buWithRoles, index) => {
        if (buWithRoles && buWithRoles.id) {
          const {
            id: buId,
            role_ids,
            city_ids: buCityIds,
            source_ids: buSourceIds,
          } = buWithRoles;

          // First ensure the user has the business unit
          queryBuilder.andWhere((qb) => {
            const buSubQuery = qb
              .subQuery()
              .select('1')
              .from('user_client', 'uc')
              .where('uc.user_id = user.id')
              .andWhere(`uc.client_id = ${buId}`);

            return 'EXISTS ' + buSubQuery.getQuery();
          });

          // Then check for role associations if needed
          if (role_ids && role_ids.length > 0) {
            const roleParamName = `roleIds${index}`;

            queryBuilder.andWhere((qb) => {
              const roleSubQuery = qb
                .subQuery()
                .select('1')
                .from('user_client', 'uc')
                .innerJoin(
                  'user_client_role',
                  'ucr',
                  'ucr.user_client_id = uc.id',
                )
                .where('uc.user_id = user.id')
                .andWhere(`uc.client_id = ${buId}`)
                .andWhere(`ucr.role_id IN (:...${roleParamName})`);

              return 'EXISTS ' + roleSubQuery.getQuery();
            });

            // Add parameters for roles
            queryBuilder.setParameter(roleParamName, role_ids);
          }

          // Check for city associations for this business unit
          if (buCityIds && buCityIds.length > 0) {
            const cityParamName = `cityIds${index}`;

            queryBuilder.andWhere((qb) => {
              const citySubQuery = qb
                .subQuery()
                .select('1')
                .from('user_city', 'uc_city')
                .where('uc_city.user_id = user.id')
                .andWhere(`uc_city.client_id = ${buId}`)
                .andWhere(`uc_city.city_id IN (:...${cityParamName})`);

              return 'EXISTS ' + citySubQuery.getQuery();
            });

            // Add parameters for cities
            queryBuilder.setParameter(cityParamName, buCityIds);
          }

          // Check for source associations for this business unit
          if (buSourceIds && buSourceIds.length > 0) {
            const sourceParamName = `sourceIds${index}`;

            queryBuilder.andWhere((qb) => {
              const sourceSubQuery = qb
                .subQuery()
                .select('1')
                .from('user_source', 'us')
                .where('us.user_id = user.id')
                .andWhere(`us.client_id = ${buId}`)
                .andWhere(`us.source_id IN (:...${sourceParamName})`);

              return 'EXISTS ' + sourceSubQuery.getQuery();
            });

            // Add parameters for sources
            queryBuilder.setParameter(sourceParamName, buSourceIds);
          }
        }
      });
    }

    // Get total count with optimized query (avoid full data fetch for count)
    const countQuery = queryBuilder.clone();
    const total = await countQuery.getCount();
    const totalPages = Math.ceil(total / sizeNum);

    // Prepare empty response with pagination meta
    const emptyResponse: PaginatedResponse<any> = {
      data: [],
      meta: {
        total,
        page: pageNum,
        size: sizeNum,
        totalPages,
        hasNextPage: pageNum < totalPages,
        hasPreviousPage: pageNum > 1,
      },
    };

    if (total === 0) {
      return emptyResponse;
    }

    // Get user IDs for the current page, applying pagination directly in the database
    const userRows = await queryBuilder
      .orderBy('user.id', 'ASC')
      .offset(skip)
      .limit(sizeNum)
      .getRawMany();

    // Extract IDs from the result
    const userIds = userRows.map((row) => row.user_id);

    if (userIds.length === 0) {
      return emptyResponse;
    }

    // Single batch query to get all user details at once
    const users = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.manager', 'manager')
      .where('user.id IN (:...userIds)', { userIds })
      .getMany();

    // Batch load all related data in parallel
    const [userClients, userCities, userSources, userClientRoles] =
      await Promise.all([
        this.userClientRepository.find({
          where: { user_id: In(userIds) },
          relations: ['client'],
        }),

        this.userCityRepository.find({
          where: { user_id: In(userIds) },
          relations: ['city'],
        }),

        this.userSourceReository.find({
          where: { user_id: In(userIds) },
          relations: ['source'],
        }),

        this.userClientRepository
          .find({
            where: { user_id: In(userIds) },
          })
          .then(async (ucs) => {
            const ucIds = ucs.map((uc) => uc.id);
            if (ucIds.length === 0) return [];

            return this.userClientRoleRepository.find({
              where: { user_client_id: In(ucIds) },
              relations: ['role'],
            });
          }),
      ]);

    // Build efficient lookup maps
    const clientsByUserId = new Map();
    userClients.forEach((uc) => {
      if (!clientsByUserId.has(uc.user_id)) {
        clientsByUserId.set(uc.user_id, []);
      }
      clientsByUserId.get(uc.user_id).push(uc);
    });

    const rolesByUserClientId = new Map();
    userClientRoles.forEach((ucr) => {
      if (!rolesByUserClientId.has(ucr.user_client_id)) {
        rolesByUserClientId.set(ucr.user_client_id, []);
      }
      rolesByUserClientId.get(ucr.user_client_id).push(ucr);
    });

    const citiesByUserAndClient = new Map();
    userCities.forEach((uc) => {
      const key = `${uc.user_id}-${uc.client_id}`;
      if (!citiesByUserAndClient.has(key)) {
        citiesByUserAndClient.set(key, []);
      }
      citiesByUserAndClient.get(key).push(uc);
    });

    const sourcesByUserAndClient = new Map();
    userSources.forEach((us) => {
      const key = `${us.user_id}-${us.client_id}`;
      if (!sourcesByUserAndClient.has(key)) {
        sourcesByUserAndClient.set(key, []);
      }
      sourcesByUserAndClient.get(key).push(us);
    });

    // Format response with roles, cities, and sources included in business units
    const formattedUsers = users.map((user) => {
      const userClientsList = clientsByUserId.get(user.id) || [];

      // Process business units with roles, cities, and sources
      const businessUnits = userClientsList.map((userClient) => {
        // Get roles for this specific userClient
        const clientRoles = rolesByUserClientId.get(userClient.id) || [];
        const roles = clientRoles.map((ucr) => ({
          id: ucr.role?.id,
          name: ucr.role?.name || 'N/A',
        }));

        // Get cities for this specific user-client combination
        const userClientCities =
          citiesByUserAndClient.get(`${user.id}-${userClient.client_id}`) || [];
        const cities = userClientCities.map((userCity) => ({
          id: userCity.city?.id,
          name: userCity.city?.city || 'N/A',
        }));

        // Get sources for this specific user-client combination
        const userClientSources =
          sourcesByUserAndClient.get(`${user.id}-${userClient.client_id}`) ||
          [];
        const sources = userClientSources.map((userSource) => ({
          id: userSource.source?.id,
          name: userSource.source?.name || 'N/A',
        }));

        return {
          id: userClient.client?.id,
          name: userClient.client?.name || 'N/A',
          is_primary_client: userClient.is_primary_client,
          roles: roles,
          cities: cities,
          sources: sources,
        };
      });

      // Keep the top-level roles array for backward compatibility
      const allRoles = [];
      userClientsList.forEach((userClient) => {
        const clientRoles = rolesByUserClientId.get(userClient.id) || [];
        clientRoles.forEach((ucr) => {
          allRoles.push({
            id: ucr.role?.id,
            name: ucr.role?.name || 'N/A',
            business_unit_id: userClient.client_id,
          });
        });
      });

      // Keep top-level cities for backward compatibility
      const allCities = [];
      userCities.forEach((userCity) => {
        if (userCity.user_id === user.id) {
          allCities.push({
            id: userCity.city?.id,
            name: userCity.city?.city || 'N/A',
            client_id: userCity.client_id,
          });
        }
      });

      return {
        id: user.id,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        employee_id: user.employee_id || '',
        is_active: user.is_active,
        email: user.email || '',
        country_code: user.country_code,
        official_number: user.official_number || 'N/A',
        virtual_number: user.virtual_number || 'N/A',
        roles: allRoles,
        business_units: businessUnits,
        cities: allCities, // Keep for backward compatibility
        manager: user.manager,
      };
    });

    // Return the typed response using the PaginatedResponse interface
    return {
      data: formattedUsers,
      meta: {
        total,
        page: pageNum,
        size: sizeNum,
        totalPages,
        hasNextPage: pageNum < totalPages,
        hasPreviousPage: pageNum > 1,
      },
    };
  }

  async getAllManagers(filterDto: FilterUserDto, clientId?: number) {
    const {
      isActive,
      pattern,
      page = 1,
      size = 10,
      business_units,
    } = filterDto;

    const pageNum = Number(page);
    const sizeNum = Number(size);
    const trimmedPattern = pattern ? pattern.trim() : undefined;

    // Self join User to find managers having subordinates
    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .innerJoin('user', 'subordinate', 'subordinate.manager_id = user.id');

    // Apply same filters as getAllUsers for managers
    if (isActive !== undefined) {
      queryBuilder.andWhere('user.is_active = :isActive', { isActive });
    }

    if (trimmedPattern && trimmedPattern.length > 0) {
      queryBuilder.andWhere(
        '(user.first_name ILIKE :pattern OR user.last_name ILIKE :pattern OR user.email ILIKE :pattern)',
        { pattern: `%${trimmedPattern}%` },
      );
    }

    if (clientId) {
      queryBuilder.innerJoin(
        'user.user_clients',
        'filter_user_client',
        'filter_user_client.client_id = :clientId',
        { clientId },
      );
    }

    // 🔍 INTERSECTION FILTER ON BUSINESS UNITS
    if (business_units?.length > 0) {
      queryBuilder
        .innerJoin(
          UserClient,
          'uc_filter',
          'uc_filter.user_id = user.id AND uc_filter.client_id IN (:...business_units)',
          { business_units },
        )
        .groupBy('user.id')
        .having('COUNT(DISTINCT uc_filter.client_id) = :unitCount', {
          unitCount: business_units.length,
        });
    }

    // Count total distinct managers matching filters
    const total = await queryBuilder.getCount();

    // Pagination
    const skip = (pageNum - 1) * sizeNum;
    queryBuilder.skip(skip).take(sizeNum);

    // Select manager IDs for this page
    const managerIdsRows = await queryBuilder
      .select('user.id')
      .orderBy('user.id', 'ASC')
      .getRawMany();

    const managerIds = managerIdsRows.map((row) => row.user_id);

    if (managerIds.length === 0) {
      return {
        data: [],
        meta: {
          total,
          page: pageNum,
          size: sizeNum,
          totalPages: Math.ceil(total / sizeNum),
          hasNextPage: pageNum < Math.ceil(total / sizeNum),
          hasPreviousPage: pageNum > 1,
        },
      };
    }

    // Fetch full user data for these managers
    const managers = await this.userRepository.findByIds(managerIds);

    // Fetch related data (clients, roles, cities) as in getAllUsers
    // Reuse the logic from your getAllUsers method to get userClients, roles, cities

    const userClients = await this.userClientRepository.find({
      where: { user_id: In(managerIds) },
      relations: ['client'],
    });
    const userClientsMap = userClients.reduce((map, userClient) => {
      if (!map[userClient.user_id]) map[userClient.user_id] = [];
      map[userClient.user_id].push(userClient);
      return map;
    }, {});

    const userClientIds = userClients.map((uc) => uc.id);

    const userClientRoles = await this.userClientRoleRepository.find({
      where: { user_client_id: In(userClientIds) },
      relations: ['role'],
    });
    const rolesByUserClientMap = userClientRoles.reduce((map, ucr) => {
      if (!map[ucr.user_client_id]) map[ucr.user_client_id] = [];
      map[ucr.user_client_id].push(ucr);
      return map;
    }, {});

    const userCities = await this.userCityRepository.find({
      where: { user_id: In(managerIds) },
      relations: ['city'],
    });
    const citiesByUserMap = userCities.reduce((map, userCity) => {
      if (!map[userCity.user_id]) map[userCity.user_id] = [];
      map[userCity.user_id].push(userCity);
      return map;
    }, {});

    // Format final response same way as getAllUsers
    const data = managers.map((user) => {
      const userClientsList = userClientsMap[user.id] || [];

      const businessUnits = userClientsList.map((userClient) => ({
        id: userClient.client?.id,
        name: userClient.client?.name || 'N/A',
        is_primary_client: userClient.is_primary_client,
      }));

      const roles = [];
      userClientsList.forEach((userClient) => {
        const userClientRoles = rolesByUserClientMap[userClient.id] || [];
        userClientRoles.forEach((ucr) => {
          roles.push({
            id: ucr.role?.id,
            name: ucr.role?.name || 'N/A',
            business_unit_id: userClient.client_id,
          });
        });
      });

      const userCityList = citiesByUserMap[user.id] || [];
      const cities = userCityList.map((userCity) => ({
        id: userCity.city?.id,
        name: userCity.city?.city || 'N/A',
        client_id: userCity.client_id,
      }));

      return {
        id: user.id,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        country_code: user.country_code,
        official_number: user.official_number || 'N/A',
        virtual_number: user.virtual_number || 'N/A',
        roles,
        business_units: businessUnits,
        cities,
      };
    });

    return {
      data,
      meta: {
        total,
        page: pageNum,
        size: sizeNum,
        totalPages: Math.ceil(total / sizeNum),
        hasNextPage: pageNum < Math.ceil(total / sizeNum),
        hasPreviousPage: pageNum > 1,
      },
    };
  }

  /**
   * Get complete user information with roles, cities and business units
   * @param userId - User ID
   * @param clientId - Client ID
   * @returns Complete user information for the client
   */
  async getUserDetailedInfo(
    req: AuthenticatedRequest,
    userId: number,
    currentClientId: number,
  ) {
    // Get basic user details with relations
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: {
        manager: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get user client details from user client service
    const userDetails = await this.userClientService.getUserDetailedInfo(
      userId,
      currentClientId,
    );

    // Get user cities for the current client
    const userCities = await this.userCityRepository.find({
      where: {
        user_id: userId,
        client_id: currentClientId,
      },
      relations: ['city'],
    });

    // Format cities for response
    const formattedCities = userCities.map((userCity) => ({
      id: userCity.city?.id,
      name: userCity.city?.city || 'N/A',
      client_id: userCity.client_id,
    }));

    // Return complete user information in the expected format
    return {
      user: {
        ...user,
        roles: userDetails.roles,
        cities: formattedCities,
        business_units: userDetails.business_units,
      },
      currentClient: userDetails.currentClient,
      availableClients: userDetails.availableClients,
    };
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['manager'],
    });
    return user;
  }

  async findByEmpId(empId: string): Promise<User | undefined> {
    const user = await this.userRepository.findOne({
      where: { employee_id: empId },
      relations: ['manager'],
    });
    if (!user) {
      throw new NotFoundException(`User with empId ${empId} not found`);
    }
    return user;
  }

  async findByOfficialNumber(
    officialNumber: string,
  ): Promise<User | undefined> {
    return this.userRepository.findOne({
      where: { official_number: officialNumber },
      relations: ['manager'],
    });
  }

  async findUserByUUID(uuid: UUID): Promise<User | undefined> {
    const user = await this.userRepository.findOne({
      where: {
        uuid: uuid,
      },
    });
    return user;
  }

  /**
   * Get user's client IDs (business units)
   * @param userId - User ID
   * @returns Array of client IDs the user is associated with
   */
  async getUserClientIds(userId: number): Promise<number[]> {
    this.logger?.debug?.(`Getting client IDs for user ID: ${userId}`);

    try {
      const userClients = await this.userClientRepository.find({
        where: {
          user_id: userId,
          is_active: true, // Only get active client associations
        },
        select: ['client_id'], // Only select what we need for performance
      });

      const clientIds = userClients.map((uc) => uc.client_id);

      this.logger?.debug?.(
        `Found ${clientIds.length} client associations for user ID: ${userId}`,
      );
      return clientIds;
    } catch (error) {
      this.logger?.error?.(
        `Error getting client IDs for user ID ${userId}: ${error.message}`,
        error.stack,
      );
      // Return empty array on error rather than throwing to prevent cascade failures
      return [];
    }
  }

  //-----------------Update last login date----------------------------
  async updateLastLoginDate(email: string) {
    const user = await this.userRepository.findOne({ where: { email: email } });
    user.last_login_date = new Date();
    return this.userRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  async getUserByIds(ids: number[]): Promise<User[]> {
    return this.userRepository.find({
      where: {
        id: In(ids),
      },
    });
  }

  async getUserHighestRole(userId: number, clientId: number): Promise<Role> {
    const userClients = await this.userClientRepository.findOne({
      where: { user_id: userId, client_id: clientId },
    });
    if (!userClients) {
      return null;
    }
    const userClientRoles = await this.userClientRoleRepository.find({
      where: { user_client_id: userClients.id },
      relations: ['role'],
    });
    const roles = userClientRoles.map((ucr) => ucr.role);
    if (roles.length === 0) {
      return null;
    }
    let HeighestRole = roles[0];
    let HeighestRoleLevel = await this.roleService.getRoleLevel(
      HeighestRole.id,
    );
    for (const role of roles) {
      const level = await this.roleService.getRoleLevel(role.id);
      if (level > HeighestRoleLevel) {
        HeighestRoleLevel = level;
        HeighestRole = role;
      }
    }
    return HeighestRole;
  }
  async toggleUserStatus(id: number, updatedBy: number): Promise<User> {
    const user = await this.findOne(id);

    user.is_active = !user.is_active;
    user.updated_by = updatedBy;
    user.updated_at = new Date();

    const result = await this.userRepository.save(user);

    // ✅ Clear auth cache when user status changes
    try {
      await this.cacheService.clearUserAuthCache(user);

      this.logger.log(
        `Cleared auth cache for user ${user.id} (UUID: ${user.uuid}) after ${user.is_active ? 'activation' : 'deactivation'}`,
      );
    } catch (cacheError) {
      this.logger.warn(
        `Failed to clear cache for user ${user.id}: ${cacheError.message}`,
      );
    }

    // Return response with appropriate message
    return Object.assign(result, {
      _message: `User ${user.is_active ? 'activated' : 'deactivated'} successfully!`,
    });
  }

  /**
   * Get user IDs for the current user and their subordinates
   * Used for filtering in other services
   */
  async getAllSubordinateIdsWithUser(userId: number): Promise<number[]> {
    // Start with the user's own ID
    const userIds = new Set<number>([userId]);

    // Check if user exists
    const user = await this.findOne(userId);
    if (!user) {
      return Array.from(userIds);
    }

    // Get all subordinates recursively
    await this.collectSubordinateIds(userId, userIds);

    return Array.from(userIds);
  }

  /**
   * Recursively collect all subordinate IDs
   */
  private async collectSubordinateIds(
    managerId: number,
    collectedIds: Set<number> = new Set(),
  ): Promise<void> {
    // Find direct subordinates
    const subordinates = await this.userRepository.find({
      select: ['id'],
      where: { manager_id: managerId },
    });

    // Process each subordinate
    for (const subordinate of subordinates) {
      if (!collectedIds.has(subordinate.id)) {
        collectedIds.add(subordinate.id);
        // Recursively collect their subordinates
        await this.collectSubordinateIds(subordinate.id, collectedIds);
      }
    }
  }

  /**
   * Check if a user has any direct subordinates
   */
  async hasSubordinates(userId: number): Promise<boolean> {
    const count = await this.userRepository.count({
      where: { manager_id: userId },
    });

    return count > 0;
  }

  /**
   * Check if user has admin-level role based on role enum
   */
  async isUserAdminOrSuperAdmin(
    userId: number,
    clientId: number,
  ): Promise<boolean> {
    try {
      this.logger.debug(
        `Checking admin status for user ${userId} in client ${clientId}`,
      );

      // Get user's highest role for the client
      const highestRole = await this.getUserHighestRole(userId, clientId);

      if (!highestRole) {
        this.logger.debug(
          `No role found for user ${userId} in client ${clientId}`,
        );
        return false;
      }

      // Check if the role is admin or super admin
      const adminRoles = [RoleEnum.SUPER_ADMIN, RoleEnum.ADMIN];
      const isAdmin = adminRoles.includes(highestRole.name as RoleEnum);

      this.logger.debug(
        `User ${userId} role: ${highestRole.name}, isAdmin: ${isAdmin}`,
      );

      return isAdmin;
    } catch (error) {
      this.logger.error(
        `Error checking admin status for user ${userId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
