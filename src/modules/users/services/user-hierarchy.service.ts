import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';
import { UserCity } from '@modules/lead-allocations/entities';
import { Role } from '@modules/roles/enities/role.entity';
import { RoleEnum } from '@modules/roles/enums/role.enum';
import { RoleService } from '@modules/roles/role.service';

@Injectable()
export class UserHierarchyService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserClient)
    private readonly userClientRepository: Repository<UserClient>,
    @InjectRepository(UserClientRole)
    private readonly userClientRoleRepository: Repository<UserClientRole>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserCity)
    private readonly userCityRepository: Repository<UserCity>,
    private readonly roleService: RoleService,
  ) {}

  /**
   * Get a user's highest role for a given client
   */
  async getUserHighestRole(userId: number, clientId: number): Promise<Role> {
    const userClients = await this.userClientRepository.findOne({
      where: { user_id: userId, client_id: clientId },
    });

    if (!userClients) {
      return null;
    }

    const userClientRoles = await this.userClientRoleRepository.find({
      where: { user_client_id: userClients.id },
      relations: ['role'],
    });

    const roles = userClientRoles.map((ucr) => ucr.role);

    if (roles.length === 0) {
      return null;
    }

    let highestRole = roles[0];
    let highestRoleLevel = await this.roleService.getRoleLevel(highestRole.id);

    for (const role of roles) {
      const level = await this.roleService.getRoleLevel(role.id);
      if (level > highestRoleLevel) {
        highestRoleLevel = level;
        highestRole = role;
      }
    }

    return highestRole;
  }

  /**
   * Get all users with a specific role as their highest role
   */
  async getUsersWithHighestRole(
    potentialUserIds: number[],
    clientId: number,
    targetRole: RoleEnum,
  ): Promise<number[]> {
    const userIds: number[] = [];

    for (const userId of potentialUserIds) {
      const highestRole = await this.getUserHighestRole(userId, clientId);
      if (highestRole && highestRole.name === targetRole) {
        userIds.push(userId);
      }
    }

    return userIds;
  }

  /**
   * Get user hierarchy structure based on role
   * Admin: Gets all GMs, cities under each GM, and SPOCs for each city
   * GM: Gets only their cities and SPOCs under each city that directly report to them
   * SPOC: Gets only their own data
   */
  async getUserHierarchy(userId: number, clientId: number): Promise<any> {
    try {
      // First, get the user's highest role
      const userRole = await this.getUserHighestRole(userId, clientId);
      if (!userRole) {
        throw new NotFoundException(
          `User with ID ${userId} has no roles for client ${clientId}`,
        );
      }

      const roleName = userRole.name;
      console.log(`User role: ${roleName}`);

      // If user is a SPOC, return only their own details
      if (roleName === RoleEnum.SPOC) {
        const user = await this.userRepository.findOne({
          where: { id: userId },
          select: ['id', 'first_name', 'last_name', 'email'],
        });

        return {
          data: {
            id: userId,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            role: roleName,
          },
          message: 'User hierarchy retrieved successfully',
        };
      }

      // For Admin/Super_Admin and GM roles, build hierarchical structure
      if (
        roleName === RoleEnum.ADMIN ||
        roleName === RoleEnum.SUPER_ADMIN ||
        roleName === RoleEnum.GM
      ) {
        // Get all GM role IDs once
        const gmRoles = await this.roleRepository.find({
          where: { name: RoleEnum.GM },
        });

        if (gmRoles.length === 0) {
          return {
            data: [],
            message: 'No GMs found for this client',
          };
        }

        const gmRoleIds = gmRoles.map((role) => role.id);

        // Get SPOC role IDs once as well
        const spocRoles = await this.roleRepository.find({
          where: { name: RoleEnum.SPOC },
        });

        const spocRoleIds = spocRoles.map((role) => role.id);

        // Get all users who have GM role for this client
        let gmIds: number[] = [];

        if (roleName === RoleEnum.GM) {
          // If user is a GM, only include themselves
          gmIds = [userId];
        } else {
          // For Admin/Super_Admin, find all GMs with optimized queries
          const gmUsers = await this.userClientRoleRepository
            .createQueryBuilder('ucr')
            .innerJoin('user_client', 'uc', 'uc.id = ucr.user_client_id')
            .innerJoin('user', 'u', 'u.id = uc.user_id')
            .where('ucr.role_id IN (:...gmRoleIds)', { gmRoleIds })
            .andWhere('uc.client_id = :clientId', { clientId })
            .andWhere('u.is_active = :isActive', { isActive: true })
            .select('u.id', 'id')
            .addSelect('u.first_name', 'first_name')
            .addSelect('u.last_name', 'last_name')
            .addSelect('u.email', 'email')
            .distinct(true)
            .getRawMany();

          // Filter to users whose highest role is GM
          const potentialGmIds = gmUsers.map((u) => u.id);

          if (potentialGmIds.length > 0) {
            gmIds = await this.getUsersWithHighestRole(
              potentialGmIds,
              clientId,
              RoleEnum.GM,
            );
          }
        }

        if (gmIds.length === 0) {
          return {
            data: [],
            message: 'No GMs found for this client',
          };
        }

        // Get all GM users with their details in one query
        const gms = await this.userRepository.find({
          where: { id: In(gmIds) },
          select: ['id', 'first_name', 'last_name', 'email'],
        });

        // Get all cities for all GMs in one query
        const allGmCities = await this.userCityRepository
          .createQueryBuilder('uc')
          .innerJoinAndSelect('uc.city', 'city')
          .where('uc.client_id = :clientId', { clientId })
          .andWhere('uc.user_id IN (:...gmIds)', { gmIds })
          .getMany();

        // Group cities by GM ID and collect all city IDs
        const citiesByGmId = new Map<number, Map<number, any>>();
        const allCityIds: number[] = [];

        allGmCities.forEach((cityMapping) => {
          if (!citiesByGmId.has(cityMapping.user_id)) {
            citiesByGmId.set(cityMapping.user_id, new Map());
          }

          // Store unique cities to avoid duplicates
          if (!citiesByGmId.get(cityMapping.user_id).has(cityMapping.city_id)) {
            citiesByGmId.get(cityMapping.user_id).set(cityMapping.city_id, {
              id: cityMapping.city_id,
              name: cityMapping.city?.city || 'Unknown',
            });

            if (!allCityIds.includes(cityMapping.city_id)) {
              allCityIds.push(cityMapping.city_id);
            }
          }
        });

        // Get all SPOCs for all GMs in one optimized query
        const spocUsers = await this.userRepository
          .createQueryBuilder('user')
          .innerJoin('user_client', 'uc', 'uc.user_id = user.id')
          .innerJoin('user_client_role', 'ucr', 'ucr.user_client_id = uc.id')
          .where('ucr.role_id IN (:...spocRoleIds)', { spocRoleIds })
          .andWhere('uc.client_id = :clientId', { clientId })
          .andWhere('user.manager_id IN (:...gmIds)', { gmIds })
          .andWhere('user.is_active = :isActive', { isActive: true })
          .select([
            'user.id',
            'user.first_name',
            'user.last_name',
            'user.email',
            'user.manager_id',
          ])
          .getMany();

        // Get all city assignments for these SPOCs
        const spocIds = spocUsers.map((spoc) => spoc.id);

        const spocCityAssignments =
          spocIds.length > 0
            ? await this.userCityRepository.find({
                where: {
                  user_id: In(spocIds),
                  client_id: clientId,
                  city_id: In(allCityIds),
                },
              })
            : [];

        // Create a map of SPOCs by GM ID and city ID
        const spocsByGmAndCity = new Map<number, Map<number, any[]>>();

        // Initialize map structure
        for (const gmId of gmIds) {
          spocsByGmAndCity.set(gmId, new Map());

          const gmCities = citiesByGmId.get(gmId) || new Map();
          for (const [cityId] of gmCities) {
            spocsByGmAndCity.get(gmId).set(cityId, []);
          }
        }

        // Populate the map with SPOCs
        for (const spoc of spocUsers) {
          const gmId = spoc.manager_id;
          if (!gmIds.includes(gmId)) continue;

          // Find all cities this SPOC is assigned to
          const spocCities = spocCityAssignments.filter(
            (ca) => ca.user_id === spoc.id,
          );

          // Add this SPOC to each of their assigned cities under their GM
          for (const cityAssignment of spocCities) {
            const cityId = cityAssignment.city_id;

            // Check if this city belongs to the GM
            const gmCities = citiesByGmId.get(gmId);
            if (gmCities && gmCities.has(cityId)) {
              const citySpocs = spocsByGmAndCity.get(gmId).get(cityId) || [];

              // Check if SPOC is already in the list
              const isSpocAlreadyAdded = citySpocs.some(
                (s) => s.id === spoc.id,
              );

              if (!isSpocAlreadyAdded) {
                citySpocs.push({
                  id: spoc.id,
                  first_name: spoc.first_name,
                  last_name: spoc.last_name,
                  email: spoc.email,
                  role: RoleEnum.SPOC,
                });

                // Update the map
                spocsByGmAndCity.get(gmId).set(cityId, citySpocs);
              }
            }
          }
        }

        // Build the final result structure
        const result = [];

        // For each GM, build their hierarchy
        for (const gm of gms) {
          const gmId = gm.id;
          const gmCitiesMap = citiesByGmId.get(gmId) || new Map();
          const gmSpocsMap = spocsByGmAndCity.get(gmId) || new Map();

          // Create GM data structure
          const gmData = {
            id: gmId,
            first_name: gm.first_name,
            last_name: gm.last_name,
            email: gm.email,
            role: RoleEnum.GM,
            cities: [],
          };

          // Add each city with its SPOCs
          for (const [cityId, cityData] of gmCitiesMap.entries()) {
            gmData.cities.push({
              ...cityData,
              spocs: gmSpocsMap.get(cityId) || [],
            });
          }

          result.push(gmData);
        }

        return {
          data: result,
          message: 'User hierarchy retrieved successfully',
        };
      }

      // Default response if role doesn't match any condition
      return {
        data: [],
        message: 'No hierarchical data available for this role',
      };
    } catch (error) {
      console.error('Error in getUserHierarchy:', error);
      throw new Error(`Failed to get user hierarchy: ${error.message}`);
    }
  }
}
