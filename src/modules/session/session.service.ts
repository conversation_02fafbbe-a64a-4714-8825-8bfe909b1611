import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserService } from '@modules/users/services/user.service';
import { UpdateSessionConfigDto } from './dto/update-session-config.dto';
import { SessionConfig } from './entities/session-config.entity';
import { LeadSession } from './entities/lead-session.entity';
import { SessionProgramLevel } from './entities/session-program-level.entity';
import { CreateSessionConfigDto } from './dto/create-session-config.dto';
import { EngagementType } from './enums/engagement-type.enum';
import { LeadSessionStatus } from './enums/lead-session-status.enum';
import { RoleEnum } from '@modules/roles/enums/role.enum';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { GetSessionDropdownDto } from './dto/get-session-dropdown.dto';
import { GetSessionFilter } from './enums/get-session-filter.enum';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  constructor(
    @InjectRepository(SessionConfig)
    private readonly sessionConfigRepository: Repository<SessionConfig>,
    @InjectRepository(LeadSession)
    private readonly leadSessionRepository: Repository<LeadSession>,
    @InjectRepository(SessionProgramLevel)
    private readonly sessionProgramLevelRepository: Repository<SessionProgramLevel>,
    private readonly userService: UserService,
  ) {}

  async create(
    dto: CreateSessionConfigDto,
    user: Express.User,
  ): Promise<SessionConfig> {
    // Validate parent session exists if parent_id is provided
    let parentSession: SessionConfig | null = null;
    let users: any[] = [];
    if (dto.parent_id) {
      parentSession = await this.sessionConfigRepository.findOne({
        where: {
          id: dto.parent_id,
          client_id: user.currentClientId,
          is_active: true,
        },
      });

      if (!parentSession) {
        throw new BadRequestException('Parent session not found');
      }
    }
    if (dto.owner_user_ids) {
      users = await this.userService.getUserByIds(dto.owner_user_ids);
      if (users.length !== dto.owner_user_ids.length) {
        throw new BadRequestException('Some users not found');
      }
    }

    // Create session config
    const sessionConfig = this.sessionConfigRepository.create({
      session_name: dto.session_name,
      parent_id: dto.parent_id,
      client_id: user.currentClientId,
      session_owners: users,
      is_program_based: dto.is_program_based,
      is_approval_required: dto.is_approval_required,
      session_program_levels: [],
    });

    const savedSession = await this.sessionConfigRepository.save(sessionConfig);

    // Handle session program levels if provided
    if (dto.session_program_levels && dto.session_program_levels.length > 0) {
      const sessionProgramLevels = dto.session_program_levels.map((spl) =>
        this.sessionProgramLevelRepository.create({
          session_id: savedSession.id,
          program_id: spl.program_id,
          level_id: spl.level_id,
          created_by: user.id,
          updated_by: user.id,
        }),
      );
      await this.sessionProgramLevelRepository.save(sessionProgramLevels);
    }

    if (parentSession) {
      // If parent session exists, link the new session as a child
      parentSession.child = savedSession;
      await this.sessionConfigRepository.save(parentSession);
    }
    return savedSession;
  }

  async findAll(
    userId: number,
    clientId: number,
  ): Promise<PaginatedResponse<SessionConfig>> {
    const data = await this.sessionConfigRepository.find({
      where: { client_id: clientId, is_active: true },
      relations: {
        parent: true,
        child: true,
        session_owners: true,
        session_program_levels: {
          program: true,
          level: true,
        },
      },
      order: { session_name: 'ASC' },
    });
    data.forEach(async (d) => {
      d.session_owners = d.session_owners.filter((u) => u.is_active);
      if (d.can_self_assign == true) {
        const user = await this.userService.findOne(userId);
        d.session_owners.push(user);
      }
    });
    return {
      data,
      meta: null,
    };
  }

  async findAllDropdown(
    dto: GetSessionDropdownDto,
    clientId: number,
  ): Promise<PaginatedResponse<SessionConfig>> {
    const data = await this.sessionConfigRepository.find({
      where: { client_id: clientId, is_active: true },
      relations: {
        parent: true,
        child: true,
        session_owners: true,
        session_program_levels: {
          program: true,
          level: true,
        },
      },
      order: { session_name: 'ASC' },
    });
    if (dto.filter === GetSessionFilter.ALL) {
      return {
        data,
        meta: null,
      };
    } else {
      const leadSessions = await this.leadSessionRepository.find({
        where: { lead_id: dto.lead_id, is_active: true },
        relations: {
          session: {
            parent: true,
            child: true,
            session_owners: true,
            session_program_levels: {
              program: true,
              level: true,
            },
          },
        },
      });
      const leadSessionIds = leadSessions.map((ls) => ls.session_config_id);
      let completedids = [];
      for (const ls of leadSessions) {
        const childs = await this.findAllParentSessions(ls.session.id);
        completedids.push(childs.map((c) => c.id));
      }
      return {
        data: data.filter((d) => !leadSessionIds.includes(d.id)),
        meta: null,
      };
    }
  }

  async findOne(id: number, clientId?: number): Promise<SessionConfig> {
    const sessionConfig = await this.sessionConfigRepository.findOne({
      where: { id, client_id: clientId ?? null, is_active: true },
      relations: {
        parent: true,
        child: true,
        session_owners: true,
        session_program_levels: {
          program: true,
          level: true,
        },
      },
    });

    if (!sessionConfig) {
      throw new NotFoundException('Session config not found');
    }

    return sessionConfig;
  }

  async update(
    dto: UpdateSessionConfigDto,
    user: Express.User,
  ): Promise<SessionConfig> {
    const sessionConfig = await this.findOne(dto.id, user.currentClientId);

    // Validate parent session exists if parent_id is provided
    if (dto.parent_id && dto.parent_id !== sessionConfig.parent_id) {
      const parentSession = await this.sessionConfigRepository.findOne({
        where: { id: dto.parent_id, client_id: user.currentClientId },
      });
      if (!parentSession) {
        throw new BadRequestException('Parent session not found');
      }
    }

    // Update session config
    Object.assign(sessionConfig, {
      session_name: dto.session_name ?? sessionConfig.session_name,
      parent_id: dto.parent_id ?? sessionConfig.parent_id,
      is_program_based: dto.is_program_based ?? sessionConfig.is_program_based,
      is_approval_required:
        dto.is_approval_required ?? sessionConfig.is_approval_required,
      updated_by: user.id,
    });

    const savedSession = await this.sessionConfigRepository.save(sessionConfig);

    // Handle session program levels if provided
    if (dto.session_program_levels !== undefined) {
      // Remove existing session program levels
      await this.sessionProgramLevelRepository.delete({ session_id: dto.id });

      // Add new session program levels if any
      if (dto.session_program_levels.length > 0) {
        const sessionProgramLevels = dto.session_program_levels.map((spl) =>
          this.sessionProgramLevelRepository.create({
            session_id: dto.id,
            program_id: spl.program_id,
            level_id: spl.level_id,
            created_by: user.id,
            updated_by: user.id,
          }),
        );
        await this.sessionProgramLevelRepository.save(sessionProgramLevels);
      }
    }

    return savedSession;
  }

  async remove(id: number, userId: number): Promise<SessionConfig> {
    const sessionConfig = await this.findOne(id);

    // Check if session has children
    const childrenCount = await this.sessionConfigRepository.count({
      where: { parent_id: id },
    });

    if (childrenCount > 0) {
      throw new BadRequestException(
        'Cannot delete session config that has child sessions',
      );
    }
    sessionConfig.updated_by = userId;
    sessionConfig.status = false;
    sessionConfig.is_active = false;
    return this.sessionConfigRepository.save(sessionConfig);
  }

  /**
   * Creates a lead session record when an engagement (call or meeting) is completed
   *
   * @param leadId The ID of the lead
   * @param sessionId The ID of the session config
   * @param engagementType The type of engagement (call or meet_and_visit)
   * @param engagementId The ID of the engagement (call_log_id or meet_visit_id)
   * @param metadata Optional additional data to store
   * @param userId The ID of the user creating the record
   * @returns The created lead session record
   */
  async createLeadSession(
    leadId: number,
    sessionConfigId: number,
    engagementType: EngagementType,
    engagementId: number,
    metadata?: Record<string, any>,
    assignedUserId?: number,
    userId?: number,
  ): Promise<LeadSession> {
    try {
      // Validate session exists
      const session = await this.sessionConfigRepository.findOne({
        where: { id: sessionConfigId },
      });

      if (!session) {
        throw new NotFoundException(
          `Session with ID ${sessionConfigId} not found`,
        );
      }
      // Create lead session record
      const assignedUser = await this.userService.findOne(assignedUserId);
      if (!assignedUser && assignedUserId) {
        throw new NotFoundException(
          `Assigned user with ID ${assignedUserId} not found`,
        );
      }

      // Finding the highest role of the assigned user
      const assignedUserRole = await this.userService.getUserHighestRole(
        assignedUserId,
        session.client_id,
      );
      // Check if the assigned user has a role
      if (!assignedUserRole) {
        throw new NotFoundException(
          `Assigned user with ID ${assignedUserId} has no roles`,
        );
      }
      // Check if the assigned user is a SPOC and if approval is required
      const isApprovalRerquired = session.is_approval_required
        ? assignedUserRole.name == RoleEnum.SPOC
          ? true
          : false
        : false;
      const leadSession = this.leadSessionRepository.create({
        lead_id: leadId,
        session_config_id: sessionConfigId,
        engagement_type: engagementType,
        metadata: metadata || {},
        created_by: userId,
        updated_by: userId,
        assigned_user_id: assignedUserId || null, // Use first owner if not provided
        complete_status:
          engagementType === EngagementType.MEET_AND_VISIT
            ? LeadSessionStatus.PENDING
            : LeadSessionStatus.COMPLETED,
        meet_visit_id:
          engagementType === EngagementType.MEET_AND_VISIT
            ? engagementId
            : null,
        call_log_id:
          engagementType === EngagementType.CALL ? engagementId : null,
        client_id: session.client_id, // Use client ID from session
        is_approval_required: isApprovalRerquired,
      });
      // Save and return the lead session
      const savedLeadSession =
        await this.leadSessionRepository.save(leadSession);
      this.logger.log(
        `Created lead session: Lead ${leadId}, Session ${sessionConfigId}, ` +
          `Engagement ${engagementType}:${engagementId}`,
      );
      return savedLeadSession;
    } catch (error) {
      this.logger.error(
        `Error creating lead session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  async updateLeadSessionStatus(
    leadId: number,
    sessionId: number,
    userId: number,
    status: LeadSessionStatus,
  ) {
    try {
      // Find the lead session
      const leadSession = await this.leadSessionRepository.findOne({
        where: {
          lead_id: leadId,
          session_config_id: sessionId,
          complete_status: LeadSessionStatus.PENDING,
        },
        relations: {
          session: true,
        },
      });

      if (!leadSession) {
        return;
      }
      leadSession.complete_status = status;
      leadSession.updated_by = userId;
      return this.leadSessionRepository.save(leadSession);
    } catch (error) {
      this.logger.error(
        `Error cancelling lead session ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async completeLeadSession(
    leadId: number,
    sessionId: number,
    metadata?: Record<string, any>,
    userId?: number,
    failedResult?: any[],
  ): Promise<LeadSession> {
    try {
      // Find the lead session
      const leadSession = await this.leadSessionRepository.findOne({
        where: {
          lead_id: leadId,
          session_config_id: sessionId,
          complete_status: LeadSessionStatus.PENDING,
        },
        relations: {
          session: true,
        },
      });

      if (!leadSession) {
        return;
      }

      // Update the complete status
      leadSession.complete_status = LeadSessionStatus.COMPLETED;
      leadSession.completed_at = new Date();
      leadSession.metadata = metadata || leadSession.metadata || {};
      leadSession.updated_by = userId;
      if (failedResult && failedResult.length > 0) {
        leadSession.metadata = {
          ...leadSession.metadata,
          failedResult,
        };
        leadSession.is_post_actions_completed = false;
      } else {
        leadSession.is_post_actions_completed = true;
      }

      // Save and return the updated lead session
      return await this.leadSessionRepository.save(leadSession);
    } catch (error) {
      this.logger.error(
        `Error completing lead session ${sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateLeadSession(
    leadId: number,
    existing_sessionId: number,
    new_sessionId: number,
    userId: number,
  ): Promise<LeadSession> {
    try {
      // Find the lead session
      const leadSession = await this.leadSessionRepository.findOne({
        where: {
          lead_id: leadId,
          session_config_id: existing_sessionId,
        },
        relations: {
          session: true,
        },
      });

      if (!leadSession) {
        throw new NotFoundException(
          `Lead session with ID ${new_sessionId} not found`,
        );
      }

      // Update the metadata
      leadSession.session_config_id = new_sessionId;
      leadSession.updated_by = userId;

      // Save and return the updated lead session
      return await this.leadSessionRepository.save(leadSession);
    } catch (error) {
      this.logger.error(
        `Error updating lead session ${new_sessionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAllChildSessions(
    parentId: number,
    clientId?: number,
  ): Promise<SessionConfig[]> {
    try {
      // Validate parent session exists
      const parentSession = await this.sessionConfigRepository.findOne({
        where: {
          id: parentId,
          is_active: true,
          ...(clientId && { client_id: clientId }),
        },
      });

      if (!parentSession) {
        throw new NotFoundException(
          `Parent session with ID ${parentId} not found`,
        );
      }

      // Find all direct children of the parent session
      const directChildren = await this.sessionConfigRepository.find({
        where: {
          parent_id: parentId,
          is_active: true,
          ...(clientId && { client_id: clientId }),
        },
        relations: {
          session_owners: true,
        },
        order: { session_name: 'ASC' },
      });

      // If no direct children found, return empty array
      if (!directChildren || directChildren.length === 0) {
        return [];
      }

      // Recursively find all descendants
      const allDescendants: SessionConfig[] = [...directChildren];

      for (const child of directChildren) {
        const childDescendants = await this.findAllChildSessions(
          child.id,
          clientId,
        );
        allDescendants.push(...childDescendants);
      }

      return allDescendants;
    } catch (error) {
      this.logger.error(
        `Error finding child sessions for parent ${parentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
  async findAllParentSessions(
    childId: number,
    clientId?: number,
  ): Promise<SessionConfig[]> {
    try {
      // Validate parent session exists
      const childSession = await this.sessionConfigRepository.findOne({
        where: {
          id: childId,
          is_active: true,
          ...(clientId && { client_id: clientId }),
        },
      });

      if (!childSession) {
        throw new NotFoundException(
          `Parent session with ID ${childId} not found`,
        );
      }

      // Find all direct children of the parent session
      const directParents = await this.sessionConfigRepository.find({
        where: {
          child_id: childId,
          is_active: true,
          ...(clientId && { client_id: clientId }),
        },
        relations: {
          session_owners: true,
        },
        order: { session_name: 'ASC' },
      });

      // If no direct children found, return empty array
      if (!directParents || directParents.length === 0) {
        return [];
      }

      // Recursively find all descendants
      const allAncenters: SessionConfig[] = [...directParents];

      for (const parent of directParents) {
        const childDescendants = await this.findAllParentSessions(
          parent.id,
          clientId,
        );
        allAncenters.push(...childDescendants);
      }

      return allAncenters;
    } catch (error) {
      this.logger.error(
        `Error finding child sessions for parent ${childId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
