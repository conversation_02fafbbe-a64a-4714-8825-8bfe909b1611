import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { SessionService } from './session.service';
import { CreateSessionConfigDto } from './dto/create-session-config.dto';
import { UpdateSessionConfigDto } from './dto/update-session-config.dto';
import { Request } from 'express';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { SessionConfig } from './entities/session-config.entity';
import { GetSessionDropdownDto } from './dto/get-session-dropdown.dto';

@Controller('session')
export class SessionController {
  constructor(private readonly sessionService: SessionService) {}
  @Post('add')
  createSession(
    @Body() createSessionConfigDto: CreateSessionConfigDto,
    @Req() req: Request,
  ) {
    return this.sessionService.create(createSessionConfigDto, req.user);
  }

  @Get('all-sessions')
  getAllSessions(
    @Req() req: Request,
  ): Promise<PaginatedResponse<SessionConfig>> {
    return this.sessionService.findAll(req.user?.id, req.user?.currentClientId);
  }

  @Post('all-sessions-dropdown')
  getAllSessionsDropdown(
    @Req() req: Request,
    @Body() getSessionDropdownDto: GetSessionDropdownDto,
  ): Promise<PaginatedResponse<SessionConfig>> {
    return this.sessionService.findAllDropdown(
      getSessionDropdownDto,
      req.user?.currentClientId,
    );
  }

  @Post('update')
  updateSession(
    @Body() updateSessionConfigDto: UpdateSessionConfigDto,
    @Req() req: Request,
  ) {
    return this.sessionService.update(updateSessionConfigDto, req.user);
  }

  @Get('remove/:sessionId')
  removeSession(
    @Param('sessionId', ParseIntPipe) sessionId: number,
    @Req() req: Request,
  ) {
    return this.sessionService.remove(sessionId, req.user?.id);
  }
}
