import { Modu<PERSON> } from '@nestjs/common';
import { SessionController } from './session.controller';
import { SessionService } from './session.service';
import { SessionConfig } from './entities/session-config.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from '@modules/users/user.module';
import { LeadSession } from './entities/lead-session.entity';
import { SessionProgramLevel } from './entities/session-program-level.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([SessionConfig, LeadSession, SessionProgramLevel]),
    UserModule,
  ],
  controllers: [SessionController],
  providers: [SessionService],
  exports: [SessionService],
})
export class SessionModule {}
