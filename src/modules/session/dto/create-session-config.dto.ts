import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsArray,
  ArrayNotEmpty,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateSessionProgramLevelDto {
  @ApiProperty({
    description: 'ID of the program',
    example: 1,
  })
  @IsNumber()
  program_id: number;

  @ApiProperty({
    description: 'ID of the lead level',
    example: 1,
  })
  @IsNumber()
  level_id: number;
}

export class CreateSessionConfigDto {
  @ApiProperty()
  @IsString()
  session_name: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  parent_id?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  child_id?: number;

  @ApiProperty({ type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  owner_user_ids: number[];

  @ApiProperty()
  @IsBoolean()
  is_program_based?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  is_approval_required?: boolean;

  @ApiProperty({
    description: 'Array of session program levels',
    type: [CreateSessionProgramLevelDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSessionProgramLevelDto)
  session_program_levels?: CreateSessionProgramLevelDto[];
}
