import { PartialType } from '@nestjs/mapped-types';
import {
  CreateSessionConfigDto,
  CreateSessionProgramLevelDto,
} from './create-session-config.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateSessionConfigDto extends PartialType(
  CreateSessionConfigDto,
) {
  @ApiProperty()
  @IsNumber()
  id: number;

  // @ApiProperty({
  //   description: 'Array of session program levels',
  //   type: [CreateSessionProgramLevelDto],
  //   required: false,
  // })
  // @IsOptional()
  // @IsArray()
  // @ValidateNested({ each: true })
  // @Type(() => CreateSessionProgramLevelDto)
  // session_program_levels?: CreateSessionProgramLevelDto[];
}
