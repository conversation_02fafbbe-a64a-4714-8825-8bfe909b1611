import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { SessionConfig } from './session-config.entity';
import { Program } from '@modules/programs/entities/program.entity';
import { LeadLevel } from '@modules/leads/entities/lead-level.entity';
import { BaseEntity } from 'src/common/entities/base.entity';

@Entity()
@Index(['session_id', 'program_id'], {
  unique: true,
})
export class SessionProgramLevel extends BaseEntity {
  @Column({
    type: 'int',
    nullable: false,
  })
  session_id: number;

  @ManyToOne(() => SessionConfig, (session) => session.session_program_levels)
  @JoinColumn({ name: 'session_id' })
  session: SessionConfig;

  @ManyToOne(() => Program)
  @JoinColumn({ name: 'program_id' })
  program: SessionConfig;

  @Column({
    type: 'int',
    nullable: false,
  })
  program_id: number;

  @ManyToOne(() => LeadLevel)
  @JoinColumn({ name: 'level_id' })
  level: LeadLevel;

  @Column({
    type: 'int',
    nullable: false,
  })
  level_id: number;
}
