import {
  <PERSON><PERSON><PERSON>,
  Column,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  Index,
  OneToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { LeadLevel } from 'src/modules/leads/entities/lead-level.entity';
import { User } from '@modules/users/entities/user.entity';
import { SessionProgramLevel } from './session-program-level.entity';

@Entity()
export class SessionConfig extends ClientAwareEntity {
  @Column({
    length: 100,
  })
  session_name: string;

  @Column({
    type: 'int',
    nullable: true,
  })
  parent_id: number | null;

  @Index()
  @ManyToOne(() => SessionConfig, (parent) => parent.child, {
    nullable: true,
  })
  @JoinColumn({
    name: 'parent_id',
  })
  parent: SessionConfig | null;

  @Column({
    type: 'int',
    nullable: true,
  })
  child_id: number | null;

  @Index()
  @ManyToOne(() => SessionConfig, (parent) => parent.parent, {
    cascade: true,
    nullable: true,
  })
  @JoinColumn({
    name: 'child_id',
  })
  child: SessionConfig | null;

  @ManyToMany(() => User)
  @JoinTable({ name: 'session_owner' })
  session_owners: User[];

  @Column({ type: 'boolean', nullable: true })
  is_program_based: boolean;

  @Column({ type: 'boolean', nullable: true })
  is_approval_required: boolean;

  @OneToMany(
    () => SessionProgramLevel,
    (sessionProgramLevel) => sessionProgramLevel.session,
    { nullable: true },
  )
  session_program_levels: SessionProgramLevel[];

  @Column({ type: 'boolean', default: false, nullable: true })
  can_self_assign: boolean;
}
