import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { MeetAndVisit } from 'src/modules/meet-and-visit/entities/meet-and-visit.entity';
import { CallLog } from 'src/modules/call-logs/entities/call-log.entity';
import { EngagementType } from '../enums/engagement-type.enum';
import { LeadSessionStatus } from '../enums/lead-session-status.enum';
import { SessionConfig } from './session-config.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { BaseEntity } from 'src/common/entities/base.entity';
import { ClientAwareDto } from 'src/common/dto/client-aware.dto';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { User } from '@modules/users/entities/user.entity';

@Entity('lead_session')
export class LeadSession extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Lead, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @Column({ type: 'integer', nullable: false })
  lead_id: number;

  @Index()
  @ManyToOne(() => SessionConfig)
  @JoinColumn({ name: 'session_config_id' })
  session: SessionConfig;

  @Column({ type: 'integer', nullable: true })
  session_config_id: number;

  @Column({
    type: 'enum',
    enum: EngagementType,
    nullable: false,
  })
  engagement_type: EngagementType;

  @ManyToOne(() => MeetAndVisit, { nullable: true })
  @JoinColumn({ name: 'meet_visit_id' })
  meet_and_visit: MeetAndVisit;

  @Column({ type: 'integer', nullable: true })
  meet_visit_id: number;

  @ManyToOne(() => CallLog, { nullable: true })
  @JoinColumn({ name: 'call_log_id' })
  call_log: CallLog;

  @Column({ type: 'integer', nullable: true })
  call_log_id: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'enum', enum: LeadSessionStatus, nullable: true })
  complete_status: LeadSessionStatus;

  @Column({ type: 'timestamp with time zone', nullable: true })
  completed_at: Date;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_user_id' })
  assigned_user: User;

  @Column({ type: 'integer', nullable: true })
  assigned_user_id: number;

  @Column({ type: 'boolean', nullable: true })
  is_approval_required: boolean;

  @Column({ type: 'boolean', nullable: true })
  is_post_actions_completed: boolean;
}
