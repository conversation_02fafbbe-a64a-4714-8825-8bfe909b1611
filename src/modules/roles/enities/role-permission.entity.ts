import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ManyToOne, <PERSON>in<PERSON>olumn, Index, Unique } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';
import { Role } from './role.entity';
import { Permission } from '@modules/permissions/enities/permission.entity';

@Entity()
@Unique(['role_id', 'permission_id'])
export class RolePermission extends BaseEntity {
  @Column()
  @Index()
  role_id: number;

  @Column()
  @Index()
  permission_id: number;

  @ManyToOne(() => Role, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @ManyToOne(() => Permission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;
}
