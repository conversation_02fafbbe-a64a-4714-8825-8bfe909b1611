import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON><PERSON>o<PERSON>ne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Index,
} from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { RolePermission } from './role-permission.entity';
import { RoleEnum } from '../enums/role.enum';

@Entity()
export class Role extends ClientAwareEntity {
  @Column({
    type: 'enum',
    enum: RoleEnum,
    nullable: false,
  })
  name: RoleEnum;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  @Index()
  parent_role_id: number;

  @ManyToOne(() => Role, (role) => role.childRoles, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'parent_role_id' })
  parentRole: Role;

  @OneToMany(() => Role, (role) => role.parentRole)
  childRoles: Role[];

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  rolePermissions: RolePermission[];
}
