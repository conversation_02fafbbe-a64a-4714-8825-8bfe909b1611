import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from './enities/role.entity';
import { UserClientRole } from '../user-client-roles/entities/user-client-role.entity';
import { RolePermission } from './enities/role-permission.entity';
import { Permission } from '@modules/permissions/enities/permission.entity';
import { RoleService } from './role.service';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { RoleController } from './role.controller';
import { UserClientRoleController } from '@modules/user-client-roles/user-client-role.controller';
import { UserClientRoleService } from '@modules/user-client-roles/services/user-client-role.service';
import { UserClientModule } from '@modules/user-clients/user-client.module';
import { CacheModule } from 'src/common/cache/cache.module';

@Global() // Make this module global
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Role,
      UserClientRole,
      RolePermission,
      Permission,
      UserClient,
    ]),
    UserClientModule,
    CacheModule,
  ],
  controllers: [RoleController, UserClientRoleController],
  providers: [RoleService, UserClientRoleService],
  exports: [RoleService, UserClientRoleService],
})
export class RoleModule {}
