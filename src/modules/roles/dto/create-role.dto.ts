import { IsString, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON><PERSON> } from 'class-validator';
import { ClientAwareDto } from 'src/common/dto/client-aware.dto';
import { RoleEnum } from '../enums/role.enum';

export class CreateRoleDto extends ClientAwareDto {
  @IsString()
  @IsNotEmpty()
  name: RoleEnum;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  category?: string;

  @IsNumber()
  @IsOptional()
  parent_role_id?: number;
}
