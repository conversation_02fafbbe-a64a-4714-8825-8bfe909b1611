import {
  Injectable,
  Logger,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not } from 'typeorm';
import { Cache } from 'cache-manager';
import { Role } from './enities/role.entity';
import { UserClientRole } from '../user-client-roles/entities/user-client-role.entity';
import { RolePermission } from './enities/role-permission.entity';
import { Permission } from '@modules/permissions/enities/permission.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { AssignPermissionsResponse } from './interfaces/role.interface';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { actionIncludes } from '@modules/permissions/config/action-hierarchy';

@Injectable()
export class RoleService {
  private readonly logger = new Logger(RoleService.name);
  private readonly CACHE_TTL = 300; // 5 minutes

  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(UserClientRole)
    private userClientRoleRepository: Repository<UserClientRole>,
    @InjectRepository(RolePermission)
    private rolePermissionRepository: Repository<RolePermission>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    @InjectRepository(UserClient)
    private userClientRepository: Repository<UserClient>,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * Check if a user has specific permission for a client
   * Flow:
   * 1. Check cache
   * 2. Get user roles (including inherited)
   * 3. Check permissions for these roles
   * 4. Cache and return result
   */
  async userHasPermission(
    userId: number,
    clientId: number,
    resource: string,
    action: string,
  ): Promise<boolean> {
    try {
      // Step 1: Check cache first
      const cacheKey = `permission:${userId}:${clientId}:${resource}:${action}`;
      //todo: commented out cache check for now
      //! Uncomment this when cache is ready
      // const cachedResult = await this.cacheManager.get<boolean>(cacheKey);

      // if (cachedResult !== undefined && cachedResult !== null) {
      //   return cachedResult;
      // }

      // Step 2: Get user-client with verification
      const userClient = await this.userClientRepository.findOne({
        where: { user_id: userId, client_id: clientId },
      });

      if (!userClient) {
        await this.cacheManager.set(cacheKey, false, this.CACHE_TTL);
        return false;
      }

      // Step 3: Get user roles with permissions in a single query
      // This reduces database roundtrips by loading all needed relations
      const userRoles = await this.userClientRoleRepository
        .createQueryBuilder('ucr')
        .innerJoinAndSelect('ucr.role', 'role')
        .innerJoinAndSelect('role.rolePermissions', 'rp')
        .innerJoinAndSelect('rp.permission', 'p')
        .where('ucr.user_client_id = :userClientId', {
          userClientId: userClient.id,
        })
        .andWhere('role.client_id = :clientId', { clientId })
        .getMany();

      if (!userRoles.length) {
        await this.cacheManager.set(cacheKey, false, this.CACHE_TTL);
        return false;
      }

      // Step 4: Check permissions using loaded relations and action hierarchy
      // This checks if the user has the exact action or any action that includes it
      const hasPermission = userRoles.some((userRole) =>
        userRole.role.rolePermissions.some(
          (rp) =>
            rp.permission.resource === resource &&
            actionIncludes(rp.permission.action, action),
        ),
      );

      // Step 5: Cache the result
      await this.cacheManager.set(cacheKey, hasPermission, this.CACHE_TTL);
      return hasPermission;
    } catch (error) {
      this.logger.error(
        `Permission check failed: ${error.message}`,
        error.stack,
      );
      // Silent fail with false for security
      return false;
    }
  }

  // Helper method for getting user roles with hierarchy
  async getUserRolesWithHierarchy(
    userId: number,
    clientId: number,
  ): Promise<UserClientRole[]> {
    // Get user-client ID first
    const userClient = await this.userClientRepository.findOne({
      where: { user_id: userId, client_id: clientId },
    });

    if (!userClient) return [];

    // Get direct roles with eager loading
    const userClientRoles = await this.userClientRoleRepository
      .createQueryBuilder('ucr')
      .innerJoinAndSelect('ucr.role', 'role')
      .innerJoinAndSelect('role.rolePermissions', 'rp')
      .innerJoinAndSelect('rp.permission', 'p')
      .where('ucr.user_client_id = :userClientId', {
        userClientId: userClient.id,
      })
      .andWhere('role.client_id = :clientId', { clientId })
      .getMany();

    // Get role IDs for inheritance check
    const roleIds = userClientRoles.map((ucr) => ucr.role_id);

    // Get inherited roles
    const inheritedRoleIds = await this.getInheritedRoleIds(roleIds);

    if (inheritedRoleIds.length) {
      const inheritedRoles = await this.userClientRoleRepository
        .createQueryBuilder('ucr')
        .innerJoinAndSelect('ucr.role', 'role')
        .innerJoinAndSelect('role.rolePermissions', 'rp')
        .innerJoinAndSelect('rp.permission', 'p')
        .where('role.id IN (:...inheritedRoleIds)', { inheritedRoleIds })
        .andWhere('role.client_id = :clientId', { clientId })
        .getMany();

      return [...userClientRoles, ...inheritedRoles];
    }

    return userClientRoles;
  }

  /**
   * Recursively get all inherited role IDs through role hierarchy
   */
  private async getInheritedRoleIds(roleIds: number[]): Promise<number[]> {
    try {
      if (!roleIds?.length) return [];

      // Get parent roles in a single query with better joining
      const parentRoles = await this.roleRepository
        .createQueryBuilder('r')
        .select(['r.id', 'r.parent_role_id', 'parent.id'])
        .leftJoin('r.parentRole', 'parent')
        .where('r.id IN (:...roleIds)', { roleIds })
        .andWhere('r.parent_role_id IS NOT NULL')
        .getMany();

      if (!parentRoles.length) return [];

      // Extract parent IDs and remove duplicates
      const parentIds = [
        ...new Set(
          parentRoles.map((r) => r.parent_role_id).filter((id) => id !== null),
        ),
      ];

      // Recursively get deeper parent roles
      const deeperParentIds = await this.getInheritedRoleIds(parentIds);

      // Combine and remove duplicates
      return [...new Set([...parentIds, ...deeperParentIds])];
    } catch (error) {
      this.logger.error(
        `Failed to get inherited role IDs: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * CRUD Operations for Roles
   */
  async create(createRoleDto: CreateRoleDto, userId: number): Promise<Role> {
    // Check if role with same name exists for this client
    const existing = await this.roleRepository.findOne({
      where: {
        name: createRoleDto.name,
        client_id: createRoleDto.client_id,
      },
    });

    if (existing) {
      throw new ConflictException(
        `Role ${createRoleDto.name} already exists for this client`,
      );
    }

    // If parent_role_id is provided, verify it exists and belongs to same client
    if (createRoleDto.parent_role_id) {
      const parentRole = await this.roleRepository.findOne({
        where: {
          id: createRoleDto.parent_role_id,
          client_id: createRoleDto.client_id,
        },
      });

      if (!parentRole) {
        throw new NotFoundException(
          'Parent role not found or does not belong to this client',
        );
      }
    }

    const role = this.roleRepository.create({
      ...createRoleDto,
      created_by: userId,
    });
    return this.roleRepository.save(role);
  }

  async findOne(id: number, clientId: number): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id, client_id: clientId },
      relations: [
        'rolePermissions',
        'rolePermissions.permission',
        'parentRole',
        'childRoles',
      ],
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async findAll(
    clientId: number,
    page = 1,
    size = 10,
  ): Promise<{ data: Role[]; meta: any }> {
    const skip = (page - 1) * size;

    const [roles, total] = await this.roleRepository.findAndCount({
      where: { client_id: clientId },
      relations: [
        'rolePermissions',
        'rolePermissions.permission',
        'parentRole',
        'childRoles',
      ],
      skip,
      take: size,
    });

    const totalPages = Math.ceil(total / size);

    return {
      data: roles,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async update(
    id: number,
    updateRoleDto: UpdateRoleDto,
    userId: number,
    clientId: number,
  ): Promise<Role> {
    const role = await this.findOne(id, clientId);

    // Check name uniqueness if name is being updated
    if (updateRoleDto.name && updateRoleDto.name !== role.name) {
      const existing = await this.roleRepository.findOne({
        where: {
          name: updateRoleDto.name,
          client_id: clientId,
          id: Not(id),
        },
      });

      if (existing) {
        throw new ConflictException(
          `Role ${updateRoleDto.name} already exists for this client`,
        );
      }
    }

    // If parent_role_id is being updated, verify it exists and belongs to same client
    if (updateRoleDto.parent_role_id !== undefined) {
      if (updateRoleDto.parent_role_id === id) {
        throw new ConflictException('Role cannot be its own parent');
      }

      if (updateRoleDto.parent_role_id) {
        const parentRole = await this.roleRepository.findOne({
          where: {
            id: updateRoleDto.parent_role_id,
            client_id: clientId,
          },
        });

        if (!parentRole) {
          throw new NotFoundException(
            'Parent role not found or does not belong to this client',
          );
        }
      }
    }

    Object.assign(role, {
      ...updateRoleDto,
      updated_by: userId,
    });

    return this.roleRepository.save(role);
  }

  async remove(id: number, clientId: number): Promise<void> {
    const role = await this.findOne(id, clientId);
    await this.roleRepository.remove(role);
  }

  /**
   * Role Permission Management
   */
  async assignPermissions(
    roleId: number,
    permissionIds: number[],
    userId: number,
    clientId: number,
  ): Promise<AssignPermissionsResponse> {
    try {
      // Verify role exists and belongs to client
      const role = await this.findOne(roleId, clientId);

      if (!role) {
        throw new NotFoundException(`Role with ID ${roleId} not found`);
      }
      // Check existing permissions
      const existingPermissions = await this.rolePermissionRepository.find({
        where: {
          role_id: roleId,
          permission_id: In(permissionIds),
        },
      });

      if (existingPermissions.length === permissionIds.length) {
        const existingIds = existingPermissions.map((ep) => ep.permission_id);
        const allMatch = permissionIds.every((id) => existingIds.includes(id));
        if (allMatch) {
          return {
            success: true,
            message: 'Permissions already assigned',
            data: { role_id: roleId, permissions: permissionIds },
          };
        }
      }

      // Verify permissions exist
      const permissions = await this.permissionRepository.find({
        where: { id: In(permissionIds) },
      });

      if (permissions.length !== permissionIds.length) {
        throw new NotFoundException('Some permissions were not found');
      }

      await this.roleRepository.manager.transaction(async (manager) => {
        await manager.delete(RolePermission, { role_id: roleId });

        const rolePermissions = permissionIds.map((permissionId) => ({
          role_id: roleId,
          permission_id: permissionId,
          created_by: userId,
        }));

        await manager.insert(RolePermission, rolePermissions);
      });

      return {
        success: true,
        message: 'Permissions assigned successfully',
        data: { role_id: roleId, permissions: permissionIds },
      };
    } catch (error) {
      this.logger.error(
        `Failed to assign permissions: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getRoleWithPermissions(id: number, clientId: number) {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect('role.rolePermissions', 'rp')
      .leftJoinAndSelect('rp.permission', 'permission')
      .where('role.id = :id', { id })
      .andWhere('role.client_id = :clientId', { clientId })
      .getOne();

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return {
      ...role,
      permissions: role.rolePermissions?.map((rp) => rp.permission) || [],
    };
  }

  async getRoleLevel(roleId: number) {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: {
        parentRole: true,
      },
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${roleId} not found`);
    }

    let level = 0;
    let currentRole = role;

    while (currentRole.parentRole) {
      level++;
      currentRole = currentRole.parentRole;
    }

    return level;
  }
}
