import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  HttpStatus,
  HttpCode,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { RoleService } from './role.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { PermissionGuard } from '../auth/guards/permission.guard';
import { Action, Resource } from '../permissions/enums/permission.enum';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ClientAccessGuard } from '@modules/user-clients/guards/client-access.guard';
import { PaginationDto } from 'src/common/dto/pagination.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  <PERSON>pi<PERSON><PERSON>,
  <PERSON>pi<PERSON><PERSON>y,
} from '@nestjs/swagger';

@ApiTags('roles')
@Controller('roles')
@UseGuards(ClientAccessGuard)
@ApiResource(Resource.ROLE)
@UseGuards(PermissionGuard)
export class RoleController {
  constructor(private readonly rolesService: RoleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiBody({ type: CreateRoleDto })
  async create(
    @Body() createRoleDto: CreateRoleDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    return this.rolesService.create(createRoleDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all roles with pagination' })
  @ApiQuery({ type: PaginationDto })
  async findAll(
    @Req() req: AuthenticatedRequest,
    @Query() paginationDto: PaginationDto,
  ) {
    const clientId = req.user?.currentClientId;

    if (!clientId) {
      return [];
    }

    const { page = 1, size = 10 } = paginationDto;

    const { data, meta } = await this.rolesService.findAll(
      clientId,
      page,
      size,
    );

    const rolesWithPermissions = data.map((role) => ({
      ...role,
      permissions: role.rolePermissions?.map((rp) => rp.permission) || [],
    }));

    return { data: rolesWithPermissions, meta };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  async findOne(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;
    const role = await this.rolesService.findOne(+id, clientId);
    return {
      ...role,
      permissions: role.rolePermissions?.map((rp) => rp.permission),
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  @ApiBody({ type: UpdateRoleDto })
  async update(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    const clientId = req.user?.currentClientId;
    return this.rolesService.update(+id, updateRoleDto, userId, clientId);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequirePermission({ resource: Resource.ROLE, action: Action.DELETE })
  @ApiOperation({ summary: 'Delete role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID', type: Number })
  async remove(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;
    return this.rolesService.remove(+id, clientId);
  }

  @ApiOperation({ summary: 'Get all roles for a specified client ID' })
  @ApiQuery({
    name: 'clientId',
    type: Number,
    required: true,
    description: 'Client ID to filter roles',
  })
  @ApiQuery({ type: PaginationDto }) // To document pagination query params properly
  @Get('client')
  async findAllByClientId(
    @Query('clientId', ParseIntPipe) clientId: number,
    @Query() paginationDto: PaginationDto,
  ) {
    if (!clientId) {
      return [];
    }

    const { page = 1, size = 10 } = paginationDto;

    const { data, meta } = await this.rolesService.findAll(
      clientId,
      page,
      size,
    );

    const rolesWithPermissions = data.map((role) => ({
      ...role,
      permissions: role.rolePermissions?.map((rp) => rp.permission) || [],
    }));

    return { data: rolesWithPermissions, meta };
  }

  @Post('permissions')
  @RequirePermission({ resource: Resource.ROLE, action: Action.UPDATE })
  @ApiOperation({ summary: 'Assign permissions to a role' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        permissionIds: { type: 'array', items: { type: 'number' } },
        roleId: { type: 'number' },
      },
      required: ['permissionIds', 'roleId'],
    },
  })
  async assignPermissions(
    @Body() body: { permissionIds: number[]; roleId: number },
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    const clientId = req.user?.currentClientId;
    return this.rolesService.assignPermissions(
      +body.roleId,
      body.permissionIds,
      userId,
      clientId,
    );
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get roles assigned to a user' })
  @ApiParam({ name: 'userId', description: 'User ID', type: Number })
  async getUserRoles(
    @Param('userId') userId: string,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;
    const roles = await this.rolesService.getUserRolesWithHierarchy(
      +userId,
      clientId,
    );
    return roles.map((ur) => ur.role);
  }
}
