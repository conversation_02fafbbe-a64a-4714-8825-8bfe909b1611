import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  Get,
  Param,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileStorageService } from './services/file-storage.service';
import { FileUploadDto } from './dto/file-upload.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('storage')
@Controller('storage')
export class FileStorageController {
  constructor(private readonly fileStorageService: FileStorageService) {}

  @Post('upload/public')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a public file' })
  @ApiBody({
    description: 'File upload with optional folder',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          description: 'Optional folder name to store the file',
        },
      },
      required: ['file'],
    },
  })
  async uploadPublicFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: FileUploadDto,
  ) {
    try {
      return await this.fileStorageService.uploadPublicFile(file, dto.folder);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload file',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload/private')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a private file' })
  @ApiBody({
    description: 'File upload with optional folder',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          description: 'Optional folder name to store the file',
        },
      },
      required: ['file'],
    },
  })
  async uploadPrivateFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: FileUploadDto,
  ) {
    try {
      return await this.fileStorageService.uploadPrivateFile(file, dto.folder);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to upload file',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('url/:key')
  @ApiOperation({ summary: 'Get a presigned URL for a stored file' })
  @ApiParam({ name: 'key', description: 'File storage key' })
  @ApiQuery({
    name: 'expiresIn',
    description: 'Expiration time in seconds for the URL',
    required: false,
    example: 3600,
  })
  async getSignedUrl(
    @Param('key') key: string,
    @Query('expiresIn') expiresIn: number = 3600,
  ) {
    try {
      return await this.fileStorageService.getPresignedUrl(key, expiresIn);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to generate URL',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
