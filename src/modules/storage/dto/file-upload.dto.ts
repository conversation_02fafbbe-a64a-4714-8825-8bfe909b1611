import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data transfer object for file upload operations
 */
export class FileUploadDto {
  /**
   * Destination folder path within the storage bucket
   * @example "call_recordings" or "profile_images/users"
   */
  @ApiProperty({
    description: 'Destination folder path within storage bucket',
    example: 'call_recordings',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  folder: string;

  /**
   * Optional filename to use (without extension)
   * If not provided, a UUID will be generated
   */
  @ApiProperty({
    description: 'Custom filename (without extension)',
    example: 'user-profile-1',
    required: false,
  })
  @IsString()
  @IsOptional()
  filename?: string;

  /**
   * Optional content type override
   * If not provided, the file's detected MIME type will be used
   */
  @ApiProperty({
    description: 'Override content type',
    example: 'audio/mp3',
    required: false,
  })
  @IsString()
  @IsOptional()
  contentType?: string;
}
