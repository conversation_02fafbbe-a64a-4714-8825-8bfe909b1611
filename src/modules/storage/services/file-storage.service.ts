import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import { AppConfig } from 'src/config/app.config';
import { UploadResult } from '../interfaces/upload-result.interface';

/**
 * Service responsible for managing file storage operations
 * Handles uploads, presigned URLs, and file management for S3
 */
@Injectable()
export class FileStorageService {
  private readonly s3Client: S3Client;
  private readonly logger = new Logger(FileStorageService.name);
  private readonly bucketName: string;
  private readonly bucketBaseUrl: string;

  constructor(private readonly appConfig: AppConfig) {
    // Initialize S3 client with AWS credentials from config
    this.s3Client = new S3Client({
      region: this.appConfig.storage.region,
      credentials: {
        accessKeyId: this.appConfig.storage.accessKeyId,
        secretAccessKey: this.appConfig.storage.secretAccessKey,
      },
    });

    this.bucketName = this.appConfig.storage.bucketName;
    this.bucketBaseUrl = this.appConfig.storage.bucketBaseUrl;
  }

  /**
   * Upload a file with private ACL (requires signed URL to access)
   * @param file File object from multer
   * @param storageFolder The folder path within the bucket
   * @returns Upload result with file key and metadata
   */
  async uploadPrivateFile(
    file: Express.Multer.File,
    storageFolder: string,
  ): Promise<UploadResult> {
    try {
      this.logger.debug(`Uploading private file to folder: ${storageFolder}`);

      const result = await this.uploadFileToS3(file, storageFolder, 'private');

      return {
        ...result,
        url: null, // Private files require getSignedUrl for access
      };
    } catch (error) {
      this.logger.error(
        `Failed to upload private file: ${error.message}`,
        error.stack,
      );
      throw new HttpException(`Failed to upload file: ${error.message}`, 500);
    }
  }

  /**
   * Upload a file with public-read ACL (directly accessible)
   * @param file File object from multer
   * @param storageFolder The folder path within the bucket
   * @returns Upload result with file key and public URL
   */
  async uploadPublicFile(
    file: Express.Multer.File,
    storageFolder: string,
  ): Promise<UploadResult> {
    try {
      this.logger.debug(`Uploading public file to folder: ${storageFolder}`);

      const result = await this.uploadFileToS3(
        file,
        storageFolder,
        'public-read',
      );
      const url = `${this.bucketBaseUrl}/${result.key}`;

      return {
        ...result,
        url,
      };
    } catch (error) {
      this.logger.error(
        `Failed to upload public file: ${error.message}`,
        error.stack,
      );
      throw new HttpException(`Failed to upload file: ${error.message}`, 500);
    }
  }

  /**
   * Generate a pre-signed URL for temporary access to private files
   * @param fileKey The S3 key of the file
   * @param expiresIn Expiration time in seconds (default: 3600 seconds / 1 hour)
   * @returns Object containing the pre-signed URL
   */
  async getPresignedUrl(
    fileKey: string,
    expiresIn: number = 3600,
  ): Promise<{ url: string }> {
    try {
      this.logger.debug(`Generating presigned URL for file: ${fileKey}`);

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: fileKey,
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });

      return { url };
    } catch (error) {
      this.logger.error(
        `Failed to generate presigned URL: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to generate access URL: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Internal helper method to handle S3 uploads
   */
  private async uploadFileToS3(
    file: Express.Multer.File,
    folder: string,
    acl: 'private' | 'public-read',
  ): Promise<{ key: string; contentType: string }> {
    const { mimetype, buffer } = file;
    const extension = this.getFileExtension(mimetype);
    const key = `${folder}/${uuidv4()}.${extension}`;

    const params = {
      Bucket: this.bucketName,
      Key: key,
      Body: buffer,
      ContentType: mimetype,
      ContentDisposition: 'inline',
      ACL: acl,
    };

    await this.s3Client.send(new PutObjectCommand(params));

    return {
      key,
      contentType: mimetype,
    };
  }

  /**
   * Extract file extension from mimetype
   */
  private getFileExtension(mimetype: string): string {
    const parts = mimetype.split('/');
    return parts.length > 1 ? parts[1] : 'bin';
  }
}
