import { forwardRef, Module } from '@nestjs/common';
import { InboundCallbackController } from './inbounds/inbound.callback.controller';
import { OutboundCallbackController } from './outbounds/outbound.callback.controller';
import { InboundCallbackService } from './inbounds/services/inbound.callback.service';
import { OutboundCallbackService } from './outbounds/services/outbound.callback.service';
import { CallLogModule } from '@modules/call-logs/call-log.module';
import { CallbackController } from './callback.controller';
import { MeetAndVisitModule } from '@modules/meet-and-visit/meet-and-visit.module';
import { TaskService } from 'src/common/service/google/task.service';

@Module({
  imports: [forwardRef(() => CallLogModule), MeetAndVisitModule],
  controllers: [
    InboundCallbackController,
    OutboundCallbackController,
    CallbackController,
  ],
  providers: [InboundCallbackService, OutboundCallbackService, TaskService],
  exports: [InboundCallbackService, OutboundCallbackService],
})
export class CallbackModule {}
