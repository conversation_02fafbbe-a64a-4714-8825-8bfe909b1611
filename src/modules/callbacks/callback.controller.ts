import {
  <PERSON>,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Public } from '@modules/auth/decorators/public.decorator';
import { CallHandlerService } from '@modules/call-logs/services/call-handler.service';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { InboundCallbackService } from './inbounds/services/inbound.callback.service';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
@ApiTags('callbacks')
@Controller('callbacks')
export class CallbackController {
  private readonly logger = new Logger(CallbackController.name);

  constructor(
    private readonly callHandlerService: CallHandlerService,
    private readonly callLogService: CallLogService,
    private readonly inboundCallbackService: InboundCallbackService,
  ) {}

  @Public()
  @Post('on-call-received')
  @ApiOperation({
    summary: 'Webhook: Notify when a call is initiated or received',
  })
  async onCallInitiated(@Body() body: any) {
    this.logger.log(
      `[on-call-received] Webhook triggered with payload: ${JSON.stringify(body)}`,
    );
    try {
      this.logger.log(
        `[on-call-received] Processing call via callHandlerService.callRecievedOnServer`,
      );
      const result = await this.callHandlerService.callRecievedOnServer(body);
      this.logger.log(
        `[on-call-received] Call successfully processed with ID: ${result.id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[on-call-received] Failed to process call: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to process call initiation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Public()
  @Post('on-user-answered')
  @ApiOperation({ summary: 'Webhook: Notify when an agent answers a call' })
  async onUserAnswered(@Body() dto: any) {
    this.logger.log(
      `[on-user-answered] Webhook triggered with payload: ${JSON.stringify(dto)}`,
    );
    try {
      this.logger.log(
        `[on-user-answered] Updating agent call attendance for call ID: ${dto.id}`,
      );
      const result = await this.callLogService.updateAgentCallAttendance(dto);
      this.logger.log(
        `[on-user-answered] Agent call attendance successfully updated for call ID: ${result.id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[on-user-answered] Failed to register agent attendance: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to register agent attendance',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Public()
  @Post('on-customer-answered')
  @ApiOperation({
    summary: 'Webhook: Notify when a customer answers an outbound call',
  })
  async onCustomerAnswered(@Body() dto: any) {
    this.logger.log(
      `[on-customer-answered] Webhook triggered with payload: ${JSON.stringify(dto)}`,
    );
    try {
      this.logger.log(
        `[on-customer-answered] Updating customer call attendance for call ID: ${dto.id}`,
      );
      const result =
        await this.callLogService.updateCustomerCallAttendance(dto);
      this.logger.log(
        `[on-customer-answered] Customer call attendance successfully updated for call ID: ${result.id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[on-customer-answered] Failed to register customer attendance: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to register customer attendance',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Public()
  @Post('on-call-hangup')
  @ApiOperation({
    summary: 'Webhook: Notify when a call is terminated or hung up',
  })
  async onCallHangup(@Body() dto: any) {
    this.logger.log(
      `[on-call-hangup] Webhook triggered with payload: ${JSON.stringify(dto)}`,
    );
    try {
      this.logger.log(
        `[on-call-hangup] Processing call termination for call ID: ${dto.id}`,
      );
      const result = await this.callHandlerService.processCallTermination(dto);
      this.logger.log(
        `[on-call-hangup] Call termination successfully processed for call ID: ${result.id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `[on-call-hangup] Failed to process call termination: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to process call termination',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handles scheduled task callbacks from Google Cloud Tasks
   * Processes different types of tasks through InboundCallbackService
   */
  @Public()
  @Post('on-task')
  async onTask(@Body() body: any) {
    try {
      // Process the task through the service
      const result = await this.inboundCallbackService.processTask(body);
      return result;
    } catch (error) {
      console.error('Error processing task callback:', error);
      throw new HttpException(
        error.message || 'Failed to process task callback',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Public()
  @Post('google-meet-recording')
  async handleGoogleMeetRecording(@Body() body: any) {
    this.inboundCallbackService.precessGmeetCouldResponse(body);
  }
}
