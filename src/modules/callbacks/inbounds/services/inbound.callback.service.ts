import { CallAttendedAgentDto } from '@modules/call-logs/dto/agent-call-attended.dto';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { PusherService } from 'nestjs-pusher';
import { TaskType } from '../../enums/task.enum';
import { MeetAndVisitService } from '@modules/meet-and-visit/services/meet-and-visit.service';
import { TaskService } from 'src/common/service/google/task.service';
import { OutboundCallbackService } from '@modules/callbacks/outbounds/services/outbound.callback.service';
import { TaskScheduleDto } from 'src/common/service/google/dto/task-schedule.dto';
import { FetchRecordingTaskDataDto } from '@modules/meet-and-visit/dto/fetch-recording-task.dto';

@Injectable()
export class InboundCallbackService {
  private readonly logger = new Logger(InboundCallbackService.name);
  constructor(
    @Inject(forwardRef(() => CallLogService))
    private readonly callLogService: CallLogService,
    private readonly meetAndVisitService: MeetAndVisitService,
    private readonly taskService: TaskService,
    private readonly outboundCallbackService: OutboundCallbackService,
    private readonly pusherService: PusherService,
  ) {}

  async onWebinarAttended(body: any) {
    try {
      const spocUuid =
        body?.lead_program_interest?.lead?.owner_spoc?.spoc?.uuid;

      if (spocUuid) {
        this.pusherService.trigger(
          `socket_${spocUuid}`, // channel name
          'WEB', // event name
          {
            type: 'WEBINAR_ATTENDED',
            data: body, // the webinar registration details
          },
        );
        this.logger.log(`Notification sent to SPOC with UUID: ${spocUuid}`);
      } else {
        this.logger.error('SPOC UUID not found in the body', body);
      }
    } catch (error) {
      this.logger.error(
        'Error in onWebinarAttended Pusher notification:',
        error,
      );
    }
  }

  /**
   * Process call attendance by agent with optimized data flow
   * @param dto Call attendance information
   * @returns Updated call log
   */
  async processAgentCallAttendance(dto: CallAttendedAgentDto) {
    try {
      const updatedCall =
        await this.callLogService.updateAgentCallAttendance(dto);
      return updatedCall;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Process different types of scheduled tasks
   * @param taskData - The task payload from Google Cloud Tasks
   * @returns Processing result
   */
  async processTask(taskData: TaskScheduleDto): Promise<any> {
    const { task, data } = taskData;

    switch (task) {
      case TaskType.FETCH_GMEET_RECORDING_AND_ATTENDANCE:
        return await this.processFetchGmeetRecordingAndAttendance(data);

      // Add more task types here as needed
      // case TaskType.SEND_EMAIL_NOTIFICATION:
      //   return await this.processSendEmailNotification(data);

      default:
        console.log(`Unknown task type: ${task}`);
        return {
          success: false,
          message: `Unknown task type: ${task}`,
          task: task,
        };
    }
  }

  /**
   * Process Google Meet recording and attendance fetching task
   * @param data - Task data containing meeting information
   * @returns Processing result
   */
  private async processFetchGmeetRecordingAndAttendance(
    data: FetchRecordingTaskDataDto,
  ): Promise<any> {
    const { visitId, userId, event_id, assigned_user_id, retryCount } = data;
    try {
      this.logger.log(
        `Processing Google Meet recording fetch task for visit ID: ${visitId}`,
      );
      // Find the office visit
      const meet = await this.meetAndVisitService.getMeetAndVisitById(visitId);

      if (!meet) {
        this.logger.error(`Office visit with ID ${visitId} not found`);
        return { success: false, message: 'Office visit not found' };
      }
      const eventId = meet.event_id;

      if (!eventId) {
        this.logger.error(
          `Office visit with ID ${visitId} does not have required Google Meet data`,
        );
        return {
          success: false,
          message: 'No Google Meet link or event ID found for this visit',
        };
      }

      // Extract the meeting ID from the Google Meet link if event ID is not available
      if (!eventId) {
        this.logger.error(
          `Office visit with ID ${visitId} does not have required Google Meet data`,
        );
        return {
          success: false,
          message: 'No Google Meet link or event ID found for this visit',
        };
      }

      await this.outboundCallbackService.processFetchGmeetRecordingAndAttendance(
        eventId,
        visitId,
        retryCount,
      );
    } catch (error) {
      this.logger.error(
        `Error processing Google Meet recording fetch task for visit ID ${visitId}:`,
        error,
      );
      return {
        success: false,
        message: `Failed to process Google Meet recording fetch for visit ID ${visitId}`,
        visitId: visitId,
        userId: userId,
        event_id: event_id,
        assigned_user_id: assigned_user_id,
        task: TaskType.FETCH_GMEET_RECORDING_AND_ATTENDANCE,
      };
    }
  }

  /**
   * Process gmeet attendance and recording
   */
  async precessGmeetCouldResponse(data: any) {
    this.logger.log('Processing Google Meet cloud response:', data);
    try {
      if (data && data.attendanceReport?.participants?.length > 0) {
        this.meetAndVisitService.updateGoogleMeetAttendance(data);
      } else {
        if (data.retry_count < 2) {
          this.meetAndVisitService.scheduleRecordingFetchTask(
            data.eventId,
            data.retry_count + 1,
          );
        }
      }
    } catch (e) {
      this.logger.error('Error processing Google Meet cloud response:', e);
      throw new Error('Failed to process Google Meet cloud response');
    }
  }
}

// Need to update cloud function to add retry count and total duration
