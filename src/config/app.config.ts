import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfig {
  constructor(private configService: ConfigService) {}

  get environment() {
    return {
      nodeEnv: this.configService.get<string>('NODE_ENV'),
      port: this.configService.get<number>('PORT'),
      isDevelopment:
        this.configService.get<string>('NODE_ENV') === 'development',
      isProduction: this.configService.get<string>('NODE_ENV') === 'production',
    };
  }

  get database() {
    return {
      host: this.configService.get<string>('DB_HOST'),
      port: this.configService.get<number>('DB_PORT'),
      username: this.configService.get<string>('DB_USERNAME'),
      password: this.configService.get<string>('DB_PASSWORD'),
      database: this.configService.get<string>('DB_DATABASE'),
      synchronize: false, //! please do not set this to true in pre-production if not require
      logging: false,
    };
  }

  get sso() {
    return {
      api: this.configService.get<string>('SSO_API'),
      applicationId: this.configService.get<number>('APPLICATION_ID'),
    };
  }

  get jwt() {
    return {
      secret: this.configService.get<string>('JWT_SECRET'),
      refreshSecret: this.configService.get<string>('JWT_SECRET_REFRESH'),
      candidateSecret: this.configService.get<string>('JWT_SECRET_CANDIDATE'),
    };
  }

  get redis() {
    return {
      host: this.configService.get<string>('REDIS_HOST'),
      port: this.configService.get<number>('REDIS_PORT'),
      password: this.configService.get<string>('REDIS_PASSWORD'),
      sso_redis: this.configService.get<string>('SSO_REDIS'),
    };
  }

  get telephony() {
    return {
      url: this.configService.get<string>('TELIPHONY_URL'),
      apiKey: this.configService.get<string>('TELIPHONY_API_KEY'),
    };
  }

  get storage() {
    return {
      region: this.configService.get<string>('AWS_S3_REGION'),
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      bucketName: this.configService.get<string>('AWS_S3_BUCKET_NAME'),
      bucketBaseUrl: this.configService.get<string>('AWS_S3_BUCKET_BASE_URL'),
    };
  }

  get pusher() {
    return {
      appId: this.configService.get<string>('PUSHER_APP_ID'),
      key: this.configService.get<string>('PUSHER_KEY'),
      secret: this.configService.get<string>('PUSHER_SECRET'),
      cluster: this.configService.get<string>('PUSHER_CLUSTER'),
      useTLS: this.configService.get<boolean>('PUSHER_USE_TLS') ?? true,
    };
  }
}
