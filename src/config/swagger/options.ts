import { DocumentBuilder } from '@nestjs/swagger';
import {
  SWAGGER_API_NAME,
  SWAGGER_API_DESCRIPTION,
  SWAGGER_API_CURRENT_VERSION,
} from './constants';

export const swaggerOptions = new DocumentBuilder()
  .setTitle(SWAGGER_API_NAME)
  .setDescription(SWAGGER_API_DESCRIPTION)
  .setVersion(SWAGGER_API_CURRENT_VERSION)
  .addBearerAuth(
    {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      name: 'JWT',
      description: 'Enter JWT token',
      in: 'header',
    },
    'JWT-auth',
  )
  .build();
