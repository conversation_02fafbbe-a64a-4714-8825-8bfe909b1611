import { IsOptional, IsInt, Min, <PERSON>, IsS<PERSON>, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class PaginationDto {
  @ApiProperty({
    description:
      'Search pattern for filtering by name, email, phone, or candidate ID',
    required: false,
    example: '<PERSON>',
  })
  @IsString()
  @IsOptional()
  pattern?: string;

  @IsOptional()
  @IsIn(['true', 'false']) // optional, to restrict values
  active?: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100) // Limiting to prevent performance issues with very large requests
  size?: number = 10;
}
