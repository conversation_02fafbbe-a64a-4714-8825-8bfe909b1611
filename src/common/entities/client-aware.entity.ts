import { Client } from 'src/modules/clients/entities/client.entity';
import { BaseEntity } from './base.entity';
import { Column, Index, ManyToOne, JoinColumn } from 'typeorm';

//? This class extends the BaseEntity class and adds a client_id column
//? Use this class as a base class for entities that need to be aware of a client

export class ClientAwareEntity extends BaseEntity {
  @Index()
  @Column({ type: 'integer', nullable: false })
  client_id: number;

  @ManyToOne(() => Client, { onDelete: 'NO ACTION' })
  @JoinColumn({ name: 'client_id' })
  client: Client;
}
