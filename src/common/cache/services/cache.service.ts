import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { User } from '../../../modules/users/entities/user.entity';
import { CacheKeys } from '../constants/cache-keys.enum';
import { CacheType } from '../constants/cache-type.enum';

/**
 * Service for managing user-specific cache operations
 */
@Injectable()
export class UserCacheService {
  private readonly logger = new Logger(UserCacheService.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * Clears authentication cache for a user
   */
  async clearUserAuthCache(user: User): Promise<void> {
    try {
      const cacheKey = `${CacheKeys.AUTH_USER}${user.uuid}`;
      await this.cacheManager.del(cacheKey);

      this.logger.log(
        `Successfully cleared authentication cache for user ID: ${user.id}, UUID: ${user.uuid}, Key: ${cacheKey}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to clear authentication cache for user ID: ${user.id}, UUID: ${user.uuid}. Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Clears specific type of cache for a user
   */
  async clearUserTypeCache(cacheType: string, user: User): Promise<void> {
    try {
      const cacheKeyMap = {
        [CacheType.queue]: CacheKeys.USER_QUEUE,
        [CacheType.auth_user]: CacheKeys.AUTH_USER,
      };

      const key = cacheKeyMap[cacheType]
        ? `${cacheKeyMap[cacheType]}${user.uuid}`
        : '';

      if (!key) {
        this.logger.warn(
          `Invalid cache type '${cacheType}' provided for user ID: ${user.id}, UUID: ${user.uuid}`,
        );
        return;
      }

      await this.cacheManager.del(key);

      this.logger.log(
        `Successfully cleared ${cacheType} cache for user ID: ${user.id}, UUID: ${user.uuid}, Key: ${key}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to clear ${cacheType} cache for user ID: ${user.id}, UUID: ${user.uuid}. Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
