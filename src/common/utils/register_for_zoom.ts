import fetch from 'node-fetch'; // or global fetch if available
import { Webinar } from '@modules/webinars/entities/webinar.entity';
import { WebinarRegistration } from '@modules/webinars/entities/webinar-registration.entity';
import getToken, { ZoomAccount } from '@modules/webinars/zoom';

export interface RegisterLeadToZoomWebinarResult {
  id: number;
  join_url: string;
  registrant_id?: string;
  start_time?: string;
  topic?: string;
}

/**
 * Registers a lead to a Zoom webinar and returns registration info.
 * @param webinar Webinar entity containing Zoom info.
 * @param registration WebinarRegistration info containing email.
 * @param lead Lead info containing name details.
 * @returns Promise resolving to Zoom registration details.
 */
export async function registerLeadToZoomWebinar(
  webinar: Webinar,
  registration: WebinarRegistration | { email: string },
  lead: { first_name: string; last_name?: string },
): Promise<RegisterLeadToZoomWebinarResult> {
  // Assume getToken is a helper function to get OAuth token for Zoom API
  const token = await getToken(webinar.zoomAccount || ZoomAccount.ACCOUNT_7);

  const url = `https://api.zoom.us/v2/webinars/${webinar.webinar_id}/registrants`;
  const body = {
    email: registration.email,
    first_name: lead.first_name,
    last_name: lead.last_name || lead.first_name,
  };

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const error = await response.json();
    console.error('Zoom Registration Error:', error);
    throw new Error(
      `Zoom registration failed: ${error.message || response.statusText}`,
    );
  }

  const data = await response.json();

  return {
    id: data.id,
    join_url: data.join_url,
    registrant_id: data.registrant_id,
    start_time: data.start_time,
    topic: data.topic,
  };
}
