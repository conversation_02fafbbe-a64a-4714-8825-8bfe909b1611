import { HttpException, Injectable } from '@nestjs/common';
import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { FilePrivacy } from '@modules/attachments/enums/file-type.enum';
import { file } from 'googleapis/build/src/apis/file';

@Injectable()
export class FileService {
  private readonly s3Client: S3Client;
  //   private readonly getUrl: typeof getSignedUrl;

  constructor(private configService: ConfigService) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_S3_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async uploadFileToAWS(file, folder, privacy: FilePrivacy): Promise<any> {
    const { mimetype, buffer } = file;
    const extension = mimetype.split('/')[1];
    const key = `${folder}/${uuidv4()}.${extension}`;
    const params = {
      Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
      Key: key,
      Body: buffer,
      ContentType: file.mimetype,
      ContentDisposition: 'inline',
    };

    try {
      const data = await this.s3Client.send(
        new PutObjectCommand({
          ...params,
          ACL: privacy,
        }),
      );
      return { data, key };
    } catch (err) {
      throw new Error(`Failed to upload file: ${err.message}`);
    }
  }

  async getPresignedSignedUrlFromAWS(
    file_id: string,
    expiresIn: number = 7200,
  ): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.configService.get('AWS_S3_BUCKET_NAME'),
        Key: file_id,
      });

      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn: expiresIn,
      });
      return url ?? null;
    } catch (error) {
      throw new HttpException(error.message, 401);
    }
  }
}
