import { Injectable, Logger } from '@nestjs/common';
import { CloudTasksClient } from '@google-cloud/tasks';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import { TaskScheduleDto } from './dto/task-schedule.dto';
import { converDateToSeconds } from './utils/convert-date.function';

@Injectable()
export class TaskService {
  private client: CloudTasksClient;

  constructor(private configService: ConfigService) {
    const keyFilePath = 'miles-ai-platform-a587e3c7c7a3.json';
    const credentials = JSON.parse(fs.readFileSync(keyFilePath, 'utf-8'));

    this.client = new CloudTasksClient({
      projectId: credentials.project_id,
      credentials: {
        client_email: credentials.client_email,
        private_key: credentials.private_key,
      },
    });
  }

  async scheduleTask(payloadData: TaskScheduleDto, scheduleTime: any) {
    const project = 'miles-ai-platform';
    const queue = 'mfx';
    const location = 'asia-south1';
    const url = `${this.configService.get<string>('BASE_URL')}callbacks/on-task`;

    const scheduleTimeInUTC = converDateToSeconds(scheduleTime);
    try {
      const [response] = await this.client.createTask({
        parent: this.client.queuePath(project, location, queue),
        task: {
          httpRequest: {
            httpMethod: 'POST',
            url,
            body: Buffer.from(JSON.stringify(payloadData)).toString('base64'),
            headers: {
              'Content-Type': 'application/json',
            },
          },
          scheduleTime: {
            seconds: scheduleTimeInUTC,
          },
        },
      });
    } catch (error) {
      Logger.error('Error scheduling task:', error.message);
    }
  }
}
