import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { google, calendar_v3 } from 'googleapis';
import { ConfigService } from '@nestjs/config';
import { CalendarEventData } from 'src/common/service/google/interfaces/calendar-evet-data.interface';
import { CalendarEventResponse } from 'src/common/service/google/interfaces/calendar-evet-response.interface';
import { GoogleMeetEventData } from 'src/common/service/google/interfaces/google-meet-event-data.inteface';
@Injectable()
export class GoogleCalendarService {
  private readonly logger = new Logger(GoogleCalendarService.name);
  private readonly defaultEmail: string;
  private readonly keyFile: string;
  private readonly scopes = ['https://www.googleapis.com/auth/calendar'];

  constructor(private readonly configService: ConfigService) {
    this.defaultEmail = this.configService.get<string>(
      'GOOGLE_DEFAULT_EMAIL',
      '<EMAIL>',
    );
    this.keyFile = this.configService.get<string>(
      'GOOGLE_SERVICE_ACCOUNT_KEY',
      'miles-ai-platform-a587e3c7c7a3.json',
    );
  }

  private async getCalendarInstance(): Promise<calendar_v3.Calendar> {
    try {
      const auth = new google.auth.GoogleAuth({
        keyFile: this.keyFile,
        scopes: this.scopes,
        clientOptions: {
          subject: this.defaultEmail,
        },
      });

      return google.calendar({ version: 'v3', auth });
    } catch (error) {
      this.logger.error(
        `Failed to authenticate Google Calendar API for ${this.defaultEmail}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to authenticate with Google Calendar',
      );
    }
  }

  private buildEventObject(
    eventData: CalendarEventData,
    includeConference = false,
  ): calendar_v3.Schema$Event {
    const event: calendar_v3.Schema$Event = {
      summary: eventData.summary,
      description: eventData.description,
      location: eventData.location,
      start: {
        dateTime: eventData.startTime,
        timeZone: 'UTC',
      },
      end: {
        dateTime: eventData.endTime,
        timeZone: 'UTC',
      },
      attendees: eventData.attendeesEmails.map((email) => ({ email })),
      reminders: {
        useDefault: false,
        overrides: [
          { method: 'email', minutes: 1440 }, // 1 day before
          { method: 'popup', minutes: 10 }, // 10 minutes before
        ],
      },
    };

    if (includeConference) {
      event.conferenceData = {
        createRequest: {
          requestId: Math.random().toString(36).substring(7),
          conferenceSolutionKey: { type: 'hangoutsMeet' },
        },
      };
    }

    return event;
  }

  private formatResponse(
    eventResponse: calendar_v3.Schema$Event,
  ): CalendarEventResponse {
    return {
      eventId: eventResponse.id,
      meetLink: eventResponse.conferenceData?.entryPoints?.[0]?.uri,
      htmlLink: eventResponse.htmlLink,
      status: eventResponse.status,
    };
  }

  async createGoogleMeetEvent(
    eventData: GoogleMeetEventData,
  ): Promise<CalendarEventResponse> {
    try {
      const calendar = await this.getCalendarInstance();
      const event = this.buildEventObject(eventData, true);

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all',
      });

      this.logger.log(`Google Meet event created: ${response.data.id}`);
      return this.formatResponse(response.data);
    } catch (error) {
      this.logger.error('Failed to create Google Meet event', error.stack);
      throw new InternalServerErrorException(
        'Failed to create Google Meet event',
      );
    }
  }

  async createCalendarEvent(
    eventData: CalendarEventData,
  ): Promise<CalendarEventResponse> {
    try {
      const calendar = await this.getCalendarInstance();
      const event = this.buildEventObject(eventData, false);

      const response = await calendar.events.insert({
        calendarId: 'primary',
        requestBody: event,
        sendUpdates: 'all',
      });

      this.logger.log(`Calendar event created: ${response.data.id}`);
      return this.formatResponse(response.data);
    } catch (error) {
      this.logger.error('Failed to create calendar event', error.stack);
      throw new InternalServerErrorException('Failed to create calendar event');
    }
  }

  async updateEvent(
    eventId: string,
    eventData: CalendarEventData,
    includeConference = false,
  ): Promise<CalendarEventResponse> {
    try {
      const calendar = await this.getCalendarInstance();
      const event = this.buildEventObject(eventData, includeConference);

      const response = await calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: event,
        conferenceDataVersion: includeConference ? 1 : 0,
        sendUpdates: 'all',
      });

      this.logger.log(`Event updated: ${eventId}`);
      return this.formatResponse(response.data);
    } catch (error) {
      this.logger.error(`Failed to update event ${eventId}`, error.stack);
      throw new InternalServerErrorException('Failed to update calendar event');
    }
  }

  async deleteEvent(eventId: string): Promise<void> {
    try {
      const calendar = await this.getCalendarInstance();

      await calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all',
      });

      this.logger.log(`Event deleted: ${eventId}`);
    } catch (error) {
      this.logger.error(`Failed to delete event ${eventId}`, error.stack);
      throw new InternalServerErrorException('Failed to delete calendar event');
    }
  }

  async getEvent(eventId: string): Promise<calendar_v3.Schema$Event> {
    try {
      const calendar = await this.getCalendarInstance();

      const response = await calendar.events.get({
        calendarId: 'primary',
        eventId: eventId,
      });

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get event ${eventId}`, error.stack);
      throw new InternalServerErrorException('Failed to get calendar event');
    }
  }
}
export { CalendarEventData };
