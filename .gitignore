# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*

# Docker volumes
postgres_data*

# Environment files
.env*
!.env.example

# rdb files
*.rdb

# Don't ignore .env.* for the sample project setup
!.env.development
!.env.test
!.env.production
!.env.uat
node_modules
