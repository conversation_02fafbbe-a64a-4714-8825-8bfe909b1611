NODE_ENV=development
PORT=3000

# Database
DB_HOST=db
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=mf-pro

# Auth
JWT_SECRET=oFwAPSvEW5ZHMzBOCRwR1FF8o2hNo1AH
JWT_SECRET_REFRESH=oFwAPSvEW5ZHMzBOCRwR1FF8o2hNo1Ah12
JWT_SECRET_CANDIDATE=oFwAPSvEW5ZHMzBOCRwR1FFTyefyg8o2hNo1AH
SSO_API=https://miles-sso-rg4aetd7ka-el.a.run.app
APPLICATION_ID=3


# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=admin  
********************************


#AWSS3 Bucket
AWS_S3_REGION=ap-south-1
AWS_S3_BUCKET_NAME=milesforce2
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=OuzM14isqK/D1eH7S5FVbVzPEKL8SoMdFP7omE+s
AWS_S3_BUCKET_BASE_URL=https://milesforce2.s3.ap-south-1.amazonaws.com


# Telephony Service
TELIPHONY_URL=https://miles-telephony-rg4aetd7ka-el.a.run.app/calls
TELIPHONY_API_KEY=d77ca097-31ce-42bb-bd86-e4092267ef43

#Pusher Service
PUSHER_APP_ID=*******
PUSHER_KEY=df850f7f1fc7589fa5b1
PUSHER_SECRET=fb9307ea92c0b9cdcc6a
PUSHER_CLUSTER=ap2
PUSHER_USE_TLS=true

# Mongoose
MONGO_URI=*******************************
MONGO_DB=MF-pro

#Base URL
BASE_URL=https://bnzvwdxp-3000.inc1.devtunnels.ms/

#zoom
ZOOM_ACCOUNT_7_ID=zvrNCCaRTca0xHc7r9JEEA
ZOOM_ACCOUNT_7_CLIENT_ID=Kycjh8vBSCibgLkBIYDNA
ZOOM_ACCOUNT_7_CLIENT_SECRET=F5RRr73ZKwIMt5lX76HLlQLGfXEiyEam
ZOOM_ACCOUNT_7_WEBHOOK_TOKEN=NVrKvTBlTfeKE8MpfOmxpA

#MF2.0
MF2_URL=https://mfx-preprod-rg4aetd7ka-el.a.run.app