pipeline {

    agent any

    stages {

        stage('Test') {

            steps {

                sh 'docker rmi -f mf-pro-uat:v1.0.0 || true'

                sh 'docker build -t mf-pro-uat:v1.0.0 -f ./Dockerfile.uat .'

                sh 'docker stop mf-pro-uat || true'

                sh 'docker rm mf-pro-uat || true'

                sh "docker run --restart always --name mf-pro-uat -p 3088:3000 -d -e REDIS_HOST=************ -e REDIS_PORT=6378 -e REDIS_PASSWORD='' -e NODE_ENV=uat mf-pro-uat:v1.0.0"
            }

        }

    }

}