import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity, JoinColumn, ManyToOne, Unique } from 'typeorm';
import { University } from './university.entity';
import { DocType } from './doc-type.entity';

@Entity('uni_req_docs')
@Unique(['university_id', 'doc_type_id'])
export class UniversityRequiredDocs extends BaseEntity {
  @ManyToOne(() => University)
  @JoinColumn({ name: 'university_id' })
  university: University;

  @Column({ type: 'integer', nullable: false })
  university_id: number;

  @ManyToOne(() => DocType)
  @JoinColumn({ name: 'doc_type_id' })
  doc_type: DocType;

  @Column({ type: 'integer', nullable: false })
  doc_type_id: number;

  @Column({ type: 'boolean', default: false })
  is_mandatory: boolean;
}
