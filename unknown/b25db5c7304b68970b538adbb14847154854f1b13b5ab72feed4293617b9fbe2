import { CallLog } from '../entities/call-log.entity';
import { CallStatus, CallType, Tab } from '../enums/call-log.enum';

/**
 * Formats duration from seconds to readable time (H Hour MM minutes SS seconds)
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours} Hr`);
  }

  if (minutes > 0) {
    parts.push(`${minutes} Min`);
  }

  if (remainingSeconds > 0 || parts.length === 0) {
    parts.push(`${remainingSeconds} Sec`);
  }

  return parts.join(' ');
}

/**
 * Formats a name from first and last name components
 */
export function formatFullName(
  firstName: string | null | undefined,
  lastName: string | null | undefined,
): string {
  return `${firstName || ''} ${lastName || ''}`.trim();
}

/**
 * Creates a formatted call data object from a call log entity
 */
export function formatCallLogData(call: CallLog) {
  const candidateId = call.lead?.contact?.candidate_id;
  const leadUUID = call.lead?.lead_uuid;

  // Extract program-wise lead levels
  const programLevels =
    call.lead?.program_interests?.map((interest) => ({
      program_id: interest.program_id,
      program_name: interest.program?.name || null,
      lead_level_id: interest.lead_level_id,
      lead_level_name: interest.lead_level?.name || null,
      lead_level_temperature: interest.lead_level?.temperature || null,
      lead_level_is_positive: interest.lead_level?.is_positive || false,
      lead_level_is_final: interest.lead_level?.is_final || false,
      is_primary: interest.is_primary,
    })) || [];

  return {
    id: call.id,
    candidate_id: candidateId || null,
    lead_uuid: leadUUID || null,
    phone_number: call.phone?.masked_phone_number || null,
    name: call.lead?.contact.full_name || null,
    created_at: call.created_at,
    call_type: call.call_type === CallType.INCOMING ? 'Incoming' : 'Outgoing',
    duration: formatDuration(call.duration || 0),
    call_status: call.call_status === CallStatus.MISSED ? 'Missed' : 'Attended',
    phone_id: call.phone?.id || null,
    lead_id: call.lead?.id || null,
    program_levels: programLevels,
    spoc: call.spoc || null,
  };
}

/**
 * Process call logs with optional grouping by phone number
 */
export function processCallLogs(calls: CallLog[], tabId: Tab) {
  const shouldGroup =
    tabId === Tab.missed_calls || tabId === Tab.ivr_missed_calls;

  if (shouldGroup) {
    // Use a Map to group by phone_id
    const phoneGroups = new Map();

    calls.forEach((call) => {
      const callData = formatCallLogData(call);

      if (callData.phone_id) {
        if (phoneGroups.has(callData.phone_id)) {
          // Increment count for existing phone group
          phoneGroups.get(callData.phone_id).missed_calls_count += 1;
        } else {
          // Create new group for this phone_id
          phoneGroups.set(callData.phone_id, {
            ...callData,
            missed_calls_count: 1,
          });
        }
      } else {
        // Handle calls without phone_id (shouldn't happen but just in case)
        phoneGroups.set(`no-phone-${Date.now()}-${Math.random()}`, {
          ...callData,
          missed_calls_count: 1,
        });
      }
    });

    const groupedCalls = Array.from(phoneGroups.values());
    return { groupedCalls, total: groupedCalls.length };
  } else {
    // No grouping needed
    const groupedCalls = calls.map((call) => formatCallLogData(call));
    return { groupedCalls, total: groupedCalls.length };
  }
}
