/**
 * Converts a datetime string in IST to a UTC Unix timestamp.
 * @param istDateTime Datetime string in IST (e.g., '2024-10-22 15:30:00')
 * @returns Unix timestamp in seconds for the equivalent UTC time
 */
export function converDateToSeconds(istDateTime: string): number {
  const istDate = new Date(`${istDateTime}`);
  const utcDate = new Date(istDate.getTime());
  return Math.floor(utcDate.getTime() / 1000);
}
