/**
 * Creates pagination metadata for API responses
 */
export function createPaginationMeta(
  total: number,
  page: number,
  size: number,
) {
  const totalPages = Math.ceil(total / size);

  return {
    total,
    page,
    size,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}

/**
 * Creates a default empty pagination response
 */
export function createEmptyPaginationResponse(page: number, size: number) {
  return {
    data: [],
    meta: createPaginationMeta(0, page, size),
  };
}
