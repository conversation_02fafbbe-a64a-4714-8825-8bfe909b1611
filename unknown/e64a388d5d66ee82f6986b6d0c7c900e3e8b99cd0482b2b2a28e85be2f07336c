import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

import { CallService } from '../services/call.service';
import { InitiateCallDto } from '../dto/create-call.dto';
import { UpdateCallRecordingDto } from '../dto/update-call-recording.dto';

import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';

import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';

@ApiTags('calls')
@Controller('calls')
export class CallController {
  constructor(private readonly callService: CallService) {}

  @Post('outgoing')
  @ApiOperation({ summary: 'Initiate an outgoing call from agent to customer' })
  @ApiBearerAuth()
  @ApiBody({ type: InitiateCallDto })
  async initiateOutgoingCall(
    @Req() req: AuthenticatedRequest,
    @Body() dto: InitiateCallDto,
  ) {
    try {
      const clientId = req.user.currentClientId;
      return await this.callService.initiateOutgoingCall(
        dto,
        req.user,
        clientId,
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to initiate call',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('recording')
  @ApiOperation({ summary: 'Update call record with a recording audio file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Call recording data and audio file',
    required: true,
    schema: {
      type: 'object',
      properties: {
        call_id: {
          type: 'string',
          description: 'ID of the call',
          example: 'call-12345',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Audio recording file (e.g., mp3, wav)',
        },
      },
      required: ['call_id', 'file'],
    },
  })
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  async updateCallRecording(
    @Body() dto: UpdateCallRecordingDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    try {
      return await this.callService.handleCallRecordingUpdate(dto, file);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update call recording',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
