import { Phone } from '@modules/contacts/entities/phone.entity';
import { Lead } from '../../leads/entities/lead.entity';
import { User } from '../../users/entities/user.entity';
import { Entity, Column, ManyToOne, Index, OneToOne } from 'typeorm';
import { BaseEntity } from 'src/common/entities/base.entity';
import { CallAttendStatus, CallStatus, CallType } from '../enums/call-log.enum';
import { DispositionHistory } from '@modules/dispositions/entities/disposition-history.entity';

@Entity()
export class CallLog extends BaseEntity {
  @Column({ type: 'integer', nullable: true })
  call_id: number;

  @Column({ type: 'integer', nullable: true })
  disposition_history_id: number;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  start_stamp: Date;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  user_answer_stamp: Date;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  customer_answer_stamp: Date;

  @Column({ nullable: true, type: 'timestamp with time zone' })
  end_stamp: Date;

  @Column({ type: 'varchar', nullable: true })
  hangup_cause: string;

  @Column({ nullable: true, type: 'int' })
  user_ring_time: number;

  @Column({ nullable: true, type: 'int' })
  customer_ring_time: number;

  @Index()
  @ManyToOne(() => Lead)
  lead: Lead;

  @Column({ type: 'enum', enum: CallType, nullable: true })
  call_type: CallType;

  @Column({ type: 'enum', enum: CallAttendStatus, nullable: true })
  call_attend_status: CallAttendStatus;

  @Column({ type: 'enum', enum: CallStatus, nullable: true })
  call_status: CallStatus;

  @Index()
  @ManyToOne(() => Phone)
  phone: Phone;

  @Column({ type: 'int', nullable: true })
  duration: number; //seconds

  @Column({ type: 'varchar', length: 255, nullable: true })
  call_recording_url: string;

  @Index()
  @ManyToOne(() => User)
  spoc: User;

  @Column({ type: 'boolean', default: false })
  is_approved: boolean;

  @Index()
  @ManyToOne(() => User)
  approved_by: User;

  @Column({ type: 'boolean', default: false })
  is_IVR: boolean;

  @Column({ type: 'boolean', default: false })
  is_disposed: boolean;

  @OneToOne(
    () => DispositionHistory,
    (dispositionHistory) => dispositionHistory.call_log,
  )
  disposition_history: DispositionHistory;

  @Column({ type: 'boolean', default: false })
  is_multi_vertical_phone: boolean;
}
