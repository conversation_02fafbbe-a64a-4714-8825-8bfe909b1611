import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permission.decorator';
import { RESOURCE_KEY } from '../decorators/resource.decorator';
import { RoleService } from '@modules/roles/role.service';
import { Action } from '@modules/permissions/enums/permission.enum';
import { AuthenticatedRequest } from '../interfaces/request.interface';

@Injectable()
export class PermissionGuard implements CanActivate {
  private readonly logger = new Logger(PermissionGuard.name);

  constructor(
    private reflector: Reflector,
    private rolesService: RoleService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const method = request.method.toLowerCase();
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );

    if (isPublic) {
      this.logger.log('Public route detected, skipping JWT auth');
      return true;
    }

    const resource = this.reflector.get(RESOURCE_KEY, context.getClass());

    if (!resource) {
      this.logger.log(
        `No resource metadata found on controller, allowing access by default`,
      );
      return true;
    }

    const customPermission = this.reflector.get(
      PERMISSIONS_KEY,
      context.getHandler(),
    );

    const action = customPermission?.action || this.getDefaultAction(method);

    const userId = request.user?.id;
    const clientId = request.user?.currentClientId;

    this.logger.log(
      `PermissionGuard: Checking permission for user ${userId} on resource '${resource}' with action '${action}' for client ${clientId}`,
    );

    if (!userId || !clientId) {
      this.logger.warn(
        `PermissionGuard: Access denied due to missing userId or clientId (userId: ${userId}, clientId: ${clientId})`,
      );
      return false;
    }

    const hasPermission = await this.rolesService.userHasPermission(
      userId,
      clientId,
      resource,
      action,
    );

    if (!hasPermission) {
      this.logger.warn(
        `PermissionGuard: User ${userId} does NOT have permission for action '${action}' on resource '${resource}'`,
      );
    } else {
      this.logger.log(
        `PermissionGuard: User ${userId} has permission for action '${action}' on resource '${resource}'`,
      );
    }

    return hasPermission;
  }

  private getDefaultAction(method: string): string {
    const actionMap = {
      get: Action.READ,
      post: Action.CREATE,
      put: Action.UPDATE,
      patch: Action.UPDATE,
      delete: Action.DELETE,
    };
    return actionMap[method] || Action.READ;
  }
}
