import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GeneralLog, MongoLogType } from './schemas/general_log.schema';

@Injectable()
export class LogService {
  constructor(
    @InjectModel('mfpro_general_logs', 'logs')
    private generalLogsModel: Model<GeneralLog>,
  ) {}

  async log(logType: MongoLogType, data: object) {
    const logEntry = new this.generalLogsModel({ logType, ...data });
    await logEntry.save();
  }
}
