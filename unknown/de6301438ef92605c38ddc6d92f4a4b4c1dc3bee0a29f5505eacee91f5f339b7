import { User } from '@modules/users/entities/user.entity';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallLog } from './entities/call-log.entity';
import { CallLogService } from './services/call-log.service';
import { CallLogController } from './controllers/call-log.controller';
import { CallService } from './services/call.service';
import { PhoneResolverService } from './services/phone-resolver.service';
import { LeadResolverService } from './services/lead-resolver.service';
import { CallHandlerService } from './services/call-handler.service';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { Contact } from '@modules/contacts/entities/contact.entity';
import { LeadModule } from '@modules/leads/lead.module';
import { ConfigModule } from '@nestjs/config';
import { AppConfig } from 'src/config/app.config';
import { CallbackModule } from '@modules/callbacks/callback.module';
import { InboundCallbackService } from '@modules/callbacks/inbounds/services/inbound.callback.service';
import { OutboundCallbackService } from '@modules/callbacks/outbounds/services/outbound.callback.service';
import { CacheInvalidationService } from 'src/common/cache/services/cache-invalidation.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { FileStorageService } from '@modules/storage/services/file-storage.service';
import { CallController } from './controllers/call.controller';
import { NotificationModule } from '@modules/notifications/notification.module';
import { ContactModule } from '@modules/contacts/contact.module';
import { MeetAndVisitModule } from '@modules/meet-and-visit/meet-and-visit.module';
import { UserModule } from '@modules/users/user.module';
import { LeadHistoryModule } from '@modules/lead-histories/lead-history.module';
import { CallProcessingService } from './services/call-processing.service';
import { LeadAssignmentService } from '@modules/leads/services/lead-assignment.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([CallLog, User, Phone, Lead, Contact]),
    forwardRef(() => LeadModule),
    ConfigModule,
    forwardRef(() => CallbackModule),
    NotificationModule,
    EventEmitterModule.forRoot(),
    ContactModule,
    MeetAndVisitModule,
    UserModule,
    LeadHistoryModule,
  ],
  controllers: [CallLogController, CallController],
  providers: [
    AppConfig,
    CallService,
    PhoneResolverService,
    LeadResolverService,
    CallHandlerService,
    CallLogService,
    InboundCallbackService,
    OutboundCallbackService,
    CacheInvalidationService,
    FileStorageService,
    CallProcessingService,
    LeadAssignmentService,
  ],
  exports: [
    CallLogService,
    CallService,
    CallHandlerService,
    PhoneResolverService,
    LeadResolverService,
  ],
})
export class CallLogModule {}
