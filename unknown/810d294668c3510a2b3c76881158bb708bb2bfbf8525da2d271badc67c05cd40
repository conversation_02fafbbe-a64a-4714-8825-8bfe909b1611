import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { University } from '../entities/university.entity';
import { CreateUniversityDto } from '../dto/create-university.dto';
import { UpdateUniversityDto } from '../dto/update-university.dto';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';

@Injectable()
export class UniversityService {
  constructor(
    @InjectRepository(University)
    private readonly universityRepository: Repository<University>,
  ) {}

  async create(
    createUniversityDto: CreateUniversityDto,
    clientId: number,
    userId: number,
  ): Promise<University> {
    const university = this.universityRepository.create({
      ...createUniversityDto,
      client_id: clientId,
      created_by: userId,
    });

    return await this.universityRepository.save(university);
  }

  async findAll(clientId: number): Promise<PaginatedResponse<University>> {
    const universities = await this.universityRepository.find({
      where: {
        client_id: clientId,
        status: true,
        is_active: true,
      },
    });
    return {
      data: universities,
      meta: null,
    };
  }

  async findOne(id: number, clientId: number): Promise<University> {
    const university = await this.universityRepository.findOne({
      where: { id, client_id: clientId, status: true, is_active: true },
    });

    if (!university) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }

    return university;
  }

  async update(
    updateUniversityDto: UpdateUniversityDto,
    clientId: number,
    userId: number,
  ): Promise<University> {
    const university = await this.findOne(updateUniversityDto.id, clientId);

    Object.assign(university, {
      ...updateUniversityDto,
      updated_by: userId,
    });

    return await this.universityRepository.save(university);
  }

  async remove(
    id: number,
    userId: number,
    clientId: number,
  ): Promise<University> {
    const university = await this.findOne(id, clientId);
    if (!university) {
      throw new NotFoundException(`University with ID ${id} not found`);
    }
    university.updated_by = userId;
    university.status = false;
    university.is_active = false;
    return this.universityRepository.save(university);
  }
}
