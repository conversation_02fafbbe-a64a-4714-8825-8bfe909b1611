import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GeneralLogSchema } from './schemas/general_log.schema';
import { LogService } from './mongo_log.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [{ name: 'mfpro_general_logs', schema: GeneralLogSchema }],
      'logs',
    ),
  ],
  providers: [LogService],
  exports: [LogService],
})
export class MongoLogModule {}
