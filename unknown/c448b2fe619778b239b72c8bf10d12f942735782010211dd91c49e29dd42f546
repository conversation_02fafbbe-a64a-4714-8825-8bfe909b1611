import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

interface CacheInvalidationPayload {
  userId: number;
  leadId: number;
}

@Injectable()
export class CacheInvalidationService {
  constructor(@InjectRedis() private readonly redis: Redis) {}

  /**
   * Handle lead cache invalidation when call status changes
   */
  @OnEvent('cache.lead.invalidate')
  async handleLeadCacheInvalidation(payload: CacheInvalidationPayload) {
    try {
      const { userId, leadId } = payload;

      if (!userId || !leadId) {
        Logger.warn('Invalid cache invalidation payload');
        return;
      }

      const cacheKey = `user:${userId}:lead:${leadId}`;
      await this.redis.del(cacheKey);

      const netEnquiryCacheKey = `user:${userId}:net-enquiries`;
      await this.redis.del(netEnquiryCacheKey);

      Logger.debug(`Cache invalidated for lead ${leadId} and user ${userId}`);
    } catch (error) {
      Logger.error(`Error invalidating cache: ${error.message}`, error.stack);
    }
  }
}
