import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity } from 'typeorm';

@Entity('attachments')
export class Attachment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  file_id: string;

  @Column({ type: 'varchar', nullable: true })
  url: string;

  @Column({ type: 'boolean', nullable: true })
  is_open: boolean;

  @Column({ type: 'varchar', nullable: true })
  file_name: string;

  @Column({ type: 'varchar', nullable: true })
  file_type: string;

  @Column({ type: 'varchar', nullable: true })
  extension: string;
}
