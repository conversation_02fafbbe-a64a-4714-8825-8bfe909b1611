import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateUniReqDocsDto } from './create-uni-req-docs.dto';
import { IsInt, IsNotEmpty } from 'class-validator';

export class UpdateUniReqDocsDto extends PartialType(CreateUniReqDocsDto) {
  @ApiProperty({
    description: 'ID of the university required document to update',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  id: number;
}
