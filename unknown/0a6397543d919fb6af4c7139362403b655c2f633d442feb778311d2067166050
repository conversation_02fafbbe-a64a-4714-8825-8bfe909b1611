import {
  <PERSON>,
  Get,
  Post,
  Param,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { AttachmentsService } from './attachments.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { FilePrivacy } from './enums/file-type.enum';
import { Public } from '@modules/auth/decorators/public.decorator';

import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('attachments')
@Controller('attachments')
export class AttachmentsController {
  constructor(private readonly attachmentsService: AttachmentsService) {}

  @Public()
  @Post('uploadfile')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a private file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File to upload (private)',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
      },
      required: ['file'],
    },
  })
  uploadFile(@UploadedFile() file: Express.Multer.File) {
    return this.attachmentsService.uploadFile(file, null);
  }

  @Post('upload-openfile')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a public file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'File to upload (public)',
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
      },
      required: ['file'],
    },
  })
  uploadOpenFile(@UploadedFile() file: Express.Multer.File) {
    return this.attachmentsService.uploadFile(file, null, FilePrivacy.PUBLIC);
  }

  @Public()
  @Get('get-url/:attachment_id')
  @ApiOperation({ summary: 'Get presigned URL for an attachment' })
  @ApiParam({
    name: 'attachment_id',
    type: 'number',
    description: 'ID of the attachment to get the URL for',
    required: true,
  })
  getFileUrl(@Param('attachment_id') attachmentId: number) {
    return this.attachmentsService.getPresignedSignedUrl(attachmentId, 7200);
  }
}
