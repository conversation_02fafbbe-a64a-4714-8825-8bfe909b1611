export class CacheKeys {
  static readonly AUTH_USER = 'auth_user_';
  static readonly USER_QUEUE = 'user_queue_';
  static readonly NEW_USER_QUEUE = 'new_user_queue_';
  static readonly USER_QUEUE_MOB = 'user_queue_mob_';
  static readonly USER_TBS = 'user_tabs_';
  static readonly SUBORDINATES = 'subordinates_';
  static readonly ALLOCATION_SPOCS = 'allocation_spocs_';
  static readonly NEXT_ALLOCATION_SPOCS = 'next_allocation_spocs_';
  static readonly SUBORDINATES_INC_INACTIVE_SPOCS =
    'subordinates_inc_inactive_spocs_';
  static USER_PRIMARY_CLIENT = 'user_primary_client_';
}
