import { HttpException, Injectable } from '@nestjs/common';
import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { FilePrivacy } from './enums/file-type.enum';
import { Attachment } from './entities/attachment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FileService } from 'src/common/service/files/file-service';

@Injectable()
export class AttachmentsService {
  private readonly s3Client: S3Client;

  constructor(
    private configService: ConfigService,
    private fileService: FileService,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
  ) {
    this.s3Client = new S3Client({
      region: this.configService.get('AWS_S3_REGION'),
      credentials: {
        accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
      },
    });
  }

  async uploadFile(
    file: Express.Multer.File,
    attachment: Attachment,
    privacy: FilePrivacy = FilePrivacy.PRIVATE,
  ): Promise<Attachment> {
    const result = await this.fileService.uploadFileToAWS(
      file,
      'files',
      privacy,
    );
    const key = result.key;
    if (!attachment) {
      attachment = new Attachment();
    }
    attachment.file_id = key;
    attachment.is_open = false;
    attachment.file_name = file.originalname;
    attachment.file_type = file.mimetype;
    attachment.extension = file.originalname.split('.').pop();
    return this.attachmentRepository.save(attachment);
  }

  async getPresignedSignedUrl(
    attachmentId: number,
    expiresIn: number = 7200,
  ): Promise<{ url: string }> {
    try {
      const attachment = await this.attachmentRepository.findOne({
        where: { id: attachmentId },
      });
      if (!attachment) {
        throw new Error('Attachment not found');
      }

      const url = await this.fileService.getPresignedSignedUrlFromAWS(
        attachment.file_id,
        expiresIn,
      );
      return { url };
    } catch (error) {
      throw new HttpException(error.message, 401);
    }
  }
}
