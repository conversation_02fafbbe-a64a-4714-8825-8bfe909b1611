/**
 * Get start and end dates for today (UTC)
 */
export function getTodayDateRange(): { startDate: Date; endDate: Date } {
  const today = new Date();
  today.setUTCHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return {
    startDate: today,
    endDate: tomorrow,
  };
}

/**
 * Get start and end dates for current month (UTC)
 */
export function getMonthToDateRange(): { startDate: Date; endDate: Date } {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfToday = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    23,
    59,
    59,
  );

  return {
    startDate: startOfMonth,
    endDate: endOfToday,
  };
}

/**
 * Formats a date into an ISO string and ensures UTC consistency
 */
export function formatDateToUTC(date: Date): string {
  return date.toISOString();
}
