import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { DocTypeService } from '../services/doc-type.service';
import { CreateDocTypeDto } from '../dto/create-doc-type.dto';
import { UpdateDocTypeDto } from '../dto/update-doc-type.dto';
import { DocType } from '../entities/doc-type.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { CreateUploadedFilesDto } from '../dto/create-uploaded-files.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('doc-types')
@ApiBearerAuth()
@Controller('doc-types')
export class DocTypeController {
  constructor(private readonly docTypeService: DocTypeService) {}

  @Post('add')
  @ApiOperation({ summary: 'Create a new document type' })
  @ApiResponse({
    status: 201,
    description: 'Document type created successfully',
    type: DocType,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  create(
    @Body() createDocTypeDto: CreateDocTypeDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.docTypeService.create(createDocTypeDto, req.user?.id);
  }

  @Get('get-all')
  @ApiOperation({
    summary: 'Get all document types with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Document types retrieved successfully',
  })
  findAll(
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<DocType>> {
    return this.docTypeService.findAll();
  }

  @Post('upload_doc')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Get all document types with filtering and pagination',
  })
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
  })
  uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: CreateUploadedFilesDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.docTypeService.createUploadDocument(
      file,
      dto,
      req.user?.currentClientId,
      req.user?.id,
    );
  }

  @Get('get-lead-docs/:leadId')
  @ApiOperation({
    summary: 'Get all document types with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Document types retrieved successfully',
  })
  @ApiParam({ name: 'leadId', type: 'number', description: 'Lead ID' })
  findLeadDocs(
    @Param('leadId', ParseIntPipe) leadId: number,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<any>> {
    return this.docTypeService.getAllUploadedDocumets(
      leadId,
      req.user?.currentClientId,
    );
  }

  @Get('remove-uploaded-doc/:id')
  @ApiOperation({ summary: 'Remove uploaded document by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Uploaded document ID' })
  @ApiResponse({
    status: 200,
    description: 'Uploaded document deleted successfully',
  })
  removeUploadedDocument(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    return this.docTypeService.removeUploadedDocument(id, userId);
  }

  @Get('get-one/:id')
  @ApiOperation({ summary: 'Get document type by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Document type ID' })
  @ApiResponse({
    status: 200,
    description: 'Document type retrieved successfully',
    type: DocType,
  })
  @ApiResponse({ status: 404, description: 'Document type not found' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.docTypeService.findOne(id);
  }

  @Post('update')
  @ApiOperation({ summary: 'Update document type by ID' })
  @ApiResponse({
    status: 200,
    description: 'Document type updated successfully',
    type: DocType,
  })
  @ApiResponse({ status: 404, description: 'Document type not found' })
  update(
    @Body() updateDocTypeDto: UpdateDocTypeDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    return this.docTypeService.update(updateDocTypeDto, userId);
  }

  @Get('delete/:id')
  @ApiOperation({ summary: 'Delete document type by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Document type ID' })
  @ApiResponse({
    status: 200,
    description: 'Document type deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Document type not found' })
  remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    return this.docTypeService.remove(id, userId);
  }
}
