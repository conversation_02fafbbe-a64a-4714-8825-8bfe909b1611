import { IsInt, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateUploadedFilesDto {
  @ApiProperty({
    description: 'Lead ID',
    example: 1,
  })
  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  lead_id: number;

  @ApiProperty({
    description: 'Contact ID',
    example: 1,
  })
  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  contact_id: number;

  @ApiProperty({
    description: 'Document type ID',
    example: 1,
  })
  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  doc_type_id: number;
}

// export class DocumentsDto {
//   @ApiProperty({
//     description: 'Attachment ID',
//     example: 1,
//   })
//   @IsInt()
//   @IsNotEmpty()
//   attachment_id: number;
// }
