import { IsInt, <PERSON>NotEmpty, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUniReqDocsDto {
  @ApiProperty({
    description: 'University ID',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  university_id: number;

  @ApiProperty({
    description: 'Document type ID',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  doc_type_id: number;

  @ApiPropertyOptional({
    description: 'Whether this document is mandatory for the university',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_mandatory?: boolean;
}
