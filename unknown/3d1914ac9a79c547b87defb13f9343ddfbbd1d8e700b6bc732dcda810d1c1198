import { Module } from '@nestjs/common';
import { Auth<PERSON><PERSON>roller } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtStrategy } from './strategies/jwt.strategy';
import { UserModule } from 'src/modules/users/user.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RefreshJwtStrategy } from './strategies/refreshToken.stratergy';
import { CandidateJwtStrategy } from './strategies/candidate-jwt.strategy';
import { AppConfig } from 'src/config/app.config';
import { PermissionGuard } from './guards/permission.guard';
import { RoleModule } from '@modules/roles/role.module';
import { ClientAccessGuard } from '@modules/user-clients/guards/client-access.guard';
import { UserClientModule } from '@modules/user-clients/user-client.module';
import { CacheModule } from 'src/common/cache/cache.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserCity } from '@modules/lead-allocations/entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserCity]),
    ConfigModule,
    PassportModule,
    UserModule,
    RoleModule,
    UserClientModule,
    CacheModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        global: true,
        signOptions: { expiresIn: '1d' },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    PermissionGuard,
    ClientAccessGuard,
    AppConfig,
    LocalStrategy,
    JwtStrategy,
    RefreshJwtStrategy,
    CandidateJwtStrategy,
  ],
})
export class AuthModule {}
