import { ApiPropertyOptional } from '@nestjs/swagger';
import { BaseDto } from './base.dto';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class ClientAwareDto extends BaseDto {
  @ApiPropertyOptional({
    description: 'Client ID (optional)',
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  client_id?: number;

  @IsBoolean()
  @IsOptional()
  is_active: boolean;
}
