import { ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.get<boolean>(
      'isPublic',
      context.getHandler(),
    );

    if (isPublic) {
      this.logger.log('Public route detected, skipping JWT auth');
      return true;
    }

    this.logger.log('JWT auth required, proceeding with validation');

    try {
      const result = super.canActivate(context);
      if (result instanceof Promise) {
        return result.catch((error) => {
          this.logger.warn(`JWT auth failed: ${error.message}`);
          throw error;
        });
      }
      return result;
    } catch (error) {
      this.logger.warn(`JWT auth failed: ${error.message}`);
      throw error;
    }
  }
}
