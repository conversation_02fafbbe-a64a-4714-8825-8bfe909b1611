import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import { CallLogService } from '../services/call-log.service';
import { CallLogFilterDto } from '../dto/call-log-filter.dto';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource } from '@modules/permissions/enums/permission.enum';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';

@Controller('call-logs')
@UseGuards(PermissionGuard)
@ApiResource(Resource.CALL_LOG)
export class CallLogController {
  constructor(private readonly callLogService: CallLogService) {}

  @Get()
  getFilteredCallLogs(
    @Query() filterDto: CallLogFilterDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.callLogService.getFilteredCallLogs(filterDto, req.user);
  }

  @Get('tab-counts')
  getTabCounts(
    @Query() filterDto: Partial<CallLogFilterDto>,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.callLogService.getTabCounts(filterDto, req.user);
  }

  @Get('non-disposed/:userId')
  getNonDisposedCallLogs(@Param('userId') userId: number) {
    return this.callLogService.getNonDisposedCallLogs(userId);
  }
}
