import { DataSource, DataSourceOptions } from 'typeorm';
import { AppConfig } from '../config/app.config';

/**
 * Determines if database logging should be enabled
 * @returns boolean indicating if logging should be enabled
 */
const shouldEnableLogging = (): boolean => {
  const env = process.env.NODE_ENV?.toLowerCase() || 'development';
  return env === 'development';
};

export function getDataSourceOptions(config: AppConfig): DataSourceOptions {
  return {
    type: 'postgres',
    host: config.database.host,
    port: config.database.port,
    username: config.database.username,
    password: config.database.password,
    database: config.database.database,
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    migrations: ['./src/db/migrations/*{.ts,.js}'],
    synchronize: config.database.synchronize,
    logging: config.database.logging,
  };
}

// For CLI commands and direct database access
const getDefaultOptions = (): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'db',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'postgres',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: ['./src/db/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: shouldEnableLogging(),
});

const dataSource = new DataSource(getDefaultOptions());
export default dataSource;
