import { IsNumber, IsString, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for initiating a call (used in the 'initiate' endpoint)
 */
export class InitiateCallDto {
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  phone_id?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  lead_id?: number;

  @IsOptional()
  @IsString()
  phone_number?: string;

  @IsOptional()
  @IsString()
  country_code?: string;
}
