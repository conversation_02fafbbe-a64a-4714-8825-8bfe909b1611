FROM node:20-alpine as base
ENV STAGE=uat

WORKDIR /app

# Copy package.json and install dependencies
COPY package.json package-lock.json* ./
RUN npm install

# Install nestjs cli globally (optional, if needed)
RUN npm install -g @nestjs/cli

# Copy rest of the source code
COPY . .

# Copy your .env.uat as .env inside container so config module can read it by default
COPY .env.uat .env

# Remove unwanted folders if any
RUN rm -rf /app/next_app

# Build the project
RUN npm run build

# Expose port (optional, but good practice)
EXPOSE 3000

# Start the app using your script
CMD ["npm", "run", "start:uat"]
