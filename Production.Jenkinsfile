pipeline {
    agent any

    environment {
        PROJECT_ID = 'milesforce-pro'
        REGION = 'asia-south1'
        REPO_NAME = 'miles-mf-pro-prod'
        IMAGE_NAME = 'mf-nest-app'
        TAG = 'v1.0.0'
        ARTIFACT_REGISTRY = "asia-south1-docker.pkg.dev/${PROJECT_ID}/${REPO_NAME}/${IMAGE_NAME}"
    }

    stages {
        stage('Checkout Code') {
            steps {
                checkout scm
            }
        }

        stage('Auth to GCP') {
            steps {
                withCredentials([file(credentialsId: 'milesforce-pro-mf-pro-jenkins', variable: 'GCLOUD_KEY')]) {
                    sh '''
                        gcloud auth activate-service-account --key-file=$GCLOUD_KEY
                        gcloud config set project $PROJECT_ID
                        gcloud auth configure-docker $REGION-docker.pkg.dev --quiet
                    '''
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                sh '''
                    docker build -t $ARTIFACT_REGISTRY:$TAG -f Dockerfile.uat .
                '''
            }
        }

        stage('Push Docker Image') {
            steps {
                sh '''
                    docker push $ARTIFACT_REGISTRY:$TAG
                '''
            }
        }
    }
}
