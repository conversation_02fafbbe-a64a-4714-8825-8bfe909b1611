{"name": "mf-pro", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "STAGE=development nest start", "dev": "sudo STAGE=development docker compose -f ./deployment/docker-compose.yaml -f ./deployment/docker-compose.dev.yaml up --build --force-recreate", "start:development": "STAGE=development nest start --watch", "start:debug": "nest start --debug --watch", "start:uat": "STAGE=uat node dist/main", "start:prod": "STAGE=prod node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:generate-migration": "npm run typeorm -- migration:generate -d src/config/typeorm.config.ts", "typeorm:run-migrations": "npm run typeorm -- migration:run -d src/config/typeorm.config.ts", "typeorm:revert-migration": "npm run typeorm -- migration:revert -d src/config/typeorm.config.ts", "docker:dev": "docker-compose -f deployment/docker-compose.dev.yml up", "docker:dev:build": "docker-compose -f deployment/docker-compose.dev.yml up --build", "docker:uat": "docker-compose -f deployment/docker-compose.uat.yml up", "docker:uat:build": "docker-compose -f deployment/docker-compose.uat.yml up --build", "docker:prod": "docker-compose -f deployment/docker-compose.prod.yml up -d", "docker:prod:build": "docker-compose -f deployment/docker-compose.prod.yml up -d --build"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@google-cloud/tasks": "^6.1.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.4.18", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^11.0.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.9.0", "cache-manager": "^5.7.3", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns-tz": "^1.3.8", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "googleapis": "^149.0.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "libphonenumber-js": "^1.12.8", "mongoose": "^8.15.1", "nestjs-pusher": "^2.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "pusher": "^5.2.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.24"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cache-manager-redis-store": "^2.0.4", "@types/express": "^5.0.2", "@types/jest": "^29.5.2", "@types/long": "^4.0.2", "@types/multer": "^1.4.12", "@types/node": "^20.17.52", "@types/passport": "^1.0.17", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}