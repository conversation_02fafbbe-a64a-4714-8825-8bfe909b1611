NODE_ENV=
PORT=

# Database
DB_HOST=
DB_PORT=
DB_USERNAME=
DB_PASSWORD=
DB_DATABASE=

# Auth
JWT_SECREToFwAPSvEW5ZHMzBOCRwR1FF8o2hNo1AH=
JWT_SECRET_REFRESH=
JWT_SECRET_CANDIDATE=
SSO_API=
APPLICATION_ID=


# Redis
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
SSO_REDIS=


#AWSS3 Bucket
AWS_S3_REGION=
AWS_S3_BUCKET_NAME=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET_BASE_URL=


# Telephony Service
TELIPHONY_URL=
TELIPHONY_API_KEY=

#Pusher Service
PUSHER_APP_ID=
PUSHER_KEY=
PUSHER_SECRET=
PUSHER_CLUSTER=
PUSHER_USE_TLS=

# Mongoose
MONGO_URI=
MONGO_DB=

#Base URL
BASE_URL=

#zoom
ZOOM_ACCOUNT_7_ID=
ZOOM_ACCOUNT_7_CLIENT_ID=
ZOOM_ACCOUNT_7_CLIENT_SECRET=
ZOOM_ACCOUNT_7_WEBHOOK_TOKEN=